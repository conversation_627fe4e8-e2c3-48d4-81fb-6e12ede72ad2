{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\api\\tool\\gen.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\api\\tool\\gen.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listTable", "query", "request", "url", "method", "params", "listDbTable", "getGenTable", "tableId", "updateGenTable", "data", "importTable", "createTable", "previewTable", "delTable", "genCode", "tableName", "synchDb"], "sources": ["D:/jgst/dataeyeui/src/api/tool/gen.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询生成表数据\nexport function listTable(query) {\n  return request({\n    url: '/tool/gen/list',\n    method: 'get',\n    params: query\n  })\n}\n// 查询db数据库列表\nexport function listDbTable(query) {\n  return request({\n    url: '/tool/gen/db/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询表详细信息\nexport function getGenTable(tableId) {\n  return request({\n    url: '/tool/gen/' + tableId,\n    method: 'get'\n  })\n}\n\n// 修改代码生成信息\nexport function updateGenTable(data) {\n  return request({\n    url: '/tool/gen',\n    method: 'put',\n    data: data\n  })\n}\n\n// 导入表\nexport function importTable(data) {\n  return request({\n    url: '/tool/gen/importTable',\n    method: 'post',\n    params: data\n  })\n}\n\n// 创建表\nexport function createTable(data) {\n  return request({\n    url: '/tool/gen/createTable',\n    method: 'post',\n    params: data\n  })\n}\n\n// 预览生成代码\nexport function previewTable(tableId) {\n  return request({\n    url: '/tool/gen/preview/' + tableId,\n    method: 'get'\n  })\n}\n\n// 删除表数据\nexport function delTable(tableId) {\n  return request({\n    url: '/tool/gen/' + tableId,\n    method: 'delete'\n  })\n}\n\n// 生成代码（自定义路径）\nexport function genCode(tableName) {\n  return request({\n    url: '/tool/gen/genCode/' + tableName,\n    method: 'get'\n  })\n}\n\n// 同步数据库\nexport function synchDb(tableName) {\n  return request({\n    url: '/tool/gen/synchDb/' + tableName,\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASK,WAAWA,CAACL,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACF,IAAI,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,YAAYA,CAACL,OAAO,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGK,OAAO;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,QAAQA,CAACN,OAAO,EAAE;EAChC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,OAAOA,CAACC,SAAS,EAAE;EACjC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGa,SAAS;IACrCZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,OAAOA,CAACD,SAAS,EAAE;EACjC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGa,SAAS;IACrCZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}