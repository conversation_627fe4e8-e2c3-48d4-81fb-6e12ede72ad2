{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\code.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\code.js", "mtime": 1749172157573}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "_break", "_cursor", "_inline", "_text", "_interopRequireWildcard", "_container", "_quill", "CodeBlockContainer", "exports", "_Container", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "code", "index", "length", "children", "map", "child", "domNode", "innerText", "join", "slice", "html", "concat", "escapeText", "create", "_superPropGet2", "setAttribute", "Container", "CodeBlock", "_Block", "register", "<PERSON><PERSON><PERSON>", "Block", "_defineProperty2", "Code", "_Inline", "Inline", "blotName", "tagName", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextBlot", "Break", "<PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>"], "sources": ["../../src/formats/code.ts"], "sourcesContent": ["import Block from '../blots/block.js';\nimport Break from '../blots/break.js';\nimport Cursor from '../blots/cursor.js';\nimport Inline from '../blots/inline.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport Container from '../blots/container.js';\nimport Quill from '../core/quill.js';\n\nclass CodeBlockContainer extends Container {\n  static create(value: string) {\n    const domNode = super.create(value) as Element;\n    domNode.setAttribute('spellcheck', 'false');\n    return domNode;\n  }\n\n  code(index: number, length: number) {\n    return (\n      this.children\n        // @ts-expect-error\n        .map((child) => (child.length() <= 1 ? '' : child.domNode.innerText))\n        .join('\\n')\n        .slice(index, index + length)\n    );\n  }\n\n  html(index: number, length: number) {\n    // `\\n`s are needed in order to support empty lines at the beginning and the end.\n    // https://html.spec.whatwg.org/multipage/syntax.html#element-restrictions\n    return `<pre>\\n${escapeText(this.code(index, length))}\\n</pre>`;\n  }\n}\n\nclass CodeBlock extends Block {\n  static TAB = '  ';\n\n  static register() {\n    Quill.register(CodeBlockContainer);\n  }\n}\n\nclass Code extends Inline {}\nCode.blotName = 'code';\nCode.tagName = 'CODE';\n\nCodeBlock.blotName = 'code-block';\nCodeBlock.className = 'ql-code-block';\nCodeBlock.tagName = 'DIV';\nCodeBlockContainer.blotName = 'code-block-container';\nCodeBlockContainer.className = 'ql-code-block-container';\nCodeBlockContainer.tagName = 'DIV';\n\nCodeBlockContainer.allowedChildren = [CodeBlock];\n\nCodeBlock.allowedChildren = [TextBlot, Break, Cursor];\nCodeBlock.requiredContainer = CodeBlockContainer;\n\nexport { Code, CodeBlockContainer, CodeBlock as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAC,uBAAA,CAAAL,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,MAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAoC,IAE9BQ,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,0BAAAE,UAAA;EAAA,SAAAF,mBAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,kBAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,kBAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,kBAAA,EAAAE,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,kBAAA;IAAAS,GAAA;IAAAC,KAAA,EAOtB,SAAAC,IAAIA,CAACC,KAAa,EAAEC,MAAc,EAAE;MAClC,OACE,IAAI,CAACC;MACH;MAAA,CACCC,GAAG,CAAE,UAAAC,KAAK;QAAA,OAAMA,KAAK,CAACH,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAGG,KAAK,CAACC,OAAO,CAACC,SAAU;MAAA,EAAC,CACpEC,IAAI,CAAC,IAAI,CAAC,CACVC,KAAK,CAACR,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC;IAEnC;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAEA,SAAAW,IAAIA,CAACT,KAAa,EAAEC,MAAc,EAAE;MAClC;MACA;MACA,iBAAAS,MAAA,CAAiB,IAAAC,gBAAU,EAAC,IAAI,CAACZ,IAAI,CAACC,KAAK,EAAEC,MAAM,CAAC,CAAE;IACxD;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EApBA,SAAOc,MAAMA,CAACd,KAAa,EAAE;MAC3B,IAAMO,OAAO,OAAAQ,cAAA,CAAArB,OAAA,EAAAJ,kBAAA,sBAAgBU,KAAK,EAAY;MAC9CO,OAAO,CAACS,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;MAC3C,OAAOT,OAAO;IAChB;EAAA;AAAA,EAL+BU,kBAAS;AAAA,IAwBpCC,SAAS,GAAA3B,OAAA,CAAAG,OAAA,0BAAAyB,MAAA;EAAA,SAAAD,UAAA;IAAA,IAAAzB,gBAAA,CAAAC,OAAA,QAAAwB,SAAA;IAAA,WAAAvB,WAAA,CAAAD,OAAA,QAAAwB,SAAA,EAAAtB,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAwB,SAAA,EAAAC,MAAA;EAAA,WAAArB,aAAA,CAAAJ,OAAA,EAAAwB,SAAA;IAAAnB,GAAA;IAAAC,KAAA,EAGb,SAAOoB,QAAQA,CAAA,EAAG;MAChBC,cAAK,CAACD,QAAQ,CAAC9B,kBAAkB,CAAC;IACpC;EAAA;AAAA,EALsBgC,cAAK;AAAA,IAAAC,gBAAA,CAAA7B,OAAA,EAAvBwB,SAAS,SACA,IAAI;AAAA,IAObM,IAAI,GAAAjC,OAAA,CAAAiC,IAAA,0BAAAC,OAAA;EAAA,SAAAD,KAAA;IAAA,IAAA/B,gBAAA,CAAAC,OAAA,QAAA8B,IAAA;IAAA,WAAA7B,WAAA,CAAAD,OAAA,QAAA8B,IAAA,EAAA5B,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAA8B,IAAA,EAAAC,OAAA;EAAA,WAAA3B,aAAA,CAAAJ,OAAA,EAAA8B,IAAA;AAAA,EAASE,eAAM;AACzBF,IAAI,CAACG,QAAQ,GAAG,MAAM;AACtBH,IAAI,CAACI,OAAO,GAAG,MAAM;AAErBV,SAAS,CAACS,QAAQ,GAAG,YAAY;AACjCT,SAAS,CAACW,SAAS,GAAG,eAAe;AACrCX,SAAS,CAACU,OAAO,GAAG,KAAK;AACzBtC,kBAAkB,CAACqC,QAAQ,GAAG,sBAAsB;AACpDrC,kBAAkB,CAACuC,SAAS,GAAG,yBAAyB;AACxDvC,kBAAkB,CAACsC,OAAO,GAAG,KAAK;AAElCtC,kBAAkB,CAACwC,eAAe,GAAG,CAACZ,SAAS,CAAC;AAEhDA,SAAS,CAACY,eAAe,GAAG,CAACC,aAAQ,EAAEC,cAAK,EAAEC,eAAM,CAAC;AACrDf,SAAS,CAACgB,iBAAiB,GAAG5C,kBAAkB", "ignoreList": []}]}