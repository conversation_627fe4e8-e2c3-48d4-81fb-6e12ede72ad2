{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\Editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\Editor\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill", "_interopRequireDefault", "require", "_auth", "name", "props", "value", "type", "String", "default", "height", "Number", "minHeight", "readOnly", "Boolean", "fileSize", "data", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "<PERSON><PERSON><PERSON>", "currentValue", "options", "theme", "bounds", "document", "body", "debug", "modules", "toolbar", "list", "indent", "size", "header", "color", "background", "align", "placeholder", "computed", "styles", "style", "concat", "watch", "handler", "val", "clipboard", "dangerouslyPasteHTML", "immediate", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this", "editor", "$refs", "getModule", "add<PERSON><PERSON><PERSON>", "upload", "$children", "input", "click", "quill", "format", "on", "delta", "<PERSON><PERSON><PERSON><PERSON>", "source", "html", "children", "innerHTML", "text", "getText", "$emit", "range", "oldRange", "eventName", "_len", "arguments", "length", "args", "Array", "_key", "apply", "handleBeforeUpload", "file", "isJPG", "includes", "$message", "error", "isLt", "handleUploadSuccess", "res", "code", "getSelection", "index", "insertEmbed", "fileName", "setSelection", "handleUploadError"], "sources": ["src/components/Editor/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n      v-if=\"this.type == 'url'\"\n    >\n    </el-upload>\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\n  </div>\n</template>\n\n<script>\nimport Quill from \"quill\";\nimport \"quill/dist/quill.core.css\";\nimport \"quill/dist/quill.snow.css\";\nimport \"quill/dist/quill.bubble.css\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"Editor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 高度 */\n    height: {\n      type: Number,\n      default: null,\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: null,\n    },\n    /* 只读 */\n    readOnly: {\n      type: Boolean,\n      default: false,\n    },\n    /* 上传文件大小限制(MB) */\n    fileSize: {\n      type: Number,\n      default: 5,\n    },\n    /* 类型（base64格式、url格式） */\n    type: {\n      type: String,\n      default: \"url\",\n    }\n  },\n  data() {\n    return {\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      Quill: null,\n      currentValue: \"\",\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [{ align: [] }],                                 // 对齐方式\n            [\"clean\"],                                       // 清除文本格式\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\n          ],\n        },\n        placeholder: \"请输入内容\",\n        readOnly: this.readOnly,\n      },\n    };\n  },\n  computed: {\n    styles() {\n      let style = {};\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`;\n      }\n      if (this.height) {\n        style.height = `${this.height}px`;\n      }\n      return style;\n    },\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val;\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init();\n  },\n  beforeDestroy() {\n    this.Quill = null;\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor;\n      this.Quill = new Quill(editor, this.options);\n      // 如果设置了上传地址则自定义图片上传事件\n      if (this.type == 'url') {\n        let toolbar = this.Quill.getModule(\"toolbar\");\n        toolbar.addHandler(\"image\", (value) => {\n          if (value) {\n            this.$refs.upload.$children[0].$refs.input.click();\n          } else {\n            this.quill.format(\"image\", false);\n          }\n        });\n      }\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML;\n        const text = this.Quill.getText();\n        const quill = this.Quill;\n        this.currentValue = html;\n        this.$emit(\"input\", html);\n        this.$emit(\"on-change\", { html, text, quill });\n      });\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\n      });\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        this.$emit(\"on-selection-change\", range, oldRange, source);\n      });\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\n        this.$emit(\"on-editor-change\", eventName, ...args);\n      });\n    },\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"];\n      const isJPG = type.includes(file.type);\n      // 检验文件格式\n      if (!isJPG) {\n        this.$message.error(`图片格式错误!`);\n        return false;\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\n        if (!isLt) {\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\n          return false;\n        }\n      }\n      return true;\n    },\n    handleUploadSuccess(res, file) {\n      // 如果上传成功\n      if (res.code == 200) {\n        // 获取富文本组件实例\n        let quill = this.Quill;\n        // 获取光标所在位置\n        let length = quill.getSelection().index;\n        // 插入图片  res.url为服务器返回的图片地址\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName);\n        // 调整光标到最后\n        quill.setSelection(length + 1);\n      } else {\n        this.$message.error(\"图片插入失败\");\n      }\n    },\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\");\n    },\n  },\n};\n</script>\n\n<style>\n.editor, .ql-toolbar {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n.quill-img {\n  display: none;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>\n"], "mappings": ";;;;;;;;;;AAoBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAM,QAAA;MACAR,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACAC,OAAA;QACAC,KAAA;QACAC,MAAA,EAAAC,QAAA,CAAAC,IAAA;QACAC,KAAA;QACAC,OAAA;UACA;UACAC,OAAA,GACA;UAAA;UACA;UAAA;UACA;YAAAC,IAAA;UAAA;YAAAA,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;YAAAA,MAAA;UAAA;UAAA;UACA;YAAAC,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;YAAAC,UAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;UAAA;UACA;UAAA;UACA;UAAA;QAEA;QACAC,WAAA;QACA5B,QAAA,OAAAA;MACA;IACA;EACA;EACA6B,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,IAAAC,KAAA;MACA,SAAAhC,SAAA;QACAgC,KAAA,CAAAhC,SAAA,MAAAiC,MAAA,MAAAjC,SAAA;MACA;MACA,SAAAF,MAAA;QACAkC,KAAA,CAAAlC,MAAA,MAAAmC,MAAA,MAAAnC,MAAA;MACA;MACA,OAAAkC,KAAA;IACA;EACA;EACAE,KAAA;IACAxC,KAAA;MACAyC,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA,UAAAvB,YAAA;UACA,KAAAA,YAAA,GAAAuB,GAAA,iBAAAA,GAAA;UACA,SAAAxB,KAAA;YACA,KAAAA,KAAA,CAAAyB,SAAA,CAAAC,oBAAA,MAAAzB,YAAA;UACA;QACA;MACA;MACA0B,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAA9B,KAAA;EACA;EACA+B,OAAA;IACAF,IAAA,WAAAA,KAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,MAAA,QAAAC,KAAA,CAAAD,MAAA;MACA,KAAAjC,KAAA,OAAAA,cAAA,CAAAiC,MAAA,OAAA/B,OAAA;MACA;MACA,SAAAnB,IAAA;QACA,IAAA0B,OAAA,QAAAT,KAAA,CAAAmC,SAAA;QACA1B,OAAA,CAAA2B,UAAA,oBAAAtD,KAAA;UACA,IAAAA,KAAA;YACAkD,KAAA,CAAAE,KAAA,CAAAG,MAAA,CAAAC,SAAA,IAAAJ,KAAA,CAAAK,KAAA,CAAAC,KAAA;UACA;YACAR,KAAA,CAAAS,KAAA,CAAAC,MAAA;UACA;QACA;MACA;MACA,KAAA1C,KAAA,CAAAyB,SAAA,CAAAC,oBAAA,MAAAzB,YAAA;MACA,KAAAD,KAAA,CAAA2C,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACA,IAAAC,IAAA,GAAAf,KAAA,CAAAE,KAAA,CAAAD,MAAA,CAAAe,QAAA,IAAAC,SAAA;QACA,IAAAC,IAAA,GAAAlB,KAAA,CAAAhC,KAAA,CAAAmD,OAAA;QACA,IAAAV,KAAA,GAAAT,KAAA,CAAAhC,KAAA;QACAgC,KAAA,CAAA/B,YAAA,GAAA8C,IAAA;QACAf,KAAA,CAAAoB,KAAA,UAAAL,IAAA;QACAf,KAAA,CAAAoB,KAAA;UAAAL,IAAA,EAAAA,IAAA;UAAAG,IAAA,EAAAA,IAAA;UAAAT,KAAA,EAAAA;QAAA;MACA;MACA,KAAAzC,KAAA,CAAA2C,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACAd,KAAA,CAAAoB,KAAA,mBAAAR,KAAA,EAAAC,QAAA,EAAAC,MAAA;MACA;MACA,KAAA9C,KAAA,CAAA2C,EAAA,+BAAAU,KAAA,EAAAC,QAAA,EAAAR,MAAA;QACAd,KAAA,CAAAoB,KAAA,wBAAAC,KAAA,EAAAC,QAAA,EAAAR,MAAA;MACA;MACA,KAAA9C,KAAA,CAAA2C,EAAA,4BAAAY,SAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAAF,IAAA,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;QAAA;QACA7B,KAAA,CAAAoB,KAAA,CAAAU,KAAA,CAAA9B,KAAA,uBAAAuB,SAAA,EAAAlC,MAAA,CAAAsC,IAAA;MACA;IACA;IACA;IACAI,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAjF,IAAA;MACA,IAAAkF,KAAA,GAAAlF,IAAA,CAAAmF,QAAA,CAAAF,IAAA,CAAAjF,IAAA;MACA;MACA,KAAAkF,KAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA,SAAA7E,QAAA;QACA,IAAA8E,IAAA,GAAAL,IAAA,CAAApD,IAAA,sBAAArB,QAAA;QACA,KAAA8E,IAAA;UACA,KAAAF,QAAA,CAAAC,KAAA,iEAAA/C,MAAA,MAAA9B,QAAA;UACA;QACA;MACA;MACA;IACA;IACA+E,mBAAA,WAAAA,oBAAAC,GAAA,EAAAP,IAAA;MACA;MACA,IAAAO,GAAA,CAAAC,IAAA;QACA;QACA,IAAA/B,KAAA,QAAAzC,KAAA;QACA;QACA,IAAA0D,MAAA,GAAAjB,KAAA,CAAAgC,YAAA,GAAAC,KAAA;QACA;QACAjC,KAAA,CAAAkC,WAAA,CAAAjB,MAAA,WAAAhE,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAA2E,GAAA,CAAAK,QAAA;QACA;QACAnC,KAAA,CAAAoC,YAAA,CAAAnB,MAAA;MACA;QACA,KAAAS,QAAA,CAAAC,KAAA;MACA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACA,KAAAX,QAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}