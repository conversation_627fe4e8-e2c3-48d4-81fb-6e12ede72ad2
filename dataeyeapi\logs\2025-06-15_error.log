2025-06-15 22:22:53.652 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 22:22:53.652 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 22:22:56.132 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 22:22:56.132 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 22:22:56.134 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 22:22:57.191 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 22:22:58.359 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 22:22:58.359 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 22:23:21.501 | 456393b021ac4fa8b55ccc7f9a41b547 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为85740bc7-dd5a-48e5-8473-4eaea1b906c1的会话获取图片验证码成功
2025-06-15 22:25:21.880 | b41e49a171a740fe8458379e02c82fc7 | WARNING  | module_admin.service.login_service:__check_login_captcha:158 - 验证码已失效
2025-06-15 22:25:21.880 | b41e49a171a740fe8458379e02c82fc7 | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码已失效
2025-06-15 22:25:22.213 | 24066209e6ed40e0b1795521324b6ae4 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a40db771-28e5-47da-97df-2654f150c483的会话获取图片验证码成功
2025-06-15 22:26:09.289 | 1f83b34b7eb04ba9bb8104c7f574c354 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-15 22:26:10.430 | 900fbfb41c7844ddbb607361fecea320 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:26:11.146 | e77be1a6106e412599ac19e7e0345605 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:32:57.432 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 22:32:57.432 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 22:32:59.166 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 22:32:59.166 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 22:32:59.169 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 22:33:00.041 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 22:33:01.392 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 22:33:01.392 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 22:33:41.863 | 8db605915fa2487aa0c363d614b59e46 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:33:42.304 | 02e7a594c45a455395a68fcdb74b02cb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:34:30.913 | 1c493f776df94c8ca078fa10aa2828e8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:34:31.466 | 70b5030608a147be9d14ccceadcf58ee | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:34:56.258 | b7da260f62f2488d84a47ea30fa3ccb5 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:34:56.781 | 15fdbac732264b90b640872d12135125 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-15 22:34:57.027 | b7da260f62f2488d84a47ea30fa3ccb5 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-15 22:34:57.103 | 15fdbac732264b90b640872d12135125 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-15 22:34:58.084 | b64d48c9ad13437f852779ff7f99a5b7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:34:58.409 | 9f7e9b0b2c0643d29ab8a396a3883d7b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:34:59.356 | 3537ad3b41dd4eb9940dd72a8eb88b82 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:34:59.400 | 13a27adad8dd48cf83fa9d1685cbab7c | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-15 22:34:59.532 | 13a27adad8dd48cf83fa9d1685cbab7c | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-15 22:34:59.809 | 3537ad3b41dd4eb9940dd72a8eb88b82 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-15 22:35:19.082 | e7a2be4d8c834f0db17a7a452438b790 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:35:19.652 | 2d8a7927ccb040f283f6215af81c61d4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:36:03.489 | b0594eb8e28f4d81b09e8139ce9ac30a | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:36:03.492 | 10b1f369740443e5890e6fdce39077d4 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-15 22:36:03.821 | 10b1f369740443e5890e6fdce39077d4 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-15 22:36:04.164 | b0594eb8e28f4d81b09e8139ce9ac30a | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-15 22:36:10.692 | 6f277d0a3574453891d30c82ac94ae75 | INFO     | app.controllers.pinboard_controller:delete_pinboard:119 - 用户 admin 删除Pinboard: default-pinboard
2025-06-15 22:36:11.099 | 6f277d0a3574453891d30c82ac94ae75 | INFO     | app.controllers.pinboard_controller:delete_pinboard:127 - 成功删除Pinboard: default-pinboard
2025-06-15 22:36:12.019 | b4369beac5804dd4924c57de696077b5 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:36:12.435 | b4369beac5804dd4924c57de696077b5 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 22:36:51.962 | 3a187357d63c461ea1a44a18a611ff29 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:36:52.680 | baf21486c4df48ebb99497b6c21290ce | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:36:54.129 | df2a688b6e1045f2b0a9b38beb6a6cb4 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:36:54.131 | 93ee1890db914df188ed629b7576dc00 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-15 22:36:54.272 | 93ee1890db914df188ed629b7576dc00 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-15 22:36:54.538 | df2a688b6e1045f2b0a9b38beb6a6cb4 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 22:37:00.365 | 77d35b5c677942d684e754a7aafe91c1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:37:00.970 | c3a5b9682c794326b2c521a604f1cdfc | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:37:02.230 | 697e0906c760493b9bffcc589224385c | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:37:02.361 | 65eecbba38ac48feb71307b5d8ab8c99 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-15 22:37:02.507 | 65eecbba38ac48feb71307b5d8ab8c99 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-15 22:37:02.681 | 697e0906c760493b9bffcc589224385c | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 22:51:09.679 | 2cf34e7081d54894917626472ca4f778 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 22:51:10.382 | 7661b9b33da041e0b44d1141ce075ff3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 22:58:51.085 | b2c8d77b3abc484aae34405614c624d6 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-15 22:58:51.087 | aa64642550304df9a0e113f46668736b | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-15 22:58:51.389 | aa64642550304df9a0e113f46668736b | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-15 22:58:51.899 | b2c8d77b3abc484aae34405614c624d6 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 22:58:52.208 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 22:58:52.209 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 22:58:58.516 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 22:58:58.516 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 22:59:01.224 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 22:59:01.225 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 22:59:02.343 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 22:59:02.344 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 22:59:02.346 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 22:59:03.982 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 22:59:04.047 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 22:59:04.047 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 22:59:05.597 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 22:59:05.597 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 22:59:06.369 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 22:59:06.369 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 22:59:06.371 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 22:59:07.226 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 22:59:08.206 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 22:59:08.206 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 22:59:28.148 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 22:59:28.149 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 22:59:30.976 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 22:59:30.977 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 22:59:32.568 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 22:59:32.568 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 22:59:32.569 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 22:59:33.553 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 22:59:33.891 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 22:59:33.892 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 22:59:34.962 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 22:59:34.962 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 22:59:36.666 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 22:59:36.666 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 22:59:38.262 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 22:59:38.264 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 22:59:38.265 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 22:59:39.182 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 22:59:40.164 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 22:59:40.164 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:01:01.222 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:01:01.222 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:01:04.019 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:01:04.019 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:01:06.763 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:01:06.763 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:01:06.766 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:01:07.648 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:01:08.626 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:01:08.626 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:01:11.500 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:01:11.500 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:01:13.133 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:01:13.133 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:01:13.134 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:01:14.025 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:01:15.317 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:01:15.317 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:02:53.387 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:02:53.387 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:02:56.198 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:02:56.198 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:02:59.181 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:02:59.181 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:02:59.183 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:03:00.003 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:03:00.939 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:03:00.939 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:03:27.097 | 6be759631e3f4251a8dbe5ebf73b5a26 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:03:27.295 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:03:27.296 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:03:30.677 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:03:30.677 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:03:34.266 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:03:34.266 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:03:34.267 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:03:35.487 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:03:36.462 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:03:36.462 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:03:36.738 | 7050caa03bed487a8d2483a44cc2faff | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:08:28.624 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:08:28.624 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:08:31.486 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:08:31.487 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:08:34.202 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:08:34.203 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:08:34.204 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:08:35.117 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:08:36.114 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:08:36.114 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:11:03.102 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:11:03.103 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:19:20.618 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:19:20.618 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:19:20.618 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:19:20.619 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:19:23.634 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:19:23.634 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:19:23.636 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:19:23.676 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:19:23.677 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:19:23.678 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:19:24.806 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:19:24.858 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:19:26.101 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:19:26.101 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:19:26.191 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:19:26.191 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:20:50.904 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:20:50.906 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:20:53.282 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:20:53.282 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:20:53.283 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:20:54.252 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:20:55.209 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:20:55.209 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:21:55.850 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:21:55.851 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:21:59.183 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:21:59.184 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:22:01.206 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:22:01.206 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:22:03.488 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:22:03.488 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:22:03.490 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:22:04.435 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:22:04.516 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:22:04.516 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:22:05.469 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:22:05.469 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:22:06.405 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:22:06.405 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:22:06.407 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:22:07.270 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:22:08.330 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:22:08.330 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:22:12.920 | fe80deb32a96481296ab47a539df0484 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:22:13.675 | f76afbf9a34f45f98eca72c00a21feb8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:22:22.669 | 5ae080ec4cc149ce806d469cd853501e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:22:23.069 | 34e5d83ebfce40d9921d5bca5f6ca18b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:23:17.764 | 990782e759c7426c8aac6aa454633ddc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:23:18.481 | 2893dcf76ee147dda8ff9f0a84d00c5a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:34:03.831 | 350cda0b8b9945ac86c645a5f62f493f | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:34:04.570 | 350cda0b8b9945ac86c645a5f62f493f | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-15 23:34:15.586 | 5aca8deb10534704a0e0c8fcf61f4ff8 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-15 23:34:16.300 | 5aca8deb10534704a0e0c8fcf61f4ff8 | ERROR    | app.dao.pinboard_dao:restore_report:819 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:34:16.300 | 5aca8deb10534704a0e0c8fcf61f4ff8 | ERROR    | app.services.pinboard_service:restore_report:621 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:34:16.301 | 5aca8deb10534704a0e0c8fcf61f4ff8 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:34:54.604 | 1fe632ca626c4ea1ba1ec86ea8fe4da6 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-15 23:34:55.316 | 1fe632ca626c4ea1ba1ec86ea8fe4da6 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-15 23:35:36.789 | 77ec5f3233a34427a22dae758bc0d85a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:35:37.518 | 0ed52fe949884e0cbe2d2f2f50e0880b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:35:39.083 | 1bc61d0dd5fc4e708bef17266b831f24 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-15 23:35:39.491 | 1bc61d0dd5fc4e708bef17266b831f24 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-15 23:35:45.003 | fe6daa26e3ca46cc8c43cda33ab97684 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-15 23:35:45.349 | fe6daa26e3ca46cc8c43cda33ab97684 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:35:45.593 | fe6daa26e3ca46cc8c43cda33ab97684 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:35:45.593 | fe6daa26e3ca46cc8c43cda33ab97684 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:35:49.588 | ac53d11db2c64c5ea7aef3e1f1bbcbb9 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-15 23:35:49.894 | ac53d11db2c64c5ea7aef3e1f1bbcbb9 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:35:50.099 | ac53d11db2c64c5ea7aef3e1f1bbcbb9 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:35:50.099 | ac53d11db2c64c5ea7aef3e1f1bbcbb9 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:35:52.457 | e903eb1ebbc244c185f31a0d72d7fc19 | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-15 23:35:53.010 | e903eb1ebbc244c185f31a0d72d7fc19 | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-15 23:35:54.732 | d6ade15e2037463d806d7a2ba2c0fce3 | INFO     | app.controllers.pinboard_controller:get_reminders:270 - 用户 admin 请求定时提醒列表
2025-06-15 23:35:54.918 | d6ade15e2037463d806d7a2ba2c0fce3 | INFO     | app.controllers.pinboard_controller:get_reminders:281 - 成功获取定时提醒列表，共 0 条记录
2025-06-15 23:35:59.741 | e0e78c6aa56b432cac1e65305c122666 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:36:00.134 | e0e78c6aa56b432cac1e65305c122666 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-15 23:38:02.213 | c2fb9b85b15f485790ce9a5ea156c8e2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:38:02.815 | 6daa8b3e30f94cb6a3dd9a3147e74b09 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:38:03.946 | 65d236c91a9c4460abcf44b95535a231 | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-15 23:38:04.248 | 65d236c91a9c4460abcf44b95535a231 | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-15 23:43:27.754 | c548b942a3b34cb4ab9d93bb22b658fe | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-15 23:43:27.902 | c548b942a3b34cb4ab9d93bb22b658fe | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:43:28.002 | c548b942a3b34cb4ab9d93bb22b658fe | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:43:28.002 | c548b942a3b34cb4ab9d93bb22b658fe | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:43:32.019 | eff78a3aa27d4ce29e31b804736029a2 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-15 23:43:33.154 | eff78a3aa27d4ce29e31b804736029a2 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-15 23:45:56.583 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:45:56.584 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:46:01.225 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:46:01.225 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:46:01.227 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:46:03.378 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:46:05.935 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:46:05.935 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:46:37.721 | 6e235ac5e4054fe9a2e5dc0d4847d3ba | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:46:37.858 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:46:37.859 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:46:41.480 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:46:41.480 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:46:44.193 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:46:44.193 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:46:44.195 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:46:45.124 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:46:46.149 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:46:46.149 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:46:46.414 | c8db66e93b734a4c8a18c59d93351d72 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:46:47.248 | fc16b86e6db6489e88b70b5090961e97 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-15 23:46:47.650 | fc16b86e6db6489e88b70b5090961e97 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:46:47.754 | fc16b86e6db6489e88b70b5090961e97 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:46:47.754 | fc16b86e6db6489e88b70b5090961e97 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-15 23:47:56.531 | 087486668cc541dcb1b7f848ea19213f | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-15 23:47:57.143 | 087486668cc541dcb1b7f848ea19213f | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-15 23:47:58.684 | 098d9a8d47304cfcb69df51cb812a68f | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-15 23:47:58.685 | d2532a868d6e43e4b39c75da09def474 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-15 23:47:59.500 | d2532a868d6e43e4b39c75da09def474 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-15 23:48:00.317 | 098d9a8d47304cfcb69df51cb812a68f | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-15 23:48:04.625 | 5c666ac97c9d44ca92f73e9fb3e317a4 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-15 23:48:05.437 | 5c666ac97c9d44ca92f73e9fb3e317a4 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-15 23:48:07.753 | af74db5bb1ba46ffa6a1fba669e5529f | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:48:08.202 | af74db5bb1ba46ffa6a1fba669e5529f | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-15 23:48:11.544 | 22c93679fc334e2781791220737e749a | INFO     | app.controllers.pinboard_controller:get_pinboard_list:49 - 用户 admin 请求Pinboard列表
2025-06-15 23:48:11.917 | 5cb6f502d3c34a5da2ff66ed6fdf98e1 | INFO     | app.controllers.pinboard_controller:get_reminders:270 - 用户 admin 请求定时提醒列表
2025-06-15 23:48:12.300 | 5cb6f502d3c34a5da2ff66ed6fdf98e1 | INFO     | app.controllers.pinboard_controller:get_reminders:281 - 成功获取定时提醒列表，共 0 条记录
2025-06-15 23:48:12.680 | 22c93679fc334e2781791220737e749a | INFO     | app.controllers.pinboard_controller:get_pinboard_list:60 - 成功获取Pinboard列表，共 8 条记录
2025-06-15 23:48:26.227 | 44d88d7b8604404bbfe8ccb15e762ae1 | INFO     | app.controllers.pinboard_controller:delete_pinboard:125 - 用户 admin 删除Pinboard: pb-005-uuid-005
2025-06-15 23:48:26.634 | 44d88d7b8604404bbfe8ccb15e762ae1 | INFO     | app.controllers.pinboard_controller:delete_pinboard:133 - 成功删除Pinboard: pb-005-uuid-005
2025-06-15 23:48:27.250 | e6f05294ef8a4f58becd719f18c3d916 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-15 23:48:27.842 | e6f05294ef8a4f58becd719f18c3d916 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 23:48:32.381 | a0ec270b7ec544b8b05a63fd97363f08 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:48:32.880 | a0ec270b7ec544b8b05a63fd97363f08 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-15 23:48:39.449 | 6578d79a9ec94cb0946a390ee0075a8a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:48:40.108 | bffafe6c2c1c40e79d0498cef0478912 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:48:41.623 | a65e0f7da471435bb91aef8df5e4e5a8 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-15 23:48:41.633 | ca48a82ebf0445948461ba734d798530 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-15 23:48:41.760 | ca48a82ebf0445948461ba734d798530 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-15 23:48:42.205 | a65e0f7da471435bb91aef8df5e4e5a8 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 23:48:44.774 | 8745ab70cff647cf975988c311f2ab99 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:48:45.234 | 8745ab70cff647cf975988c311f2ab99 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-15 23:48:48.861 | 8195b0e53beb4b8d8338fad8a65b398f | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-15 23:48:49.571 | 8195b0e53beb4b8d8338fad8a65b398f | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:48:49.572 | 8195b0e53beb4b8d8338fad8a65b398f | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:48:49.572 | 8195b0e53beb4b8d8338fad8a65b398f | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:49:18.555 | 5cb97cbb6129416b9ade6c3b1e98a816 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-15 23:49:18.756 | 5cb97cbb6129416b9ade6c3b1e98a816 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-15 23:49:18.757 | 5cb97cbb6129416b9ade6c3b1e98a816 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-15 23:49:18.759 | 5cb97cbb6129416b9ade6c3b1e98a816 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-15 23:49:19.023 | 06fdd9480ce34b2dbebcbff0e6f15429 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-15 23:49:19.371 | 06fdd9480ce34b2dbebcbff0e6f15429 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-15 23:49:19.371 | 06fdd9480ce34b2dbebcbff0e6f15429 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-15 23:49:19.371 | 06fdd9480ce34b2dbebcbff0e6f15429 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-15 23:53:45.084 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-15 23:53:45.085 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-15 23:53:49.078 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-15 23:53:49.078 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-15 23:53:54.008 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-15 23:53:54.008 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-15 23:53:54.009 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-15 23:53:55.851 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-15 23:53:58.318 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-15 23:53:58.319 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-15 23:55:21.364 | a136dab413bc487594bd4aef462435ca | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-15 23:55:22.031 | 49819e7ef8cb4ddfab82a921f72da39b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-15 23:55:23.199 | fa476f9c3c19425e833cb8a9f709a6fc | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:55:23.375 | fa476f9c3c19425e833cb8a9f709a6fc | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-15 23:55:25.671 | fe9a976127e04a08b42bc735e5ddd5ea | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-15 23:55:25.673 | de2bde04882040b087d24a792a40aacb | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-15 23:55:25.851 | de2bde04882040b087d24a792a40aacb | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-15 23:55:26.255 | fe9a976127e04a08b42bc735e5ddd5ea | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-15 23:56:07.036 | 00e52a5c50bb4a14ba41b1c68cf109e8 | INFO     | app.controllers.pinboard_controller:delete_pinboard:125 - 用户 admin 删除Pinboard: pb-001-uuid-001
2025-06-15 23:56:07.437 | 00e52a5c50bb4a14ba41b1c68cf109e8 | INFO     | app.controllers.pinboard_controller:delete_pinboard:133 - 成功删除Pinboard: pb-001-uuid-001
2025-06-15 23:56:08.321 | e4a3a9c4014f4e8ba8caefe1d2612b01 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-15 23:56:08.803 | e4a3a9c4014f4e8ba8caefe1d2612b01 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-15 23:56:12.767 | fccfa855c0144c229e1fb4982330d7b6 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-15 23:56:13.272 | fccfa855c0144c229e1fb4982330d7b6 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-15 23:56:16.389 | 492eb71fd6944b3ebbe6e869cd8c7a6f | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-001-uuid-001
2025-06-15 23:56:16.736 | 492eb71fd6944b3ebbe6e869cd8c7a6f | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-001-uuid-001'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:56:16.737 | 492eb71fd6944b3ebbe6e869cd8c7a6f | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-001-uuid-001'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-15 23:56:16.737 | 492eb71fd6944b3ebbe6e869cd8c7a6f | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-001-uuid-001'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
