{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\router\\report.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\router\\report.js", "mtime": 1748102776000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_layout", "_interopRequireDefault", "require", "reportRouter", "path", "component", "Layout", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "_default", "exports"], "sources": ["D:/jgst/dataeyeui/src/router/report.js"], "sourcesContent": ["import Layout from '@/layout'\n\nconst reportRouter = {\n  path: '/report',\n  component: Layout,\n  redirect: '/report/recent',\n  name: 'Report',\n  meta: {\n    title: '报告',\n    icon: 'documentation'\n  },\n  children: [\n    {\n      path: 'recent',\n      component: () => import('@/views/report/recent/index'),\n      name: 'ReportRecent',\n      meta: { title: '最近', icon: 'time' }\n    },\n    {\n      path: 'my',\n      component: () => import('@/views/report/my/index'),\n      name: 'ReportMy',\n      meta: { title: '我的', icon: 'user' }\n    },\n    {\n      path: 'collaborate',\n      component: () => import('@/views/report/collaborate/index'),\n      name: 'ReportCollaborate',\n      meta: { title: '协作', icon: 'peoples' }\n    },\n    {\n      path: 'shared',\n      component: () => import('@/views/report/shared/index'),\n      name: 'ReportShared',\n      meta: { title: '共享', icon: 'share' }\n    },\n    {\n      path: 'followed',\n      component: () => import('@/views/report/followed/index'),\n      name: 'ReportFollowed',\n      meta: { title: '已关注', icon: 'star' }\n    },\n    {\n      path: 'timed',\n      component: () => import('@/views/report/timed/index'),\n      name: 'ReportTimed',\n      meta: { title: '定时提醒', icon: 'time-range' }\n    },\n    {\n      path: 'trash',\n      component: () => import('@/views/report/trash/index'),\n      name: 'ReportTrash',\n      meta: { title: '回收站', icon: 'delete' }\n    }\n  ]\n}\n\nexport default reportRouter\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEC,eAAM;EACjBC,QAAQ,EAAE,gBAAgB;EAC1BC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE;IACJC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDM,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAO;EACpC,CAAC,EACD;IACEP,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDM,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAO;EACpC,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,kCAAkC;MAAA;IAAA,CAAC;IAC3DM,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAU;EACvC,CAAC,EACD;IACEP,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDM,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ;EACrC,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDM,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAO;EACrC,CAAC,EACD;IACEP,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDM,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAa;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDM,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAS;EACvC,CAAC;AAEL,CAAC;AAAA,IAAAO,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAEcd,YAAY", "ignoreList": []}]}