{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\layout\\mixin\\ResizeHandler.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\layout\\mixin\\ResizeHandler.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_document", "document", "body", "WIDTH", "_default", "exports", "default", "watch", "$route", "route", "device", "sidebar", "opened", "store", "dispatch", "withoutAnimation", "beforeMount", "window", "addEventListener", "$_resizeHandler", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "isMobile", "$_isMobile", "methods", "rect", "getBoundingClientRect", "width", "hidden"], "sources": ["D:/jgst/dataeyeui/src/layout/mixin/ResizeHandler.js"], "sourcesContent": ["import store from '@/store'\n\nconst { body } = document\nconst WIDTH = 992 // refer to Bootstrap's responsive design\n\nexport default {\n  watch: {\n    $route(route) {\n      if (this.device === 'mobile' && this.sidebar.opened) {\n        store.dispatch('app/closeSideBar', { withoutAnimation: false })\n      }\n    }\n  },\n  beforeMount() {\n    window.addEventListener('resize', this.$_resizeHandler)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.$_resizeHandler)\n  },\n  mounted() {\n    const isMobile = this.$_isMobile()\n    if (isMobile) {\n      store.dispatch('app/toggleDevice', 'mobile')\n      store.dispatch('app/closeSideBar', { withoutAnimation: true })\n    }\n  },\n  methods: {\n    // use $_ for mixins properties\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\n    $_isMobile() {\n      const rect = body.getBoundingClientRect()\n      return rect.width - 1 < WIDTH\n    },\n    $_resizeHandler() {\n      if (!document.hidden) {\n        const isMobile = this.$_isMobile()\n        store.dispatch('app/toggleDevice', isMobile ? 'mobile' : 'desktop')\n\n        if (isMobile) {\n          store.dispatch('app/closeSideBar', { withoutAnimation: true })\n        }\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAiBC,QAAQ;EAAjBC,IAAI,GAAAF,SAAA,CAAJE,IAAI;AACZ,IAAMC,KAAK,GAAG,GAAG,EAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEH;EACbC,KAAK,EAAE;IACLC,MAAM,WAANA,MAAMA,CAACC,KAAK,EAAE;MACZ,IAAI,IAAI,CAACC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,EAAE;QACnDC,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;UAAEC,gBAAgB,EAAE;QAAM,CAAC,CAAC;MACjE;IACF;EACF,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC;EACzD,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;EAC5D,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAClC,IAAID,QAAQ,EAAE;MACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAC5CD,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;QAAEC,gBAAgB,EAAE;MAAK,CAAC,CAAC;IAChE;EACF,CAAC;EACDU,OAAO,EAAE;IACP;IACA;IACAD,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAME,IAAI,GAAGxB,IAAI,CAACyB,qBAAqB,CAAC,CAAC;MACzC,OAAOD,IAAI,CAACE,KAAK,GAAG,CAAC,GAAGzB,KAAK;IAC/B,CAAC;IACDgB,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAClB,QAAQ,CAAC4B,MAAM,EAAE;QACpB,IAAMN,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;QAClCX,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAES,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;QAEnE,IAAIA,QAAQ,EAAE;UACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;YAAEC,gBAAgB,EAAE;UAAK,CAAC,CAAC;QAChE;MACF;IACF;EACF;AACF,CAAC", "ignoreList": []}]}