{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\dashboard\\index.vue?vue&type=style&index=0&id=106c86ed&lang=less&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\dashboard\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749172155888}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAiLi9Xb3JrcGxhY2UubGVzcyI7CgoucHJvamVjdC1saXN0IHsKICAuY2FyZC10aXRsZSB7CiAgICBmb250LXNpemU6IDA7CgogICAgYSB7CiAgICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuODUpOwogICAgICBtYXJnaW4tbGVmdDogMTJweDsKICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgICAgIGhlaWdodDogMjRweDsKICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICB2ZXJ0aWNhbC1hbGlnbjogdG9wOwogICAgICBmb250LXNpemU6IDE0cHg7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgfQogICAgfQogIH0KCiAgLmNhcmQtZGVzY3JpcHRpb24gewogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMC40NSk7CiAgICBoZWlnaHQ6IDQ0cHg7CiAgICBsaW5lLWhlaWdodDogMjJweDsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgfQoKICAucHJvamVjdC1pdGVtIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBtYXJnaW4tdG9wOiA4cHg7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgaGVpZ2h0OiAyMHB4OwogICAgbGluZS1oZWlnaHQ6IDIwcHg7CgogICAgYSB7CiAgICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNDUpOwogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgIGZsZXg6IDEgMSAwOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgIH0KICAgIH0KCiAgICAuZGF0ZXRpbWUgewogICAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAwLjI1KTsKICAgICAgZmxleDogMCAwIGF1dG87CiAgICAgIGZsb2F0OiByaWdodDsKICAgIH0KICB9CgogIC5hbnQtY2FyZC1tZXRhLWRlc2NyaXB0aW9uIHsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNDUpOwogICAgaGVpZ2h0OiA0NHB4OwogICAgbGluZS1oZWlnaHQ6IDIycHg7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogIH0KfQoKLml0ZW0tZ3JvdXAgewogIHBhZGRpbmc6IDIwcHggMCA4cHggMjRweDsKICBmb250LXNpemU6IDA7CgogIGEgewogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMC42NSk7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxM3B4OwogICAgd2lkdGg6IDI1JTsKICB9Cn0KCi5tZW1iZXJzIHsKICBhIHsKICAgIGRpc3BsYXk6IGJsb2NrOwogICAgbWFyZ2luOiAxMnB4IDA7CiAgICBsaW5lLWhlaWdodDogMjRweDsKICAgIGhlaWdodDogMjRweDsKCiAgICAubWVtYmVyIHsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAwLjY1KTsKICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgICAgIG1heC13aWR0aDogMTAwcHg7CiAgICAgIHZlcnRpY2FsLWFsaWduOiB0b3A7CiAgICAgIG1hcmdpbi1sZWZ0OiAxMnB4OwogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgfQoKICAgICY6aG92ZXIgewogICAgICBzcGFuIHsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgfQogICAgfQogIH0KfQoKLm1vYmlsZSB7CiAgLnByb2plY3QtbGlzdCB7CiAgICAucHJvamVjdC1jYXJkLWdyaWQgewogICAgICB3aWR0aDogMTAwJTsKICAgIH0KICB9CgogIC5tb3JlLWluZm8gewogICAgYm9yZGVyOiAwOwogICAgcGFkZGluZy10b3A6IDE2cHg7CiAgICBtYXJnaW46IDE2cHggMCAxNnB4OwogIH0KCiAgLmhlYWRlckNvbnRlbnQgLnRpdGxlIC53ZWxjb21lLXRleHQgewogICAgZGlzcGxheTogbm9uZTsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAudA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div>\n    <div class=\"page-header-content\">\n      <div class=\"avatar\">\n        <a-avatar size=\"large\" :src=\"currentUser.avatar\" />\n      </div>\n      <div class=\"content\">\n        <div class=\"content-title\">\n          早安，{{ currentUser.name\n          }}<span class=\"welcome-text\">，祝你开心每一天！</span>\n        </div>\n        <div>{{ currentUser.title }} |{{ currentUser.group }}</div>\n      </div>\n      <div class=\"extra-content\">\n        <div class=\"stat-item\">\n          <a-statistic title=\"项目数\" :value=\"56\" />\n        </div>\n        <div class=\"stat-item\">\n          <a-statistic title=\"团队内排名\" :value=\"8\" suffix=\"/ 24\" />\n        </div>\n        <div class=\"stat-item\">\n          <a-statistic title=\"项目访问\" :value=\"2223\" />\n        </div>\n      </div>\n    </div>\n\n    <div>\n      <a-row :gutter=\"24\">\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n          <a-card\n            class=\"project-list\"\n            :loading=\"loading\"\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n            title=\"进行中的项目\"\n            :body-style=\"{ padding: 0 }\"\n          >\n            <a slot=\"extra\">全部项目</a>\n            <div>\n              <a-card-grid\n                class=\"project-card-grid\"\n                :key=\"i\"\n                v-for=\"(item, i) in projects\"\n              >\n                <a-card :bordered=\"false\" :body-style=\"{ padding: 0 }\">\n                  <a-card-meta>\n                    <div slot=\"title\" class=\"card-title\">\n                      <a-avatar size=\"small\" :src=\"item.logo\" />\n                      <a>{{ item.title }}</a>\n                    </div>\n                    <div slot=\"description\" class=\"card-description\">\n                      {{ item.description }}\n                    </div>\n                  </a-card-meta>\n                  <div class=\"project-item\">\n                    <a href=\"/#/\">{{ item.member || \"\" }}</a>\n                    <span class=\"datetime\">{{ item.updatedAt }}</span>\n                  </div>\n                </a-card>\n              </a-card-grid>\n            </div>\n          </a-card>\n\n          <a-card :loading=\"loading\" title=\"动态\" :bordered=\"false\">\n            <a-list>\n              <a-list-item :key=\"index\" v-for=\"(item, index) in activities\">\n                <a-list-item-meta>\n                  <a-avatar\n                    slot=\"avatar\"\n                    size=\"small\"\n                    :src=\"item.user.avatar\"\n                  />\n                  <div slot=\"title\">\n                    <span>{{ item.user.name }}</span\n                    >&nbsp; {{ item.template1 }}&nbsp;<a href=\"#\">{{\n                      item.group && item.group.name\n                    }}</a\n                    >&nbsp; <span>{{ item.template2 }}</span\n                    >&nbsp;\n                    <a :href=\"item.project && item.project.link\">{{\n                      item.project && item.project.name\n                    }}</a>\n                  </div>\n                  <div slot=\"description\">{{ item.updatedAt }}</div>\n                </a-list-item-meta>\n              </a-list-item>\n            </a-list>\n          </a-card>\n        </a-col>\n        <a-col\n          style=\"padding: 0 12px\"\n          :xl=\"8\"\n          :lg=\"24\"\n          :md=\"24\"\n          :sm=\"24\"\n          :xs=\"24\"\n        >\n          <a-card\n            title=\"快速开始 / 便捷导航\"\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n            :body-style=\"{ padding: 0 }\"\n          >\n            <div class=\"item-group\">\n              <a>操作一</a>\n              <a>操作二</a>\n              <a>操作三</a>\n              <a>操作四</a>\n              <a>操作五</a>\n              <a>操作六</a>\n              <a-button size=\"small\" type=\"primary\" ghost icon=\"plus\"\n                >添加</a-button\n              >\n            </div>\n          </a-card>\n          <a-card\n            title=\"XX 指数\"\n            style=\"margin-bottom: 24px\"\n            :loading=\"radarLoading\"\n            :bordered=\"false\"\n            :body-style=\"{ padding: 0 }\"\n          >\n            <div style=\"min-height: 400px\">\n              <radar :data=\"radarData\" />\n            </div>\n          </a-card>\n          <a-card :loading=\"loading\" title=\"团队\" :bordered=\"false\">\n            <div class=\"members\">\n              <a-row>\n                <a-col\n                  :span=\"12\"\n                  v-for=\"(item, index) in projects\"\n                  :key=\"index\"\n                >\n                  <a>\n                    <a-avatar size=\"small\" :src=\"item.logo\" />\n                    <span class=\"member\">{{ item.member }}</span>\n                  </a>\n                </a-col>\n              </a-row>\n            </div>\n          </a-card>\n        </a-col>\n      </a-row>\n    </div>\n  </div>\n</template>\n  \n<script>\nimport Radar from \"./Radar.vue\";\nimport {\n  Avatar,\n  Button,\n  Card,\n  Col,\n  List,\n  Row,\n  Statistic,\n} from \"ant-design-vue\";\nimport 'ant-design-vue/dist/antd.css';\nimport Vue from \"vue\";\n\nVue.component(Avatar.name, Avatar);\nVue.component(Button.name, Button);\nVue.component(Card.name, Card);\nVue.component(Card.Grid.name, Card.Grid);\nVue.component(Card.Meta.name, Card.Meta);\nVue.component(Col.name, Col);\nVue.component(List.name, List);\nVue.component(List.Item.name, List.Item);\nVue.component(List.Item.Meta.name, List.Item.Meta);\nVue.component(Row.name, Row);\nVue.component(Statistic.name, Statistic);\n\nexport default {\n  name: \"DashBoard\",\n  components: {\n    Radar,\n  },\n  data() {\n    return {\n      currentUser: {\n        avatar:\n          \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\n        name: \"吴彦祖\",\n        userid: \"00000001\",\n        email: \"<EMAIL>\",\n        signature: \"海纳百川，有容乃大\",\n        title: \"交互专家\",\n        group: \"蚂蚁金服－某某某事业群－某某平台部－某某技术部－UED\",\n      },\n      projects: [\n        {\n          id: \"xxx1\",\n          title: \"Alipay\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png\",\n          description: \"那是一种内在的东西，他们到达不了，也无法触及的\",\n          updatedAt: \"几秒前\",\n          member: \"科学搬砖组\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx2\",\n          title: \"Angular\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png\",\n          description: \"希望是一个好东西，也许是最好的，好东西是不会消亡的\",\n          updatedAt: \"6 年前\",\n          member: \"全组都是吴彦祖\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx3\",\n          title: \"Ant Design\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png\",\n          description: \"城镇中有那么多的酒馆，她却偏偏走进了我的酒馆\",\n          updatedAt: \"几秒前\",\n          member: \"中二少女团\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx4\",\n          title: \"Ant Design Pro\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png\",\n          description: \"那时候我只会想自己想要什么，从不想自己拥有什么\",\n          updatedAt: \"6 年前\",\n          member: \"程序员日常\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx5\",\n          title: \"Bootstrap\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png\",\n          description: \"凛冬将至\",\n          updatedAt: \"6 年前\",\n          member: \"高逼格设计天团\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx6\",\n          title: \"React\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/kZzEzemZyKLKFsojXItE.png\",\n          description: \"生命就像一盒巧克力，结果往往出人意料\",\n          updatedAt: \"6 年前\",\n          member: \"骗你来学计算机\",\n          href: \"\",\n          memberLink: \"\",\n        },\n      ],\n      activities: [\n        {\n          id: \"trend-1\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"曲丽丽\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\n          },\n          group: {\n            name: \"高逼格设计天团\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"六月迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n        {\n          id: \"trend-2\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"付小小\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png\",\n          },\n          group: {\n            name: \"高逼格设计天团\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"六月迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n        {\n          id: \"trend-3\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"林东东\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png\",\n          },\n          group: {\n            name: \"中二少女团\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"六月迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n        {\n          id: \"trend-4\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"周星星\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png\",\n          },\n          group: {\n            name: \"5 月日常迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"将\",\n          template2: \"更新至已发布状态\",\n        },\n        {\n          id: \"trend-5\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"朱偏右\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png\",\n          },\n          group: {\n            name: \"工程效能\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"留言\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"发布了\",\n        },\n        {\n          id: \"trend-6\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"乐哥\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png\",\n          },\n          group: {\n            name: \"程序员日常\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"品牌迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n      ],\n      radarData: [\n        {\n          item: \"引用\",\n          user: \"个人\",\n          score: 70,\n        },\n        {\n          item: \"引用\",\n          user: \"团队\",\n          score: 30,\n        },\n        {\n          item: \"引用\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"口碑\",\n          user: \"个人\",\n          score: 60,\n        },\n        {\n          item: \"口碑\",\n          user: \"团队\",\n          score: 70,\n        },\n        {\n          item: \"口碑\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"产量\",\n          user: \"个人\",\n          score: 50,\n        },\n        {\n          item: \"产量\",\n          user: \"团队\",\n          score: 60,\n        },\n        {\n          item: \"产量\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"贡献\",\n          user: \"个人\",\n          score: 40,\n        },\n        {\n          item: \"贡献\",\n          user: \"团队\",\n          score: 50,\n        },\n        {\n          item: \"贡献\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"热度\",\n          user: \"个人\",\n          score: 60,\n        },\n        {\n          item: \"热度\",\n          user: \"团队\",\n          score: 70,\n        },\n        {\n          item: \"热度\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"引用\",\n          user: \"个人\",\n          score: 70,\n        },\n        {\n          item: \"引用\",\n          user: \"团队\",\n          score: 50,\n        },\n        {\n          item: \"引用\",\n          user: \"部门\",\n          score: 40,\n        },\n      ],\n      loading: true,\n      radarLoading: true,\n    };\n  },\n  mounted() {\n    setTimeout(() => {\n      this.loading = false;\n      this.radarLoading = false;\n    }, 1000);\n  },\n};\n</script>\n  \n  <style lang=\"less\" scoped>\n@import \"./Workplace.less\";\n\n.project-list {\n  .card-title {\n    font-size: 0;\n\n    a {\n      color: rgba(0, 0, 0, 0.85);\n      margin-left: 12px;\n      line-height: 24px;\n      height: 24px;\n      display: inline-block;\n      vertical-align: top;\n      font-size: 14px;\n\n      &:hover {\n        color: #1890ff;\n      }\n    }\n  }\n\n  .card-description {\n    color: rgba(0, 0, 0, 0.45);\n    height: 44px;\n    line-height: 22px;\n    overflow: hidden;\n  }\n\n  .project-item {\n    display: flex;\n    margin-top: 8px;\n    overflow: hidden;\n    font-size: 12px;\n    height: 20px;\n    line-height: 20px;\n\n    a {\n      color: rgba(0, 0, 0, 0.45);\n      display: inline-block;\n      flex: 1 1 0;\n\n      &:hover {\n        color: #1890ff;\n      }\n    }\n\n    .datetime {\n      color: rgba(0, 0, 0, 0.25);\n      flex: 0 0 auto;\n      float: right;\n    }\n  }\n\n  .ant-card-meta-description {\n    color: rgba(0, 0, 0, 0.45);\n    height: 44px;\n    line-height: 22px;\n    overflow: hidden;\n  }\n}\n\n.item-group {\n  padding: 20px 0 8px 24px;\n  font-size: 0;\n\n  a {\n    color: rgba(0, 0, 0, 0.65);\n    display: inline-block;\n    font-size: 14px;\n    margin-bottom: 13px;\n    width: 25%;\n  }\n}\n\n.members {\n  a {\n    display: block;\n    margin: 12px 0;\n    line-height: 24px;\n    height: 24px;\n\n    .member {\n      font-size: 14px;\n      color: rgba(0, 0, 0, 0.65);\n      line-height: 24px;\n      max-width: 100px;\n      vertical-align: top;\n      margin-left: 12px;\n      transition: all 0.3s;\n      display: inline-block;\n    }\n\n    &:hover {\n      span {\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n.mobile {\n  .project-list {\n    .project-card-grid {\n      width: 100%;\n    }\n  }\n\n  .more-info {\n    border: 0;\n    padding-top: 16px;\n    margin: 16px 0 16px;\n  }\n\n  .headerContent .title .welcome-text {\n    display: none;\n  }\n}\n</style>\n  "]}]}