import request from '@/utils/request'

// 卡片相关API

// 获取卡片列表
export function getCardList(params) {
  return request({
    url: '/api/cards',
    method: 'get',
    params
  })
}

// 获取单个卡片详情
export function getCardById(id) {
  return request({
    url: `/api/cards/${id}`,
    method: 'get'
  })
}

// 新增卡片
export function addCard(data) {
  return request({
    url: '/api/cards',
    method: 'post',
    data
  })
}

// 更新卡片
export function updateCard(id, data) {
  return request({
    url: `/api/cards/${id}`,
    method: 'put',
    data
  })
}

// 删除卡片
export function removeCard(id) {
  return request({
    url: `/api/cards/${id}`,
    method: 'delete'
  })
}