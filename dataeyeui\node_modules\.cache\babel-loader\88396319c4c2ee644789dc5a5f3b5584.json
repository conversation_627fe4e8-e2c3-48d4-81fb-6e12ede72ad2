{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\App.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwp2YXIgX1RoZW1lUGlja2VyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL2NvbXBvbmVudHMvVGhlbWVQaWNrZXIiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiQXBwIiwKICBjb21wb25lbnRzOiB7CiAgICBUaGVtZVBpY2tlcjogX1RoZW1lUGlja2VyLmRlZmF1bHQKICB9LAogIG1ldGFJbmZvOiBmdW5jdGlvbiBtZXRhSW5mbygpIHsKICAgIHJldHVybiB7CiAgICAgIHRpdGxlOiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5keW5hbWljVGl0bGUgJiYgdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudGl0bGUsCiAgICAgIHRpdGxlVGVtcGxhdGU6IGZ1bmN0aW9uIHRpdGxlVGVtcGxhdGUodGl0bGUpIHsKICAgICAgICByZXR1cm4gdGl0bGUgPyAiIi5jb25jYXQodGl0bGUsICIgLSAiKS5jb25jYXQocHJvY2Vzcy5lbnYuVlVFX0FQUF9USVRMRSkgOiBwcm9jZXNzLmVudi5WVUVfQVBQX1RJVExFOwogICAgICB9CiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["_ThemePicker", "_interopRequireDefault", "require", "name", "components", "ThemePicker", "metaInfo", "title", "$store", "state", "settings", "dynamicTitle", "titleTemplate", "concat", "process", "env", "VUE_APP_TITLE"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n    <theme-picker />\n  </div>\n</template>\n\n<script>\nimport ThemePicker from \"@/components/ThemePicker\";\n\nexport default {\n  name: \"App\",\n  components: { ThemePicker },\n  metaInfo() {\n    return {\n      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\n      titleTemplate: title => {\n        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\n      }\n    }\n  }\n};\n</script>\n<style scoped>\n#app .theme-picker {\n  display: none;\n}\n</style>\n"], "mappings": ";;;;;;;;AAQA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,QAAA,WAAAA,SAAA;IACA;MACAC,KAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;MACAK,aAAA,WAAAA,cAAAL,KAAA;QACA,OAAAA,KAAA,MAAAM,MAAA,CAAAN,KAAA,SAAAM,MAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAC,aAAA,IAAAF,OAAA,CAAAC,GAAA,CAAAC,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}