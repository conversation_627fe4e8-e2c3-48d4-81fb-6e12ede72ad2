{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\test-api.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\test-api.vue", "mtime": 1749638102000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBheGlvcyBmcm9tICdheGlvcycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVGVzdEFwaScsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICByZXN1bHQ6IG51bGwsCiAgICAgIGVycm9yOiBudWxsCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBhc3luYyB0ZXN0Q29ubmVjdGlvbigpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLnJlc3VsdCA9IG51bGwKICAgICAgdGhpcy5lcnJvciA9IG51bGwKICAgICAgCiAgICAgIHRyeSB7CiAgICAgICAgLy8g55u05o6l5rWL6K+V5ZCO56uv5o6l5Y+j77yM5LiN6YCa6L+H5YmN56uv5Luj55CGCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoJ2h0dHA6Ly9sb2NhbGhvc3Q6OTA5OS9waW5ib2FyZC90ZXN0JywgewogICAgICAgICAgdGltZW91dDogNTAwMCwKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgCiAgICAgICAgdGhpcy5yZXN1bHQgPSBKU09OLnN0cmluZ2lmeShyZXNwb25zZS5kYXRhLCBudWxsLCAyKQogICAgICAgIGNvbnNvbGUubG9nKCdBUEnmtYvor5XmiJDlip86JywgcmVzcG9uc2UuZGF0YSkKICAgICAgICAKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLmVycm9yID0gewogICAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSwKICAgICAgICAgIGNvZGU6IGVycm9yLmNvZGUsCiAgICAgICAgICByZXNwb25zZTogZXJyb3IucmVzcG9uc2UgPyB7CiAgICAgICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2Uuc3RhdHVzLAogICAgICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZS5zdGF0dXNUZXh0LAogICAgICAgICAgICBkYXRhOiBlcnJvci5yZXNwb25zZS5kYXRhCiAgICAgICAgICB9IDogbnVsbAogICAgICAgIH0KICAgICAgICBjb25zb2xlLmVycm9yKCdBUEnmtYvor5XlpLHotKU6JywgZXJyb3IpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["test-api.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "test-api.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"test-api\">\n    <h2>API连接测试</h2>\n    \n    <el-card class=\"test-card\">\n      <div slot=\"header\">\n        <span>测试后端连接</span>\n      </div>\n      \n      <el-button @click=\"testConnection\" type=\"primary\" :loading=\"loading\">\n        测试连接\n      </el-button>\n      \n      <div v-if=\"result\" class=\"result-area\">\n        <h4>测试结果：</h4>\n        <pre>{{ result }}</pre>\n      </div>\n      \n      <div v-if=\"error\" class=\"error-area\">\n        <h4>错误信息：</h4>\n        <pre>{{ error }}</pre>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios'\n\nexport default {\n  name: 'TestApi',\n  data() {\n    return {\n      loading: false,\n      result: null,\n      error: null\n    }\n  },\n  methods: {\n    async testConnection() {\n      this.loading = true\n      this.result = null\n      this.error = null\n      \n      try {\n        // 直接测试后端接口，不通过前端代理\n        const response = await axios.get('http://localhost:9099/pinboard/test', {\n          timeout: 5000,\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        })\n        \n        this.result = JSON.stringify(response.data, null, 2)\n        console.log('API测试成功:', response.data)\n        \n      } catch (error) {\n        this.error = {\n          message: error.message,\n          code: error.code,\n          response: error.response ? {\n            status: error.response.status,\n            statusText: error.response.statusText,\n            data: error.response.data\n          } : null\n        }\n        console.error('API测试失败:', error)\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-api {\n  padding: 20px;\n}\n\n.test-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.result-area, .error-area {\n  margin-top: 20px;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.result-area {\n  background-color: #f0f9ff;\n  border: 1px solid #67c23a;\n}\n\n.error-area {\n  background-color: #fef0f0;\n  border: 1px solid #f56c6c;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n</style>\n"]}]}