import request from '@/utils/request'

// 指标告警相关API
export function getAlertList(params) {
  return request({
    url: '/api/alerts',
    method: 'get',
    params
  })
}

export function getAlertById(id) {
  return request({
    url: `/api/alerts/${id}`,
    method: 'get'
  })
}

export function addAlert(data) {
  return request({
    url: '/api/alerts',
    method: 'post',
    data
  })
}

export function updateAlert(id, data) {
  return request({
    url: `/api/alerts/${id}`,
    method: 'put',
    data
  })
}

export function removeAlert(id) {
  return request({
    url: `/api/alerts/${id}`,
    method: 'delete'
  })
}
