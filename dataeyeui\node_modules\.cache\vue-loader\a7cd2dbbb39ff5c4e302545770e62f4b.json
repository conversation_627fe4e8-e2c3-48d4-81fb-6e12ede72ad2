{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\login.vue", "mtime": 1747038934000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"login\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\n      <h3 class=\"title\">金刚数瞳</h3>\n      <el-form-item prop=\"username\">\n        <el-input\n          v-model=\"loginForm.username\"\n          type=\"text\"\n          auto-complete=\"off\"\n          placeholder=\"账号\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"loginForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"loginForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"login-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleLogin\"\n        >\n          <span v-if=\"!loading\">登 录</span>\n          <span v-else>登 录 中...</span>\n        </el-button>\n        <div style=\"float: right;\" v-if=\"register\">\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-login-footer\">\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\";\nimport Cookies from \"js-cookie\";\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\n\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      codeUrl: \"\",\n      loginForm: {\n        username: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\"\n      },\n      loginRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      // 验证码开关\n      captchaEnabled: true,\n      // 注册开关\n      register: false,\n      redirect: undefined\n    };\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect;\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getCode();\n    this.getCookie();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\n        this.register = res.registerEnabled === undefined ? false : res.registerEnabled;\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\n          this.loginForm.uuid = res.uuid;\n        }\n      });\n    },\n    getCookie() {\n      const username = Cookies.get(\"username\");\n      const password = Cookies.get(\"password\");\n      const rememberMe = Cookies.get('rememberMe')\n      this.loginForm = {\n        username: username === undefined ? this.loginForm.username : username,\n        password: password === undefined ? this.loginForm.password : decrypt(password),\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\n      };\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          if (this.loginForm.rememberMe) {\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\n          } else {\n            Cookies.remove(\"username\");\n            Cookies.remove(\"password\");\n            Cookies.remove('rememberMe');\n          }\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\n            this.$router.push({ path: this.redirect || \"/\" }).catch(()=>{});\n          }).catch(() => {\n            this.loading = false;\n            if (this.captchaEnabled) {\n              this.getCode();\n            }\n          });\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.login {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.login-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.login-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.login-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-login-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.login-code-img {\n  height: 38px;\n}\n</style>\n"]}]}