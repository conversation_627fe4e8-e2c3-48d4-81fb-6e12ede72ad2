#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, BigInteger, Text, DateTime, Integer, Enum, ForeignKey
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy.orm import relationship
from config.database import Base
from datetime import datetime

class AccountProject(Base):
    """项目表"""
    __tablename__ = 'account_projects'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='项目ID')
    name = Column(String(100), nullable=False, comment='项目名称')
    desc = Column(Text, comment='项目详细描述')
    owner_id = Column(BigInteger, nullable=False, comment='项目所有者ID')
    locked = Column(TINYINT(1), default=0, comment='项目锁定状态')
    preset = Column(TINYINT(1), default=0, comment='预置项目标识')
    adapter = Column(Integer, comment='数据适配器类型编号')
    need_sync_keyword = Column(TINYINT(1), default=0, comment='关键词同步需求')
    config_ready = Column(TINYINT(1), default=0, comment='配置就绪状态')
    ws_status = Column(Integer, comment='数据工作空间状态码')
    ws_etag_cut = Column(String(32), comment='精确匹配模式数据版本标识')
    ws_etag_fuzzy = Column(String(32), comment='模糊匹配模式数据版本标识')
    can_access_data = Column(TINYINT(1), default=1, comment='数据访问权限标识')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')

    # 关系
    pinboards = relationship("Pinboard", back_populates="project")
    tags = relationship("PinboardTag", back_populates="project")

class Pinboard(Base):
    """Pinboard表"""
    __tablename__ = 'pinboards'

    uid = Column(String(36), primary_key=True, comment='Pinboard唯一标识符')
    project_id = Column(BigInteger, ForeignKey('account_projects.id'), nullable=False, comment='所属项目ID')
    name = Column(String(100), nullable=False, comment='Pinboard名称')
    description = Column(Text, comment='Pinboard描述')
    owner_id = Column(BigInteger, nullable=False, comment='创建者ID')
    is_template = Column(TINYINT(1), default=0, comment='是否为模板')
    is_shared = Column(TINYINT(1), default=0, comment='是否共享')
    share_type = Column(Enum('none', 'user', 'all'), default='none', comment='共享类型')
    is_deleted = Column(TINYINT(1), default=0, comment='是否在回收站')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')

    # 关系
    project = relationship("AccountProject", back_populates="pinboards")
    tag_relations = relationship("PinboardTagRelation", back_populates="pinboard")
    shares = relationship("PinboardShare", back_populates="pinboard")
    stars = relationship("PinboardStar", back_populates="pinboard")

class PinboardTag(Base):
    """Pinboard标签表"""
    __tablename__ = 'pinboard_tags'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='标签ID')
    project_id = Column(BigInteger, ForeignKey('account_projects.id'), nullable=False, comment='所属项目ID')
    name = Column(String(50), nullable=False, comment='标签名称')
    color = Column(String(20), comment='标签颜色')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')

    # 关系
    project = relationship("AccountProject", back_populates="tags")
    tag_relations = relationship("PinboardTagRelation", back_populates="tag")

class PinboardTagRelation(Base):
    """Pinboard标签关联表"""
    __tablename__ = 'pinboard_tag_relations'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='关联ID')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='Pinboard ID')
    tag_id = Column(BigInteger, ForeignKey('pinboard_tags.id'), nullable=False, comment='标签ID')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')

    # 关系
    pinboard = relationship("Pinboard", back_populates="tag_relations")
    tag = relationship("PinboardTag", back_populates="tag_relations")

class PinboardShare(Base):
    """Pinboard分享表"""
    __tablename__ = 'pinboard_shares'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='共享ID')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='Pinboard ID')
    user_id = Column(BigInteger, comment='共享目标用户ID(为空表示共享给所有人)')
    share_url = Column(String(255), comment='分享链接')
    access_password = Column(String(100), comment='访问密码')
    expire_time = Column(DateTime, comment='过期时间')
    view_count = Column(Integer, default=0, comment='访问次数')
    permissions = Column(Text, comment='访问权限设置(JSON格式)')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    create_by = Column(String(64), comment='创建者')

    # 关系
    pinboard = relationship("Pinboard", back_populates="shares")

class PinboardStar(Base):
    """Pinboard收藏表"""
    __tablename__ = 'pinboard_stars'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='收藏ID')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='Pinboard ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')

    # 关系
    pinboard = relationship("Pinboard", back_populates="stars")

class UserNotificationSettings(Base):
    """用户通知设置表"""
    __tablename__ = 'user_notification_settings'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='设置ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    email_enabled = Column(TINYINT(1), default=1, comment='邮件通知开关')
    in_app_enabled = Column(TINYINT(1), default=1, comment='站内通知开关')
    frequency = Column(Enum('immediate', 'daily', 'weekly'), default='immediate', comment='通知频率')
    notification_types = Column(Text, comment='通知类型设置(JSON格式)')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

class PinboardUpdate(Base):
    """报告更新记录表"""
    __tablename__ = 'pinboard_updates'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='更新记录ID')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='Pinboard ID')
    update_type = Column(Enum('content_update', 'new_comment', 'author_post'), comment='更新类型')
    update_content = Column(Text, comment='更新内容')
    update_by = Column(BigInteger, comment='更新者ID')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    # 关系
    pinboard = relationship("Pinboard")

class PinboardReminder(Base):
    """报告提醒表"""
    __tablename__ = 'pinboard_reminders'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='提醒ID')
    name = Column(String(100), nullable=False, comment='提醒名称')
    description = Column(Text, comment='提醒描述')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='关联报告ID')
    frequency = Column(Enum('daily', 'weekly', 'monthly', 'custom'), comment='提醒频率')
    cron_expression = Column(String(255), comment='Cron表达式')
    execute_time = Column(String(10), comment='执行时间')
    recipients = Column(Text, comment='接收人列表(JSON格式)')
    push_methods = Column(Text, comment='推送方式(JSON格式)')
    expire_date = Column(DateTime, comment='过期日期')
    enabled = Column(TINYINT(1), default=1, comment='是否启用')
    next_run_time = Column(DateTime, comment='下次执行时间')
    job_id = Column(BigInteger, comment='关联的系统任务ID')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')

    # 关系
    pinboard = relationship("Pinboard")

class PinboardReminderLog(Base):
    """提醒执行日志表"""
    __tablename__ = 'pinboard_reminder_logs'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='日志ID')
    reminder_id = Column(BigInteger, ForeignKey('pinboard_reminders.id'), nullable=False, comment='提醒ID')
    execute_time = Column(DateTime, comment='执行时间')
    status = Column(Enum('success', 'failed'), comment='执行状态')
    recipients_count = Column(Integer, comment='接收人数')
    message = Column(Text, comment='执行消息')
    duration = Column(String(20), comment='执行耗时')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    # 关系
    reminder = relationship("PinboardReminder")

class PinboardDeleteRecord(Base):
    """删除记录表"""
    __tablename__ = 'pinboard_delete_records'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='删除记录ID')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='Pinboard ID')
    original_path = Column(String(500), comment='原路径')
    delete_time = Column(DateTime, default=datetime.now, comment='删除时间')
    delete_by = Column(BigInteger, comment='删除者ID')
    delete_reason = Column(Text, comment='删除原因')
    file_size = Column(String(20), comment='文件大小')
    expire_time = Column(DateTime, comment='过期时间(30天后永久删除)')
    restored = Column(TINYINT(1), default=0, comment='是否已恢复')
    restore_time = Column(DateTime, comment='恢复时间')
    restore_by = Column(BigInteger, comment='恢复者ID')
    restore_path = Column(String(500), comment='恢复路径')

    # 关系
    pinboard = relationship("Pinboard")
