{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\store\\modules\\settings.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\store\\modules\\settings.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_settings", "_interopRequireDefault", "require", "sideTheme", "defaultSettings", "showSettings", "topNav", "tagsView", "fixedHeader", "sidebarLogo", "dynamicTitle", "storageSetting", "JSON", "parse", "localStorage", "getItem", "state", "title", "theme", "undefined", "mutations", "CHANGE_SETTING", "_ref", "key", "value", "hasOwnProperty", "actions", "changeSetting", "_ref2", "data", "commit", "setTitle", "_ref3", "_default", "exports", "default", "namespaced"], "sources": ["D:/jgst/dataeyeui/src/store/modules/settings.js"], "sourcesContent": ["import defaultSettings from '@/settings'\n\nconst { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } = defaultSettings\n\nconst storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''\nconst state = {\n  title: '',\n  theme: storageSetting.theme || '#409EFF',\n  sideTheme: storageSetting.sideTheme || sideTheme,\n  showSettings: showSettings,\n  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,\n  tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,\n  fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,\n  sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,\n  dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle\n}\nconst mutations = {\n  CHANGE_SETTING: (state, { key, value }) => {\n    if (state.hasOwnProperty(key)) {\n      state[key] = value\n    }\n  }\n}\n\nconst actions = {\n  // 修改布局设置\n  changeSetting({ commit }, data) {\n    commit('CHANGE_SETTING', data)\n  },\n  // 设置网页标题\n  setTitle({ commit }, title) {\n    state.title = title\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n\n"], "mappings": ";;;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAQC,SAAS,GAA6EC,iBAAe,CAArGD,SAAS;EAAEE,YAAY,GAA+DD,iBAAe,CAA1FC,YAAY;EAAEC,MAAM,GAAuDF,iBAAe,CAA5EE,MAAM;EAAEC,QAAQ,GAA6CH,iBAAe,CAApEG,QAAQ;EAAEC,WAAW,GAAgCJ,iBAAe,CAA1DI,WAAW;EAAEC,WAAW,GAAmBL,iBAAe,CAA7CK,WAAW;EAAEC,YAAY,GAAKN,iBAAe,CAAhCM,YAAY;AAEzF,IAAMC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE;AAC/E,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAEP,cAAc,CAACO,KAAK,IAAI,SAAS;EACxCf,SAAS,EAAEQ,cAAc,CAACR,SAAS,IAAIA,SAAS;EAChDE,YAAY,EAAEA,YAAY;EAC1BC,MAAM,EAAEK,cAAc,CAACL,MAAM,KAAKa,SAAS,GAAGb,MAAM,GAAGK,cAAc,CAACL,MAAM;EAC5EC,QAAQ,EAAEI,cAAc,CAACJ,QAAQ,KAAKY,SAAS,GAAGZ,QAAQ,GAAGI,cAAc,CAACJ,QAAQ;EACpFC,WAAW,EAAEG,cAAc,CAACH,WAAW,KAAKW,SAAS,GAAGX,WAAW,GAAGG,cAAc,CAACH,WAAW;EAChGC,WAAW,EAAEE,cAAc,CAACF,WAAW,KAAKU,SAAS,GAAGV,WAAW,GAAGE,cAAc,CAACF,WAAW;EAChGC,YAAY,EAAEC,cAAc,CAACD,YAAY,KAAKS,SAAS,GAAGT,YAAY,GAAGC,cAAc,CAACD;AAC1F,CAAC;AACD,IAAMU,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAGL,KAAK,EAAAM,IAAA,EAAqB;IAAA,IAAjBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAClC,IAAIR,KAAK,CAACS,cAAc,CAACF,GAAG,CAAC,EAAE;MAC7BP,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACd;EACAC,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,KAAA,CAANE,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC,CAAC;EACD;EACAE,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAAaf,KAAK,EAAE;IAAA,IAAjBa,MAAM,GAAAE,KAAA,CAANF,MAAM;IACfd,KAAK,CAACC,KAAK,GAAGA,KAAK;EACrB;AACF,CAAC;AAAA,IAAAgB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACbC,UAAU,EAAE,IAAI;EAChBpB,KAAK,EAALA,KAAK;EACLI,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}