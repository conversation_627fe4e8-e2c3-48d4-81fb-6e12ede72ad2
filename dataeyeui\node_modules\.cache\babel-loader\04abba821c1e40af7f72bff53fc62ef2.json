{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\error\\401.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\error\\401.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKdmFyIF8gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvYXNzZXRzLzQwMV9pbWFnZXMvNDAxLmdpZiIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdQYWdlNDAxJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZXJyR2lmOiBfLmRlZmF1bHQgKyAnPycgKyArbmV3IERhdGUoKQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGJhY2s6IGZ1bmN0aW9uIGJhY2soKSB7CiAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5ub0dvQmFjaykgewogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICAgIHBhdGg6ICcvJwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_", "_interopRequireDefault", "require", "name", "data", "err<PERSON><PERSON>", "Date", "methods", "back", "$route", "query", "noGoBack", "$router", "push", "path", "go"], "sources": ["src/views/error/401.vue"], "sourcesContent": ["<template>\n  <div class=\"errPage-container\">\n    <el-button icon=\"arrow-left\" class=\"pan-back-btn\" @click=\"back\">\n      返回\n    </el-button>\n    <el-row>\n      <el-col :span=\"12\">\n        <h1 class=\"text-jumbo text-ginormous\">\n          401错误!\n        </h1>\n        <h2>您没有访问权限！</h2>\n        <h6>对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面</h6>\n        <ul class=\"list-unstyled\">\n          <li class=\"link-type\">\n            <router-link to=\"/\">\n              回首页\n            </router-link>\n          </li>\n        </ul>\n      </el-col>\n      <el-col :span=\"12\">\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport errGif from '@/assets/401_images/401.gif'\n\nexport default {\n  name: 'Page401',\n  data() {\n    return {\n      errGif: errGif + '?' + +new Date()\n    }\n  },\n  methods: {\n    back() {\n      if (this.$route.query.noGoBack) {\n        this.$router.push({ path: '/' })\n      } else {\n        this.$router.go(-1)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .errPage-container {\n    width: 800px;\n    max-width: 100%;\n    margin: 100px auto;\n    .pan-back-btn {\n      background: #008489;\n      color: #fff;\n      border: none!important;\n    }\n    .pan-gif {\n      margin: 0 auto;\n      display: block;\n    }\n    .pan-img {\n      display: block;\n      margin: 0 auto;\n      width: 100%;\n    }\n    .text-jumbo {\n      font-size: 60px;\n      font-weight: 700;\n      color: #484848;\n    }\n    .list-unstyled {\n      font-size: 14px;\n      li {\n        padding-bottom: 5px;\n      }\n      a {\n        color: #008489;\n        text-decoration: none;\n        &:hover {\n          text-decoration: underline;\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;AA4BA,IAAAA,CAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAA,SAAA,cAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UAAAC,IAAA;QAAA;MACA;QACA,KAAAF,OAAA,CAAAG,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}