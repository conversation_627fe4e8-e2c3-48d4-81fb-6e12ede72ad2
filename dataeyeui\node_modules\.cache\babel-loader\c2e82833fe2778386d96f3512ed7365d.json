{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\plugins\\tab.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\plugins\\tab.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_router", "_default", "exports", "default", "refreshPage", "obj", "_router$currentRoute", "router", "currentRoute", "path", "query", "matched", "undefined", "for<PERSON>ach", "m", "components", "name", "includes", "store", "dispatch", "then", "_obj", "replace", "closeOpenPage", "push", "closePage", "_ref", "visitedViews", "latestView", "slice", "fullPath", "closeAllPage", "closeLeftPage", "closeRightPage", "closeOtherPage", "openPage", "title", "url", "params", "meta", "updatePage"], "sources": ["D:/jgst/dataeyeui/src/plugins/tab.js"], "sourcesContent": ["import store from '@/store'\nimport router from '@/router';\n\nexport default {\n  // 刷新当前tab页签\n  refreshPage(obj) {\n    const { path, query, matched } = router.currentRoute;\n    if (obj === undefined) {\n      matched.forEach((m) => {\n        if (m.components && m.components.default && m.components.default.name) {\n          if (!['Layout', 'ParentView'].includes(m.components.default.name)) {\n            obj = { name: m.components.default.name, path: path, query: query };\n          }\n        }\n      });\n    }\n    return store.dispatch('tagsView/delCachedView', obj).then(() => {\n      const { path, query } = obj\n      router.replace({\n        path: '/redirect' + path,\n        query: query\n      })\n    })\n  },\n  // 关闭当前tab页签，打开新页签\n  closeOpenPage(obj) {\n    store.dispatch(\"tagsView/delView\", router.currentRoute);\n    if (obj !== undefined) {\n      return router.push(obj);\n    }\n  },\n  // 关闭指定tab页签\n  closePage(obj) {\n    if (obj === undefined) {\n      return store.dispatch('tagsView/delView', router.currentRoute).then(({ visitedViews }) => {\n        const latestView = visitedViews.slice(-1)[0]\n        if (latestView) {\n          return router.push(latestView.fullPath)\n        }\n        return router.push('/');\n      });\n    }\n    return store.dispatch('tagsView/delView', obj);\n  },\n  // 关闭所有tab页签\n  closeAllPage() {\n    return store.dispatch('tagsView/delAllViews');\n  },\n  // 关闭左侧tab页签\n  closeLeftPage(obj) {\n    return store.dispatch('tagsView/delLeftTags', obj || router.currentRoute);\n  },\n  // 关闭右侧tab页签\n  closeRightPage(obj) {\n    return store.dispatch('tagsView/delRightTags', obj || router.currentRoute);\n  },\n  // 关闭其他tab页签\n  closeOtherPage(obj) {\n    return store.dispatch('tagsView/delOthersViews', obj || router.currentRoute);\n  },\n  // 添加tab页签\n  openPage(title, url, params) {\n    const obj = { path: url, meta: { title: title } }\n    store.dispatch('tagsView/addView', obj);\n    return router.push({ path: url, query: params });\n  },\n  // 修改tab页签\n  updatePage(obj) {\n    return store.dispatch('tagsView/updateVisitedView', obj);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA8B,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEf;EACb;EACAC,WAAW,WAAXA,WAAWA,CAACC,GAAG,EAAE;IACf,IAAAC,oBAAA,GAAiCC,eAAM,CAACC,YAAY;MAA5CC,IAAI,GAAAH,oBAAA,CAAJG,IAAI;MAAEC,KAAK,GAAAJ,oBAAA,CAALI,KAAK;MAAEC,OAAO,GAAAL,oBAAA,CAAPK,OAAO;IAC5B,IAAIN,GAAG,KAAKO,SAAS,EAAE;MACrBD,OAAO,CAACE,OAAO,CAAC,UAACC,CAAC,EAAK;QACrB,IAAIA,CAAC,CAACC,UAAU,IAAID,CAAC,CAACC,UAAU,CAACZ,OAAO,IAAIW,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI,EAAE;UACrE,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACH,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI,CAAC,EAAE;YACjEX,GAAG,GAAG;cAAEW,IAAI,EAAEF,CAAC,CAACC,UAAU,CAACZ,OAAO,CAACa,IAAI;cAAEP,IAAI,EAAEA,IAAI;cAAEC,KAAK,EAAEA;YAAM,CAAC;UACrE;QACF;MACF,CAAC,CAAC;IACJ;IACA,OAAOQ,cAAK,CAACC,QAAQ,CAAC,wBAAwB,EAAEd,GAAG,CAAC,CAACe,IAAI,CAAC,YAAM;MAC9D,IAAAC,IAAA,GAAwBhB,GAAG;QAAnBI,IAAI,GAAAY,IAAA,CAAJZ,IAAI;QAAEC,KAAK,GAAAW,IAAA,CAALX,KAAK;MACnBH,eAAM,CAACe,OAAO,CAAC;QACbb,IAAI,EAAE,WAAW,GAAGA,IAAI;QACxBC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAa,aAAa,WAAbA,aAAaA,CAAClB,GAAG,EAAE;IACjBa,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEZ,eAAM,CAACC,YAAY,CAAC;IACvD,IAAIH,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAOL,eAAM,CAACiB,IAAI,CAACnB,GAAG,CAAC;IACzB;EACF,CAAC;EACD;EACAoB,SAAS,WAATA,SAASA,CAACpB,GAAG,EAAE;IACb,IAAIA,GAAG,KAAKO,SAAS,EAAE;MACrB,OAAOM,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEZ,eAAM,CAACC,YAAY,CAAC,CAACY,IAAI,CAAC,UAAAM,IAAA,EAAsB;QAAA,IAAnBC,YAAY,GAAAD,IAAA,CAAZC,YAAY;QACjF,IAAMC,UAAU,GAAGD,YAAY,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAID,UAAU,EAAE;UACd,OAAOrB,eAAM,CAACiB,IAAI,CAACI,UAAU,CAACE,QAAQ,CAAC;QACzC;QACA,OAAOvB,eAAM,CAACiB,IAAI,CAAC,GAAG,CAAC;MACzB,CAAC,CAAC;IACJ;IACA,OAAON,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEd,GAAG,CAAC;EAChD,CAAC;EACD;EACA0B,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,OAAOb,cAAK,CAACC,QAAQ,CAAC,sBAAsB,CAAC;EAC/C,CAAC;EACD;EACAa,aAAa,WAAbA,aAAaA,CAAC3B,GAAG,EAAE;IACjB,OAAOa,cAAK,CAACC,QAAQ,CAAC,sBAAsB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC3E,CAAC;EACD;EACAyB,cAAc,WAAdA,cAAcA,CAAC5B,GAAG,EAAE;IAClB,OAAOa,cAAK,CAACC,QAAQ,CAAC,uBAAuB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC5E,CAAC;EACD;EACA0B,cAAc,WAAdA,cAAcA,CAAC7B,GAAG,EAAE;IAClB,OAAOa,cAAK,CAACC,QAAQ,CAAC,yBAAyB,EAAEd,GAAG,IAAIE,eAAM,CAACC,YAAY,CAAC;EAC9E,CAAC;EACD;EACA2B,QAAQ,WAARA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAE;IAC3B,IAAMjC,GAAG,GAAG;MAAEI,IAAI,EAAE4B,GAAG;MAAEE,IAAI,EAAE;QAAEH,KAAK,EAAEA;MAAM;IAAE,CAAC;IACjDlB,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAEd,GAAG,CAAC;IACvC,OAAOE,eAAM,CAACiB,IAAI,CAAC;MAAEf,IAAI,EAAE4B,GAAG;MAAE3B,KAAK,EAAE4B;IAAO,CAAC,CAAC;EAClD,CAAC;EACD;EACAE,UAAU,WAAVA,UAAUA,CAACnC,GAAG,EAAE;IACd,OAAOa,cAAK,CAACC,QAAQ,CAAC,4BAA4B,EAAEd,GAAG,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}]}