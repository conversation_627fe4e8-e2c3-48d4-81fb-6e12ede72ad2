{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=template&id=b84ac022&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1750056626712}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749172157834}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}