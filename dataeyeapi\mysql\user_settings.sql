-- 用户设置表
CREATE TABLE user_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    setting_type VARCHAR(50) NOT NULL COMMENT '设置类型(search_settings, analysis_settings)',
    setting_key VARCHAR(100) NOT NULL COMMENT '设置键',
    setting_value TEXT COMMENT '设置值(JSON格式)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
    UNIQUE KEY unique_user_setting (user_id, setting_type, setting_key)
) COMMENT='用户个性化设置表';

-- 插入默认设置数据
INSERT INTO user_settings (user_id, setting_type, setting_key, setting_value) VALUES
-- 用户1的搜索设置
(1, 'search_settings', 'smart_tips', 'true'),
(1, 'search_settings', 'try_suggestions', 'true'),
(1, 'search_settings', 'related_search', 'true'),
-- 用户1的智能解析设置
(1, 'analysis_settings', 'anomaly', 'true'),
(1, 'analysis_settings', 'maximum', 'true'),
(1, 'analysis_settings', 'minimum', 'true'),
(1, 'analysis_settings', 'average', 'true'),
(1, 'analysis_settings', 'discrete', 'true'),
(1, 'analysis_settings', 'bio_tag', 'true'),
(1, 'analysis_settings', 'chain_anomaly', 'true'),
(1, 'analysis_settings', 'cyclical_fluctuation', 'true'),
-- 用户2的搜索设置
(2, 'search_settings', 'smart_tips', 'false'),
(2, 'search_settings', 'try_suggestions', 'true'),
(2, 'search_settings', 'related_search', 'false'),
-- 用户2的智能解析设置
(2, 'analysis_settings', 'anomaly', 'false'),
(2, 'analysis_settings', 'maximum', 'true'),
(2, 'analysis_settings', 'minimum', 'false'),
(2, 'analysis_settings', 'average', 'true'),
(2, 'analysis_settings', 'discrete', 'false'),
(2, 'analysis_settings', 'bio_tag', 'false'),
(2, 'analysis_settings', 'chain_anomaly', 'true'),
(2, 'analysis_settings', 'cyclical_fluctuation', 'false');
