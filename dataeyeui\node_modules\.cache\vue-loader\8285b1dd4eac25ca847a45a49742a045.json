{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\dashboard\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBSYWRhciBmcm9tICIuL1JhZGFyLnZ1ZSI7CmltcG9ydCB7CiAgQXZhdGFyLAogIEJ1dHRvbiwKICBDYXJkLAogIENvbCwKICBMaXN0LAogIFJvdywKICBTdGF0aXN0aWMsCn0gZnJvbSAiYW50LWRlc2lnbi12dWUiOwppbXBvcnQgJ2FudC1kZXNpZ24tdnVlL2Rpc3QvYW50ZC5jc3MnOwppbXBvcnQgVnVlIGZyb20gInZ1ZSI7CgpWdWUuY29tcG9uZW50KEF2YXRhci5uYW1lLCBBdmF0YXIpOwpWdWUuY29tcG9uZW50KEJ1dHRvbi5uYW1lLCBCdXR0b24pOwpWdWUuY29tcG9uZW50KENhcmQubmFtZSwgQ2FyZCk7ClZ1ZS5jb21wb25lbnQoQ2FyZC5HcmlkLm5hbWUsIENhcmQuR3JpZCk7ClZ1ZS5jb21wb25lbnQoQ2FyZC5NZXRhLm5hbWUsIENhcmQuTWV0YSk7ClZ1ZS5jb21wb25lbnQoQ29sLm5hbWUsIENvbCk7ClZ1ZS5jb21wb25lbnQoTGlzdC5uYW1lLCBMaXN0KTsKVnVlLmNvbXBvbmVudChMaXN0Lkl0ZW0ubmFtZSwgTGlzdC5JdGVtKTsKVnVlLmNvbXBvbmVudChMaXN0Lkl0ZW0uTWV0YS5uYW1lLCBMaXN0Lkl0ZW0uTWV0YSk7ClZ1ZS5jb21wb25lbnQoUm93Lm5hbWUsIFJvdyk7ClZ1ZS5jb21wb25lbnQoU3RhdGlzdGljLm5hbWUsIFN0YXRpc3RpYyk7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRhc2hCb2FyZCIsCiAgY29tcG9uZW50czogewogICAgUmFkYXIsCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFVzZXI6IHsKICAgICAgICBhdmF0YXI6CiAgICAgICAgICAiaHR0cHM6Ly9ndy5hbGlwYXlvYmplY3RzLmNvbS96b3Mvcm1zcG9ydGFsL0JpYXpmYW54bWFtTlJveHhWeGthLnBuZyIsCiAgICAgICAgbmFtZTogIuWQtOW9puelliIsCiAgICAgICAgdXNlcmlkOiAiMDAwMDAwMDEiLAogICAgICAgIGVtYWlsOiAiYW50ZGVzaWduQGFsaXBheS5jb20iLAogICAgICAgIHNpZ25hdHVyZTogIua1t+e6s+eZvuW3ne+8jOacieWuueS5g+WkpyIsCiAgICAgICAgdGl0bGU6ICLkuqTkupLkuJPlrrYiLAogICAgICAgIGdyb3VwOiAi6JqC6JqB6YeR5pyN77yN5p+Q5p+Q5p+Q5LqL5Lia576k77yN5p+Q5p+Q5bmz5Y+w6YOo77yN5p+Q5p+Q5oqA5pyv6YOo77yNVUVEIiwKICAgICAgfSwKICAgICAgcHJvamVjdHM6IFsKICAgICAgICB7CiAgICAgICAgICBpZDogInh4eDEiLAogICAgICAgICAgdGl0bGU6ICJBbGlwYXkiLAogICAgICAgICAgbG9nbzogImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9XZEdxbUhwYXl5TWppRWhjS29WRS5wbmciLAogICAgICAgICAgZGVzY3JpcHRpb246ICLpgqPmmK/kuIDnp43lhoXlnKjnmoTkuJzopb/vvIzku5bku6zliLDovr7kuI3kuobvvIzkuZ/ml6Dms5Xop6blj4rnmoQiLAogICAgICAgICAgdXBkYXRlZEF0OiAi5Yeg56eS5YmNIiwKICAgICAgICAgIG1lbWJlcjogIuenkeWtpuaQrOeglue7hCIsCiAgICAgICAgICBocmVmOiAiIiwKICAgICAgICAgIG1lbWJlckxpbms6ICIiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICJ4eHgyIiwKICAgICAgICAgIHRpdGxlOiAiQW5ndWxhciIsCiAgICAgICAgICBsb2dvOiAiaHR0cHM6Ly9ndy5hbGlwYXlvYmplY3RzLmNvbS96b3Mvcm1zcG9ydGFsL3pPc0tabUZSZFV0dnBxQ0ltT1ZZLnBuZyIsCiAgICAgICAgICBkZXNjcmlwdGlvbjogIuW4jOacm+aYr+S4gOS4quWlveS4nOilv++8jOS5n+iuuOaYr+acgOWlveeahO+8jOWlveS4nOilv+aYr+S4jeS8mua2iOS6oeeahCIsCiAgICAgICAgICB1cGRhdGVkQXQ6ICI2IOW5tOWJjSIsCiAgICAgICAgICBtZW1iZXI6ICLlhajnu4Tpg73mmK/lkLTlvabnpZYiLAogICAgICAgICAgaHJlZjogIiIsCiAgICAgICAgICBtZW1iZXJMaW5rOiAiIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAieHh4MyIsCiAgICAgICAgICB0aXRsZTogIkFudCBEZXNpZ24iLAogICAgICAgICAgbG9nbzogImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9kVVJJTWtrclJGcFBnVHV6a3duQi5wbmciLAogICAgICAgICAgZGVzY3JpcHRpb246ICLln47plYfkuK3mnInpgqPkuYjlpJrnmoTphZLppobvvIzlpbnljbTlgY/lgY/otbDov5vkuobmiJHnmoTphZLppoYiLAogICAgICAgICAgdXBkYXRlZEF0OiAi5Yeg56eS5YmNIiwKICAgICAgICAgIG1lbWJlcjogIuS4reS6jOWwkeWls+WboiIsCiAgICAgICAgICBocmVmOiAiIiwKICAgICAgICAgIG1lbWJlckxpbms6ICIiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICJ4eHg0IiwKICAgICAgICAgIHRpdGxlOiAiQW50IERlc2lnbiBQcm8iLAogICAgICAgICAgbG9nbzogImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9zZmpiT3Fuc1hYSmdOQ2pDekRCTC5wbmciLAogICAgICAgICAgZGVzY3JpcHRpb246ICLpgqPml7blgJnmiJHlj6rkvJrmg7Poh6rlt7Hmg7PopoHku4DkuYjvvIzku47kuI3mg7Poh6rlt7Hmi6XmnInku4DkuYgiLAogICAgICAgICAgdXBkYXRlZEF0OiAiNiDlubTliY0iLAogICAgICAgICAgbWVtYmVyOiAi56iL5bqP5ZGY5pel5bi4IiwKICAgICAgICAgIGhyZWY6ICIiLAogICAgICAgICAgbWVtYmVyTGluazogIiIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogInh4eDUiLAogICAgICAgICAgdGl0bGU6ICJCb290c3RyYXAiLAogICAgICAgICAgbG9nbzogImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9zaUNyQlhYaG12VFFHV1BOTEJvdy5wbmciLAogICAgICAgICAgZGVzY3JpcHRpb246ICLlh5vlhqzlsIboh7MiLAogICAgICAgICAgdXBkYXRlZEF0OiAiNiDlubTliY0iLAogICAgICAgICAgbWVtYmVyOiAi6auY6YC85qC86K6+6K6h5aSp5ZuiIiwKICAgICAgICAgIGhyZWY6ICIiLAogICAgICAgICAgbWVtYmVyTGluazogIiIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogInh4eDYiLAogICAgICAgICAgdGl0bGU6ICJSZWFjdCIsCiAgICAgICAgICBsb2dvOiAiaHR0cHM6Ly9ndy5hbGlwYXlvYmplY3RzLmNvbS96b3Mvcm1zcG9ydGFsL2taekV6ZW1aeUtMS0Zzb2pYSXRFLnBuZyIsCiAgICAgICAgICBkZXNjcmlwdGlvbjogIueUn+WRveWwseWDj+S4gOebkuW3p+WFi+WKm++8jOe7k+aenOW+gOW+gOWHuuS6uuaEj+aWmSIsCiAgICAgICAgICB1cGRhdGVkQXQ6ICI2IOW5tOWJjSIsCiAgICAgICAgICBtZW1iZXI6ICLpqpfkvaDmnaXlraborqHnrpfmnLoiLAogICAgICAgICAgaHJlZjogIiIsCiAgICAgICAgICBtZW1iZXJMaW5rOiAiIiwKICAgICAgICB9LAogICAgICBdLAogICAgICBhY3Rpdml0aWVzOiBbCiAgICAgICAgewogICAgICAgICAgaWQ6ICJ0cmVuZC0xIiwKICAgICAgICAgIHVwZGF0ZWRBdDogIuWHoOenkuWJjSIsCiAgICAgICAgICB1c2VyOiB7CiAgICAgICAgICAgIG5hbWU6ICLmm7LkuL3kuL0iLAogICAgICAgICAgICBhdmF0YXI6CiAgICAgICAgICAgICAgImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9CaWF6ZmFueG1hbU5Sb3h4VnhrYS5wbmciLAogICAgICAgICAgfSwKICAgICAgICAgIGdyb3VwOiB7CiAgICAgICAgICAgIG5hbWU6ICLpq5jpgLzmoLzorr7orqHlpKnlm6IiLAogICAgICAgICAgICBsaW5rOiAiaHR0cDovL2dpdGh1Yi5jb20vIiwKICAgICAgICAgIH0sCiAgICAgICAgICBwcm9qZWN0OiB7CiAgICAgICAgICAgIG5hbWU6ICLlha3mnIjov63ku6MiLAogICAgICAgICAgICBsaW5rOiAiaHR0cDovL2dpdGh1Yi5jb20vIiwKICAgICAgICAgIH0sCiAgICAgICAgICB0ZW1wbGF0ZTE6ICLlnKgiLAogICAgICAgICAgdGVtcGxhdGUyOiAi5paw5bu66aG555uuIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAidHJlbmQtMiIsCiAgICAgICAgICB1cGRhdGVkQXQ6ICLlh6Dnp5LliY0iLAogICAgICAgICAgdXNlcjogewogICAgICAgICAgICBuYW1lOiAi5LuY5bCP5bCPIiwKICAgICAgICAgICAgYXZhdGFyOgogICAgICAgICAgICAgICJodHRwczovL2d3LmFsaXBheW9iamVjdHMuY29tL3pvcy9ybXNwb3J0YWwvY25yaFZrend4alB3QWFDZlBiZGMucG5nIiwKICAgICAgICAgIH0sCiAgICAgICAgICBncm91cDogewogICAgICAgICAgICBuYW1lOiAi6auY6YC85qC86K6+6K6h5aSp5ZuiIiwKICAgICAgICAgICAgbGluazogImh0dHA6Ly9naXRodWIuY29tLyIsCiAgICAgICAgICB9LAogICAgICAgICAgcHJvamVjdDogewogICAgICAgICAgICBuYW1lOiAi5YWt5pyI6L+t5LujIiwKICAgICAgICAgICAgbGluazogImh0dHA6Ly9naXRodWIuY29tLyIsCiAgICAgICAgICB9LAogICAgICAgICAgdGVtcGxhdGUxOiAi5ZyoIiwKICAgICAgICAgIHRlbXBsYXRlMjogIuaWsOW7uumhueebriIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogInRyZW5kLTMiLAogICAgICAgICAgdXBkYXRlZEF0OiAi5Yeg56eS5YmNIiwKICAgICAgICAgIHVzZXI6IHsKICAgICAgICAgICAgbmFtZTogIuael+S4nOS4nCIsCiAgICAgICAgICAgIGF2YXRhcjoKICAgICAgICAgICAgICAiaHR0cHM6Ly9ndy5hbGlwYXlvYmplY3RzLmNvbS96b3Mvcm1zcG9ydGFsL2dhT25nSndzUllSYVZBdVhYY21CLnBuZyIsCiAgICAgICAgICB9LAogICAgICAgICAgZ3JvdXA6IHsKICAgICAgICAgICAgbmFtZTogIuS4reS6jOWwkeWls+WboiIsCiAgICAgICAgICAgIGxpbms6ICJodHRwOi8vZ2l0aHViLmNvbS8iLAogICAgICAgICAgfSwKICAgICAgICAgIHByb2plY3Q6IHsKICAgICAgICAgICAgbmFtZTogIuWFreaciOi/reS7oyIsCiAgICAgICAgICAgIGxpbms6ICJodHRwOi8vZ2l0aHViLmNvbS8iLAogICAgICAgICAgfSwKICAgICAgICAgIHRlbXBsYXRlMTogIuWcqCIsCiAgICAgICAgICB0ZW1wbGF0ZTI6ICLmlrDlu7rpobnnm64iLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICJ0cmVuZC00IiwKICAgICAgICAgIHVwZGF0ZWRBdDogIuWHoOenkuWJjSIsCiAgICAgICAgICB1c2VyOiB7CiAgICAgICAgICAgIG5hbWU6ICLlkajmmJ/mmJ8iLAogICAgICAgICAgICBhdmF0YXI6CiAgICAgICAgICAgICAgImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC9XaHhLRUNQTnVqV29XRUZOZG5KRS5wbmciLAogICAgICAgICAgfSwKICAgICAgICAgIGdyb3VwOiB7CiAgICAgICAgICAgIG5hbWU6ICI1IOaciOaXpeW4uOi/reS7oyIsCiAgICAgICAgICAgIGxpbms6ICJodHRwOi8vZ2l0aHViLmNvbS8iLAogICAgICAgICAgfSwKICAgICAgICAgIHRlbXBsYXRlMTogIuWwhiIsCiAgICAgICAgICB0ZW1wbGF0ZTI6ICLmm7TmlrDoh7Plt7Llj5HluIPnirbmgIEiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICJ0cmVuZC01IiwKICAgICAgICAgIHVwZGF0ZWRBdDogIuWHoOenkuWJjSIsCiAgICAgICAgICB1c2VyOiB7CiAgICAgICAgICAgIG5hbWU6ICLmnLHlgY/lj7MiLAogICAgICAgICAgICBhdmF0YXI6CiAgICAgICAgICAgICAgImh0dHBzOi8vZ3cuYWxpcGF5b2JqZWN0cy5jb20vem9zL3Jtc3BvcnRhbC91Ym5LU0lmQUpUeElnWE9LbGNpTi5wbmciLAogICAgICAgICAgfSwKICAgICAgICAgIGdyb3VwOiB7CiAgICAgICAgICAgIG5hbWU6ICLlt6XnqIvmlYjog70iLAogICAgICAgICAgICBsaW5rOiAiaHR0cDovL2dpdGh1Yi5jb20vIiwKICAgICAgICAgIH0sCiAgICAgICAgICBwcm9qZWN0OiB7CiAgICAgICAgICAgIG5hbWU6ICLnlZnoqIAiLAogICAgICAgICAgICBsaW5rOiAiaHR0cDovL2dpdGh1Yi5jb20vIiwKICAgICAgICAgIH0sCiAgICAgICAgICB0ZW1wbGF0ZTE6ICLlnKgiLAogICAgICAgICAgdGVtcGxhdGUyOiAi5Y+R5biD5LqGIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAidHJlbmQtNiIsCiAgICAgICAgICB1cGRhdGVkQXQ6ICLlh6Dnp5LliY0iLAogICAgICAgICAgdXNlcjogewogICAgICAgICAgICBuYW1lOiAi5LmQ5ZOlIiwKICAgICAgICAgICAgYXZhdGFyOgogICAgICAgICAgICAgICJodHRwczovL2d3LmFsaXBheW9iamVjdHMuY29tL3pvcy9ybXNwb3J0YWwvalpVSXhtSnljb3ltQnByTE9VYlQucG5nIiwKICAgICAgICAgIH0sCiAgICAgICAgICBncm91cDogewogICAgICAgICAgICBuYW1lOiAi56iL5bqP5ZGY5pel5bi4IiwKICAgICAgICAgICAgbGluazogImh0dHA6Ly9naXRodWIuY29tLyIsCiAgICAgICAgICB9LAogICAgICAgICAgcHJvamVjdDogewogICAgICAgICAgICBuYW1lOiAi5ZOB54mM6L+t5LujIiwKICAgICAgICAgICAgbGluazogImh0dHA6Ly9naXRodWIuY29tLyIsCiAgICAgICAgICB9LAogICAgICAgICAgdGVtcGxhdGUxOiAi5ZyoIiwKICAgICAgICAgIHRlbXBsYXRlMjogIuaWsOW7uumhueebriIsCiAgICAgICAgfSwKICAgICAgXSwKICAgICAgcmFkYXJEYXRhOiBbCiAgICAgICAgewogICAgICAgICAgaXRlbTogIuW8leeUqCIsCiAgICAgICAgICB1c2VyOiAi5Liq5Lq6IiwKICAgICAgICAgIHNjb3JlOiA3MCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGl0ZW06ICLlvJXnlKgiLAogICAgICAgICAgdXNlcjogIuWboumYnyIsCiAgICAgICAgICBzY29yZTogMzAsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpdGVtOiAi5byV55SoIiwKICAgICAgICAgIHVzZXI6ICLpg6jpl6giLAogICAgICAgICAgc2NvcmU6IDQwLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaXRlbTogIuWPo+eikSIsCiAgICAgICAgICB1c2VyOiAi5Liq5Lq6IiwKICAgICAgICAgIHNjb3JlOiA2MCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGl0ZW06ICLlj6PnopEiLAogICAgICAgICAgdXNlcjogIuWboumYnyIsCiAgICAgICAgICBzY29yZTogNzAsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpdGVtOiAi5Y+j56KRIiwKICAgICAgICAgIHVzZXI6ICLpg6jpl6giLAogICAgICAgICAgc2NvcmU6IDQwLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaXRlbTogIuS6p+mHjyIsCiAgICAgICAgICB1c2VyOiAi5Liq5Lq6IiwKICAgICAgICAgIHNjb3JlOiA1MCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGl0ZW06ICLkuqfph48iLAogICAgICAgICAgdXNlcjogIuWboumYnyIsCiAgICAgICAgICBzY29yZTogNjAsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpdGVtOiAi5Lqn6YePIiwKICAgICAgICAgIHVzZXI6ICLpg6jpl6giLAogICAgICAgICAgc2NvcmU6IDQwLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaXRlbTogIui0oeeMriIsCiAgICAgICAgICB1c2VyOiAi5Liq5Lq6IiwKICAgICAgICAgIHNjb3JlOiA0MCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGl0ZW06ICLotKHnjK4iLAogICAgICAgICAgdXNlcjogIuWboumYnyIsCiAgICAgICAgICBzY29yZTogNTAsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpdGVtOiAi6LSh54yuIiwKICAgICAgICAgIHVzZXI6ICLpg6jpl6giLAogICAgICAgICAgc2NvcmU6IDQwLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaXRlbTogIueDreW6piIsCiAgICAgICAgICB1c2VyOiAi5Liq5Lq6IiwKICAgICAgICAgIHNjb3JlOiA2MCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGl0ZW06ICLng63luqYiLAogICAgICAgICAgdXNlcjogIuWboumYnyIsCiAgICAgICAgICBzY29yZTogNzAsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpdGVtOiAi54Ot5bqmIiwKICAgICAgICAgIHVzZXI6ICLpg6jpl6giLAogICAgICAgICAgc2NvcmU6IDQwLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaXRlbTogIuW8leeUqCIsCiAgICAgICAgICB1c2VyOiAi5Liq5Lq6IiwKICAgICAgICAgIHNjb3JlOiA3MCwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGl0ZW06ICLlvJXnlKgiLAogICAgICAgICAgdXNlcjogIuWboumYnyIsCiAgICAgICAgICBzY29yZTogNTAsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpdGVtOiAi5byV55SoIiwKICAgICAgICAgIHVzZXI6ICLpg6jpl6giLAogICAgICAgICAgc2NvcmU6IDQwLAogICAgICAgIH0sCiAgICAgIF0sCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIHJhZGFyTG9hZGluZzogdHJ1ZSwKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLnJhZGFyTG9hZGluZyA9IGZhbHNlOwogICAgfSwgMTAwMCk7CiAgfSwKfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div>\n    <div class=\"page-header-content\">\n      <div class=\"avatar\">\n        <a-avatar size=\"large\" :src=\"currentUser.avatar\" />\n      </div>\n      <div class=\"content\">\n        <div class=\"content-title\">\n          早安，{{ currentUser.name\n          }}<span class=\"welcome-text\">，祝你开心每一天！</span>\n        </div>\n        <div>{{ currentUser.title }} |{{ currentUser.group }}</div>\n      </div>\n      <div class=\"extra-content\">\n        <div class=\"stat-item\">\n          <a-statistic title=\"项目数\" :value=\"56\" />\n        </div>\n        <div class=\"stat-item\">\n          <a-statistic title=\"团队内排名\" :value=\"8\" suffix=\"/ 24\" />\n        </div>\n        <div class=\"stat-item\">\n          <a-statistic title=\"项目访问\" :value=\"2223\" />\n        </div>\n      </div>\n    </div>\n\n    <div>\n      <a-row :gutter=\"24\">\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n          <a-card\n            class=\"project-list\"\n            :loading=\"loading\"\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n            title=\"进行中的项目\"\n            :body-style=\"{ padding: 0 }\"\n          >\n            <a slot=\"extra\">全部项目</a>\n            <div>\n              <a-card-grid\n                class=\"project-card-grid\"\n                :key=\"i\"\n                v-for=\"(item, i) in projects\"\n              >\n                <a-card :bordered=\"false\" :body-style=\"{ padding: 0 }\">\n                  <a-card-meta>\n                    <div slot=\"title\" class=\"card-title\">\n                      <a-avatar size=\"small\" :src=\"item.logo\" />\n                      <a>{{ item.title }}</a>\n                    </div>\n                    <div slot=\"description\" class=\"card-description\">\n                      {{ item.description }}\n                    </div>\n                  </a-card-meta>\n                  <div class=\"project-item\">\n                    <a href=\"/#/\">{{ item.member || \"\" }}</a>\n                    <span class=\"datetime\">{{ item.updatedAt }}</span>\n                  </div>\n                </a-card>\n              </a-card-grid>\n            </div>\n          </a-card>\n\n          <a-card :loading=\"loading\" title=\"动态\" :bordered=\"false\">\n            <a-list>\n              <a-list-item :key=\"index\" v-for=\"(item, index) in activities\">\n                <a-list-item-meta>\n                  <a-avatar\n                    slot=\"avatar\"\n                    size=\"small\"\n                    :src=\"item.user.avatar\"\n                  />\n                  <div slot=\"title\">\n                    <span>{{ item.user.name }}</span\n                    >&nbsp; {{ item.template1 }}&nbsp;<a href=\"#\">{{\n                      item.group && item.group.name\n                    }}</a\n                    >&nbsp; <span>{{ item.template2 }}</span\n                    >&nbsp;\n                    <a :href=\"item.project && item.project.link\">{{\n                      item.project && item.project.name\n                    }}</a>\n                  </div>\n                  <div slot=\"description\">{{ item.updatedAt }}</div>\n                </a-list-item-meta>\n              </a-list-item>\n            </a-list>\n          </a-card>\n        </a-col>\n        <a-col\n          style=\"padding: 0 12px\"\n          :xl=\"8\"\n          :lg=\"24\"\n          :md=\"24\"\n          :sm=\"24\"\n          :xs=\"24\"\n        >\n          <a-card\n            title=\"快速开始 / 便捷导航\"\n            style=\"margin-bottom: 24px\"\n            :bordered=\"false\"\n            :body-style=\"{ padding: 0 }\"\n          >\n            <div class=\"item-group\">\n              <a>操作一</a>\n              <a>操作二</a>\n              <a>操作三</a>\n              <a>操作四</a>\n              <a>操作五</a>\n              <a>操作六</a>\n              <a-button size=\"small\" type=\"primary\" ghost icon=\"plus\"\n                >添加</a-button\n              >\n            </div>\n          </a-card>\n          <a-card\n            title=\"XX 指数\"\n            style=\"margin-bottom: 24px\"\n            :loading=\"radarLoading\"\n            :bordered=\"false\"\n            :body-style=\"{ padding: 0 }\"\n          >\n            <div style=\"min-height: 400px\">\n              <radar :data=\"radarData\" />\n            </div>\n          </a-card>\n          <a-card :loading=\"loading\" title=\"团队\" :bordered=\"false\">\n            <div class=\"members\">\n              <a-row>\n                <a-col\n                  :span=\"12\"\n                  v-for=\"(item, index) in projects\"\n                  :key=\"index\"\n                >\n                  <a>\n                    <a-avatar size=\"small\" :src=\"item.logo\" />\n                    <span class=\"member\">{{ item.member }}</span>\n                  </a>\n                </a-col>\n              </a-row>\n            </div>\n          </a-card>\n        </a-col>\n      </a-row>\n    </div>\n  </div>\n</template>\n  \n<script>\nimport Radar from \"./Radar.vue\";\nimport {\n  Avatar,\n  Button,\n  Card,\n  Col,\n  List,\n  Row,\n  Statistic,\n} from \"ant-design-vue\";\nimport 'ant-design-vue/dist/antd.css';\nimport Vue from \"vue\";\n\nVue.component(Avatar.name, Avatar);\nVue.component(Button.name, Button);\nVue.component(Card.name, Card);\nVue.component(Card.Grid.name, Card.Grid);\nVue.component(Card.Meta.name, Card.Meta);\nVue.component(Col.name, Col);\nVue.component(List.name, List);\nVue.component(List.Item.name, List.Item);\nVue.component(List.Item.Meta.name, List.Item.Meta);\nVue.component(Row.name, Row);\nVue.component(Statistic.name, Statistic);\n\nexport default {\n  name: \"DashBoard\",\n  components: {\n    Radar,\n  },\n  data() {\n    return {\n      currentUser: {\n        avatar:\n          \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\n        name: \"吴彦祖\",\n        userid: \"00000001\",\n        email: \"<EMAIL>\",\n        signature: \"海纳百川，有容乃大\",\n        title: \"交互专家\",\n        group: \"蚂蚁金服－某某某事业群－某某平台部－某某技术部－UED\",\n      },\n      projects: [\n        {\n          id: \"xxx1\",\n          title: \"Alipay\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png\",\n          description: \"那是一种内在的东西，他们到达不了，也无法触及的\",\n          updatedAt: \"几秒前\",\n          member: \"科学搬砖组\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx2\",\n          title: \"Angular\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png\",\n          description: \"希望是一个好东西，也许是最好的，好东西是不会消亡的\",\n          updatedAt: \"6 年前\",\n          member: \"全组都是吴彦祖\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx3\",\n          title: \"Ant Design\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png\",\n          description: \"城镇中有那么多的酒馆，她却偏偏走进了我的酒馆\",\n          updatedAt: \"几秒前\",\n          member: \"中二少女团\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx4\",\n          title: \"Ant Design Pro\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png\",\n          description: \"那时候我只会想自己想要什么，从不想自己拥有什么\",\n          updatedAt: \"6 年前\",\n          member: \"程序员日常\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx5\",\n          title: \"Bootstrap\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png\",\n          description: \"凛冬将至\",\n          updatedAt: \"6 年前\",\n          member: \"高逼格设计天团\",\n          href: \"\",\n          memberLink: \"\",\n        },\n        {\n          id: \"xxx6\",\n          title: \"React\",\n          logo: \"https://gw.alipayobjects.com/zos/rmsportal/kZzEzemZyKLKFsojXItE.png\",\n          description: \"生命就像一盒巧克力，结果往往出人意料\",\n          updatedAt: \"6 年前\",\n          member: \"骗你来学计算机\",\n          href: \"\",\n          memberLink: \"\",\n        },\n      ],\n      activities: [\n        {\n          id: \"trend-1\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"曲丽丽\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\n          },\n          group: {\n            name: \"高逼格设计天团\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"六月迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n        {\n          id: \"trend-2\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"付小小\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png\",\n          },\n          group: {\n            name: \"高逼格设计天团\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"六月迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n        {\n          id: \"trend-3\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"林东东\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png\",\n          },\n          group: {\n            name: \"中二少女团\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"六月迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n        {\n          id: \"trend-4\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"周星星\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png\",\n          },\n          group: {\n            name: \"5 月日常迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"将\",\n          template2: \"更新至已发布状态\",\n        },\n        {\n          id: \"trend-5\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"朱偏右\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png\",\n          },\n          group: {\n            name: \"工程效能\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"留言\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"发布了\",\n        },\n        {\n          id: \"trend-6\",\n          updatedAt: \"几秒前\",\n          user: {\n            name: \"乐哥\",\n            avatar:\n              \"https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png\",\n          },\n          group: {\n            name: \"程序员日常\",\n            link: \"http://github.com/\",\n          },\n          project: {\n            name: \"品牌迭代\",\n            link: \"http://github.com/\",\n          },\n          template1: \"在\",\n          template2: \"新建项目\",\n        },\n      ],\n      radarData: [\n        {\n          item: \"引用\",\n          user: \"个人\",\n          score: 70,\n        },\n        {\n          item: \"引用\",\n          user: \"团队\",\n          score: 30,\n        },\n        {\n          item: \"引用\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"口碑\",\n          user: \"个人\",\n          score: 60,\n        },\n        {\n          item: \"口碑\",\n          user: \"团队\",\n          score: 70,\n        },\n        {\n          item: \"口碑\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"产量\",\n          user: \"个人\",\n          score: 50,\n        },\n        {\n          item: \"产量\",\n          user: \"团队\",\n          score: 60,\n        },\n        {\n          item: \"产量\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"贡献\",\n          user: \"个人\",\n          score: 40,\n        },\n        {\n          item: \"贡献\",\n          user: \"团队\",\n          score: 50,\n        },\n        {\n          item: \"贡献\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"热度\",\n          user: \"个人\",\n          score: 60,\n        },\n        {\n          item: \"热度\",\n          user: \"团队\",\n          score: 70,\n        },\n        {\n          item: \"热度\",\n          user: \"部门\",\n          score: 40,\n        },\n        {\n          item: \"引用\",\n          user: \"个人\",\n          score: 70,\n        },\n        {\n          item: \"引用\",\n          user: \"团队\",\n          score: 50,\n        },\n        {\n          item: \"引用\",\n          user: \"部门\",\n          score: 40,\n        },\n      ],\n      loading: true,\n      radarLoading: true,\n    };\n  },\n  mounted() {\n    setTimeout(() => {\n      this.loading = false;\n      this.radarLoading = false;\n    }, 1000);\n  },\n};\n</script>\n  \n  <style lang=\"less\" scoped>\n@import \"./Workplace.less\";\n\n.project-list {\n  .card-title {\n    font-size: 0;\n\n    a {\n      color: rgba(0, 0, 0, 0.85);\n      margin-left: 12px;\n      line-height: 24px;\n      height: 24px;\n      display: inline-block;\n      vertical-align: top;\n      font-size: 14px;\n\n      &:hover {\n        color: #1890ff;\n      }\n    }\n  }\n\n  .card-description {\n    color: rgba(0, 0, 0, 0.45);\n    height: 44px;\n    line-height: 22px;\n    overflow: hidden;\n  }\n\n  .project-item {\n    display: flex;\n    margin-top: 8px;\n    overflow: hidden;\n    font-size: 12px;\n    height: 20px;\n    line-height: 20px;\n\n    a {\n      color: rgba(0, 0, 0, 0.45);\n      display: inline-block;\n      flex: 1 1 0;\n\n      &:hover {\n        color: #1890ff;\n      }\n    }\n\n    .datetime {\n      color: rgba(0, 0, 0, 0.25);\n      flex: 0 0 auto;\n      float: right;\n    }\n  }\n\n  .ant-card-meta-description {\n    color: rgba(0, 0, 0, 0.45);\n    height: 44px;\n    line-height: 22px;\n    overflow: hidden;\n  }\n}\n\n.item-group {\n  padding: 20px 0 8px 24px;\n  font-size: 0;\n\n  a {\n    color: rgba(0, 0, 0, 0.65);\n    display: inline-block;\n    font-size: 14px;\n    margin-bottom: 13px;\n    width: 25%;\n  }\n}\n\n.members {\n  a {\n    display: block;\n    margin: 12px 0;\n    line-height: 24px;\n    height: 24px;\n\n    .member {\n      font-size: 14px;\n      color: rgba(0, 0, 0, 0.65);\n      line-height: 24px;\n      max-width: 100px;\n      vertical-align: top;\n      margin-left: 12px;\n      transition: all 0.3s;\n      display: inline-block;\n    }\n\n    &:hover {\n      span {\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n.mobile {\n  .project-list {\n    .project-card-grid {\n      width: 100%;\n    }\n  }\n\n  .more-info {\n    border: 0;\n    padding-top: 16px;\n    margin: 16px 0 16px;\n  }\n\n  .headerContent .title .welcome-text {\n    display: none;\n  }\n}\n</style>\n  "]}]}