<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="showCreateDialog">新建</el-button>
        <el-button size="small" @click="showTemplateLibrary">模板库</el-button>
        <el-button size="small" @click="showTagManageDialog">标签管理</el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="输入关键词搜索"
          prefix-icon="el-icon-search"
          size="small"
          style="width: 200px;"
          clearable
        />
      </div>
    </div>

    <!-- 报告表格 -->
    <div class="report-table">
      <el-table :data="filteredReports" style="width: 100%" size="small">
        <el-table-column prop="name" label="报告名称" min-width="250">
          <template slot-scope="scope">
            <div class="report-name">
              <i class="el-icon-document report-icon"></i>
              <span class="report-title">{{ scope.row.name }}</span>
              <el-tag v-if="scope.row.isNew" size="mini" type="primary">新</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="owner" label="负责人" width="120">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar :size="20" :src="scope.row.ownerAvatar">
                {{ scope.row.owner.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ scope.row.owner }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lastEditor" label="最后更新人" width="140">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar :size="20" :src="scope.row.lastEditorAvatar">
                {{ scope.row.lastEditor.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ scope.row.lastEditor }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'edit', report: scope.row}">编辑</el-dropdown-item>
                <el-dropdown-item :command="{action: 'share', report: scope.row}">分享</el-dropdown-item>
                <el-dropdown-item :command="{action: 'copy', report: scope.row}">复制</el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', report: scope.row}" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新建报告对话框 -->
    <el-dialog title="新建报告" :visible.sync="createDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form :model="createForm" :rules="createRules" ref="createForm" label-width="60px">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入报告名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入报告简介"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input
            v-model="createForm.tags"
            placeholder="请输入标签信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelCreate">取消</el-button>
        <el-button type="primary" @click="confirmCreate">确定</el-button>
      </div>
    </el-dialog>

    <!-- 标签管理对话框 -->
    <el-dialog title="标签管理" :visible.sync="tagManageDialogVisible" width="600px" :close-on-click-modal="false">
      <div class="tag-manage-content">
        <!-- 搜索框 -->
        <div class="tag-search">
          <el-input
            v-model="tagSearchKeyword"
            placeholder="输入关键词搜索"
            prefix-icon="el-icon-search"
            size="small"
            style="width: 300px;"
            clearable
          />
        </div>

        <!-- 标签表格 -->
        <div class="tag-table">
          <el-table :data="filteredTags" style="width: 100%" size="small">
            <el-table-column prop="name" label="标签名" min-width="120" />
            <el-table-column prop="reportCount" label="关联报告数" width="120" align="center" />
            <el-table-column prop="modifyType" label="修改方式" width="100" align="center" />
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="editTag(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="deleteTag(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTags.length === 0" class="tag-empty-state">
          <div class="empty-icon">
            <i class="el-icon-price-tag"></i>
          </div>
          <p>暂无数据</p>
        </div>
      </div>
    </el-dialog>

    <!-- 模板库抽屉 -->
    <el-drawer
      title="模板库"
      :visible.sync="templateLibraryVisible"
      direction="btt"
      size="90%"
      :close-on-click-modal="false"
      class="template-library-drawer"
    >
      <div class="template-library-content">
        <!-- 左侧模板列表 -->
        <div class="template-sidebar">
          <div class="template-search">
            <el-input
              v-model="templateSearchKeyword"
              placeholder="输入关键词搜索"
              prefix-icon="el-icon-search"
              size="small"
              clearable
            />
          </div>

          <div class="template-categories">
            <div class="category-title">模板分类</div>
            <div class="template-list">
              <div
                v-for="template in filteredTemplates"
                :key="template.id"
                :class="['template-item', { active: selectedTemplate && selectedTemplate.id === template.id }]"
                @click="selectTemplate(template)"
              >
                <div class="template-name">
                  {{ template.name }}
                  <el-tag v-if="template.isNew" type="primary" size="mini">新</el-tag>
                </div>
                <div class="template-desc">{{ template.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧模板预览 -->
        <div class="template-preview">
          <div v-if="selectedTemplate" class="preview-content">
            <div class="preview-header">
              <h3>{{ selectedTemplate.name }}</h3>
              <el-button type="primary" size="small" @click="useTemplate">使用模板</el-button>
            </div>
            <div class="preview-body">
              <div class="template-preview-image">
                <img :src="selectedTemplate.previewImage" :alt="selectedTemplate.name" />
              </div>
            </div>
          </div>
          <div v-else class="preview-empty">
            <div class="empty-icon">
              <i class="el-icon-document"></i>
            </div>
            <p>请选择一个模板查看预览</p>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'ReportRecent',
  data() {
    return {
      searchKeyword: '',
      createDialogVisible: false,
      createForm: {
        name: '',
        description: '',
        tags: ''
      },
      createRules: {
        name: [
          { required: true, message: '请输入报告名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      tagManageDialogVisible: false,
      tagSearchKeyword: '',
      tags: [],
      templateLibraryVisible: false,
      templateSearchKeyword: '',
      selectedTemplate: null,
      templates: [
        {
          id: 1,
          name: '人工智能发展趋势分析报告1',
          description: '一个AI报告',
          isNew: true,
          previewImage: '/static/template-preview-placeholder.svg',
          category: 'AI分析'
        },
        {
          id: 2,
          name: '人工智能发展趋势',
          description: 'AI发展趋势分析',
          isNew: false,
          previewImage: '/static/template-preview-placeholder.svg',
          category: 'AI分析'
        },
        {
          id: 3,
          name: '对于AI',
          description: 'AI相关报告模板',
          isNew: false,
          previewImage: '/static/template-preview-placeholder.svg',
          category: 'AI分析'
        }
      ],
      reports: [
        {
          id: 27,
          name: '我的问题测试1',
          owner: '王明明1',
          ownerAvatar: '',
          lastEditor: '',
          lastEditorAvatar: '',
          updateTime: '昨天 14:29:29',
          isNew: false
        },
        {
          id: 1,
          name: '关于AI',
          owner: 'huichengcheng',
          ownerAvatar: '',
          lastEditor: 'huichengcheng',
          lastEditorAvatar: '',
          updateTime: '2024年12月4日 15:14:50',
          isNew: true
        },
        {
          id: 2,
          name: '人工智能发展趋势',
          owner: 'huichengcheng',
          ownerAvatar: '',
          lastEditor: 'huichengcheng',
          lastEditorAvatar: '',
          updateTime: '2024年12月4日 15:14:50',
          isNew: true
        },
        {
          id: 3,
          name: '人工智能发展趋势分析报告1',
          owner: 'huichengcheng',
          ownerAvatar: '',
          lastEditor: 'huichengcheng',
          lastEditorAvatar: '',
          updateTime: '2024年12月4日 15:14:50',
          isNew: true
        }
      ]
    }
  },
  computed: {
    filteredReports() {
      if (!this.searchKeyword) {
        return this.reports
      }
      return this.reports.filter(report =>
        report.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        report.owner.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        report.lastEditor.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },
    filteredTags() {
      if (!this.tagSearchKeyword) {
        return this.tags
      }
      return this.tags.filter(tag =>
        tag.name.toLowerCase().includes(this.tagSearchKeyword.toLowerCase())
      )
    },
    filteredTemplates() {
      if (!this.templateSearchKeyword) {
        return this.templates
      }
      return this.templates.filter(template =>
        template.name.toLowerCase().includes(this.templateSearchKeyword.toLowerCase()) ||
        template.description.toLowerCase().includes(this.templateSearchKeyword.toLowerCase())
      )
    }
  },
  methods: {
    showCreateDialog() {
      this.createDialogVisible = true
    },
    cancelCreate() {
      this.createDialogVisible = false
      this.resetCreateForm()
    },
    confirmCreate() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 创建新报告
          const newReport = {
            id: Date.now(),
            name: this.createForm.name,
            owner: '当前用户',
            ownerAvatar: '',
            lastEditor: '当前用户',
            lastEditorAvatar: '',
            updateTime: new Date().toLocaleString('zh-CN'),
            isNew: true
          }
          this.reports.unshift(newReport)
          this.$message.success('报告创建成功')
          this.createDialogVisible = false
          this.resetCreateForm()
        }
      })
    },
    resetCreateForm() {
      this.createForm = {
        name: '',
        description: '',
        tags: ''
      }
      this.$nextTick(() => {
        this.$refs.createForm && this.$refs.createForm.clearValidate()
      })
    },
    showTagManageDialog() {
      this.tagManageDialogVisible = true
    },
    showTemplateLibrary() {
      this.templateLibraryVisible = true
      // 默认选择第一个模板
      if (this.templates.length > 0) {
        this.selectedTemplate = this.templates[0]
      }
    },
    selectTemplate(template) {
      this.selectedTemplate = template
    },
    useTemplate() {
      if (this.selectedTemplate) {
        this.$message.success(`使用模板: ${this.selectedTemplate.name}`)
        this.templateLibraryVisible = false
        // 这里可以添加使用模板的逻辑，比如跳转到编辑页面
      }
    },
    editTag(tag) {
      this.$message.info(`编辑标签: ${tag.name}`)
    },
    deleteTag(tag) {
      this.$confirm(`确定要删除标签"${tag.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tags.findIndex(t => t.id === tag.id)
        if (index > -1) {
          this.tags.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    handleCommand(command) {
      const { action, report } = command
      switch (action) {
        case 'edit':
          this.$message.info(`编辑报告: ${report.name}`)
          break
        case 'share':
          this.$message.info(`分享报告: ${report.name}`)
          break
        case 'copy':
          this.$message.info(`复制报告: ${report.name}`)
          break
        case 'delete':
          this.deleteReport(report)
          break
      }
    },
    deleteReport(report) {
      this.$confirm(`确定要删除报告"${report.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reports.findIndex(r => r.id === report.id)
        if (index > -1) {
          this.reports.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
  }
}

.report-table {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0;

  .report-name {
    display: flex;
    align-items: center;

    .report-icon {
      margin-right: 8px;
      color: #409EFF;
      font-size: 16px;
    }

    .report-title {
      margin-right: 8px;
    }

    .el-tag {
      margin-left: 8px;
    }
  }

  .user-info {
    display: flex;
    align-items: center;

    .user-name {
      margin-left: 8px;
      font-size: 12px;
      color: #606266;
    }
  }

  .el-dropdown-link {
    color: #909399;
    cursor: pointer;
    padding: 4px;

    &:hover {
      color: #409EFF;
    }
  }
}

// 表格样式调整
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 500;
        font-size: 12px;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      td {
        padding: 12px 0;
        font-size: 12px;
        border-bottom: 1px solid #ebeef5;

        .cell {
          padding: 0 10px;
        }
      }
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 头像样式
::v-deep .el-avatar {
  background-color: #409EFF;
  color: #fff;
  font-size: 10px;
}

// 标签管理对话框样式
.tag-manage-content {
  .tag-search {
    margin-bottom: 20px;
  }

  .tag-table {
    margin-bottom: 20px;
  }

  .tag-empty-state {
    text-align: center;
    padding: 40px 20px;

    .empty-icon {
      font-size: 48px;
      color: #dcdfe6;
      margin-bottom: 16px;
    }

    p {
      color: #909399;
      margin: 0;
      font-size: 14px;
    }
  }
}

// 模板库抽屉样式
::v-deep .template-library-drawer {
  .el-drawer {
    border-radius: 20px 20px 0 0;

    .el-drawer__header {
      padding: 20px 24px 0;
      margin-bottom: 0;

      .el-drawer__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-drawer__body {
      padding: 20px 24px 24px;
      height: calc(100% - 60px);
      overflow: hidden;
    }
  }
}

.template-library-content {
  display: flex;
  height: 100%;

  .template-sidebar {
    width: 300px;
    border-right: 1px solid #e6e6e6;
    padding: 20px;
    overflow-y: auto;

    .template-search {
      margin-bottom: 20px;
    }

    .category-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
    }

    .template-list {
      .template-item {
        padding: 12px;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409EFF;
          background-color: #f0f9ff;
        }

        &.active {
          border-color: #409EFF;
          background-color: #ecf5ff;
        }

        .template-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .template-desc {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }

  .template-preview {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .preview-content {
      height: 100%;

      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e6e6e6;

        h3 {
          margin: 0;
          font-size: 18px;
          color: #303133;
        }
      }

      .preview-body {
        .template-preview-image {
          text-align: center;

          img {
            max-width: 100%;
            max-height: 600px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .preview-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;

      .empty-icon {
        font-size: 64px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}
</style>
