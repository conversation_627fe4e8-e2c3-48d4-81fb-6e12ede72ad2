{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\dataeye\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\dataeye\\index.vue", "mtime": 1748160488000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_cache", "_interopRequireDefault", "require", "echarts", "_interopRequireWildcard", "name", "data", "activeIndex", "question", "searchHistory", "showAllHistory", "maxDisplayHistory", "commonQuestions", "storeChart", "chartData", "stores", "revenue", "growth", "computed", "displayHistory", "slice", "mounted", "_this", "loadSearchHistory", "$nextTick", "initStoreChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "handleSearch", "query", "trim", "$message", "warning", "addToHistory", "success", "concat", "selectQuestion", "existingIndex", "findIndex", "item", "content", "splice", "unshift", "time", "Date", "toLocaleString", "length", "saveSearchHistory", "removeHistoryItem", "index", "actualIndex", "clearHistory", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "catch", "cache", "local", "setJSON", "history", "getJSON", "Array", "isArray", "_this3", "$refs", "init", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "legend", "top", "xAxis", "axisLabel", "rotate", "fontSize", "yAxis", "position", "formatter", "series", "itemStyle", "graphic", "LinearGradient", "offset", "emphasis", "yAxisIndex", "lineStyle", "width", "setOption", "window", "addEventListener", "resize"], "sources": ["src/views/dataeye/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dataeye-container\">\n    <!-- AI智能问数区域 -->\n    <div class=\"ai-qa-section\">\n      <div class=\"qa-container\">\n        <h2 class=\"section-title\">AI智能问数</h2>\n        <div class=\"qa-content\">\n          <!-- 问题输入框 -->\n          <div class=\"question-input\">\n            <el-input\n              v-model=\"question\"\n              placeholder=\"请输入您的问题，Shift + Enter 换行\"\n              class=\"input-area\"\n              @keyup.enter.native=\"handleSearch\"\n            >\n              <el-button\n                slot=\"append\"\n                icon=\"el-icon-s-promotion\"\n                @click=\"handleSearch\"\n              ></el-button>\n            </el-input>\n          </div>\n\n          <!-- 历史搜索区域 -->\n          <div class=\"history-section\">\n            <div class=\"history-header\">\n              <div class=\"history-search-title\">历史搜索</div>\n              <el-button\n                v-if=\"searchHistory.length > 0\"\n                type=\"text\"\n                size=\"small\"\n                @click=\"clearHistory\"\n                class=\"clear-btn\"\n              >\n                清空历史\n              </el-button>\n            </div>\n\n            <!-- 搜索历史标签 -->\n            <div v-if=\"searchHistory.length > 0\" class=\"history-tags\">\n              <el-tag\n                v-for=\"(item, index) in displayHistory\"\n                :key=\"index\"\n                class=\"history-tag\"\n                closable\n                @click=\"selectQuestion(item.content)\"\n                @close=\"removeHistoryItem(index)\"\n              >\n                {{ item.content }}\n              </el-tag>\n              <el-button\n                v-if=\"searchHistory.length > maxDisplayHistory\"\n                type=\"text\"\n                size=\"small\"\n                @click=\"showAllHistory = !showAllHistory\"\n                class=\"toggle-btn\"\n              >\n                {{ showAllHistory ? '收起' : `查看全部(${searchHistory.length})` }}\n              </el-button>\n            </div>\n\n            <!-- 常用问题标签 -->\n            <div class=\"common-questions-section\">\n              <div class=\"section-subtitle\">常用问题</div>\n              <div class=\"question-tags\">\n                <el-tag\n                  v-for=\"(tag, index) in commonQuestions\"\n                  :key=\"index\"\n                  class=\"question-tag\"\n                  @click=\"selectQuestion(tag)\"\n                >\n                  {{ tag }}\n                </el-tag>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据仪表板区域 -->\n    <div class=\"dashboard-section\">\n      <!-- 数据卡片区域 -->\n      <div class=\"data-cards-section\">\n        <!-- 营业额卡片 -->\n        <div class=\"data-card\">\n          <div class=\"card-header\">\n            <div class=\"card-title\">\n              <i class=\"el-icon-s-data\"></i>\n              营业额/万元\n            </div>\n            <div class=\"card-actions\">\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\n            </div>\n          </div>\n          <div class=\"card-content\">\n            <div class=\"main-value\">165.32</div>\n            <div class=\"sub-info\">\n              <span class=\"date-info\">2024-01-01 至 14:23</span>\n              <span class=\"trend-info positive\">\n                <i class=\"el-icon-top\"></i>\n                同比 +14.5%\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 门店营业额前十名卡片 -->\n        <div class=\"data-card wide-card\">\n          <div class=\"card-header\">\n            <div class=\"card-title\">\n              <i class=\"el-icon-s-shop\"></i>\n              门店营业额前十名\n            </div>\n            <div class=\"card-actions\">\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-refresh\"></el-button>\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-download\"></el-button>\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\n            </div>\n          </div>\n          <div class=\"card-content\">\n            <div class=\"date-info\">2024-01-01 至 17:31</div>\n            <div class=\"chart-container\">\n              <div ref=\"storeChart\" class=\"chart\" style=\"height: 300px;\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能洞察区域 -->\n      <div class=\"insights-section\">\n        <div class=\"insights-card\">\n          <div class=\"card-header\">\n            <div class=\"card-title\">\n              <i class=\"el-icon-lightbulb\"></i>\n              智能洞察\n            </div>\n            <div class=\"card-actions\">\n              <el-button type=\"text\" size=\"mini\">智能分析</el-button>\n            </div>\n          </div>\n          <div class=\"card-content\">\n            <div class=\"insight-item\">\n              <div class=\"insight-icon\">\n                <i class=\"el-icon-warning-outline\"></i>\n              </div>\n              <div class=\"insight-content\">\n                <div class=\"insight-title\">营业额下降提醒</div>\n                <div class=\"insight-desc\">本周营业额相比上周下降了8.2%，建议关注门店运营情况</div>\n              </div>\n            </div>\n            <div class=\"insight-item\">\n              <div class=\"insight-icon success\">\n                <i class=\"el-icon-success\"></i>\n              </div>\n              <div class=\"insight-content\">\n                <div class=\"insight-title\">会员增长良好</div>\n                <div class=\"insight-desc\">新增会员数量环比增长15.3%，会员活跃度持续提升</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport cache from '@/plugins/cache'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'DataeyeIndex',\n  data() {\n    return {\n      activeIndex: '1',\n      question: '',\n      searchHistory: [],\n      showAllHistory: false,\n      maxDisplayHistory: 8, // 默认显示的历史记录数量\n      commonQuestions: [\n        '上季度营业额',\n        '今年Q1营业额每天',\n        '去年营业额前十的门店同比情况',\n        '今年Q1营业额每天品牌会员客流趋势上海北京',\n        '营业额每月',\n        '营业额降序的5个品牌',\n        '上季度营业额目标是多少',\n        '去年的营业额',\n        '去年门店是(三里屯)品牌是(耐克)门店面积营业额'\n      ],\n      storeChart: null,\n      chartData: {\n        stores: ['三里屯店', '王府井店', '西单店', '朝阳店', '海淀店', '丰台店', '石景山店', '通州店', '昌平店', '大兴店'],\n        revenue: [6892.192, 5482.192, 5200.007, 4702.477, 4200.477, 3962.577, 3425.547, 2894.477, 2638.849, 2114.669],\n        growth: [4.7, 3.2, -2.5, 1.8, -0.5, 2.1, -1.2, 0.8, 1.5, -0.3]\n      }\n    }\n  },\n  computed: {\n    displayHistory() {\n      if (this.showAllHistory) {\n        return this.searchHistory\n      }\n      return this.searchHistory.slice(0, this.maxDisplayHistory)\n    }\n  },\n  mounted() {\n    this.loadSearchHistory()\n    this.$nextTick(() => {\n      this.initStoreChart()\n    })\n  },\n  beforeDestroy() {\n    if (this.storeChart) {\n      this.storeChart.dispose()\n    }\n  },\n  methods: {\n    // 处理搜索\n    handleSearch() {\n      const query = this.question.trim()\n      if (!query) {\n        this.$message.warning('请输入搜索内容')\n        return\n      }\n\n      // 添加到搜索历史\n      this.addToHistory(query)\n\n      // 这里可以添加实际的搜索逻辑\n      this.$message.success(`正在搜索: ${query}`)\n\n      // 清空输入框\n      this.question = ''\n    },\n\n    // 选择问题\n    selectQuestion(question) {\n      this.question = question\n      // 可以选择是否立即搜索\n      // this.handleSearch()\n    },\n\n    // 添加到搜索历史\n    addToHistory(query) {\n      // 检查是否已存在\n      const existingIndex = this.searchHistory.findIndex(item => item.content === query)\n\n      if (existingIndex !== -1) {\n        // 如果已存在，移除旧的记录\n        this.searchHistory.splice(existingIndex, 1)\n      }\n\n      // 添加到开头\n      this.searchHistory.unshift({\n        content: query,\n        time: new Date().toLocaleString()\n      })\n\n      // 限制历史记录数量（最多保存50条）\n      if (this.searchHistory.length > 50) {\n        this.searchHistory = this.searchHistory.slice(0, 50)\n      }\n\n      // 保存到本地存储\n      this.saveSearchHistory()\n    },\n\n    // 移除单个历史记录\n    removeHistoryItem(index) {\n      const actualIndex = this.showAllHistory ? index : index\n      this.searchHistory.splice(actualIndex, 1)\n      this.saveSearchHistory()\n    },\n\n    // 清空历史记录\n    clearHistory() {\n      this.$confirm('确定要清空所有搜索历史吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.searchHistory = []\n        this.showAllHistory = false\n        this.saveSearchHistory()\n        this.$message.success('搜索历史已清空')\n      }).catch(() => {\n        // 用户取消\n      })\n    },\n\n    // 保存搜索历史到本地存储\n    saveSearchHistory() {\n      cache.local.setJSON('dataeye_search_history', this.searchHistory)\n    },\n\n    // 从本地存储加载搜索历史\n    loadSearchHistory() {\n      const history = cache.local.getJSON('dataeye_search_history')\n      if (history && Array.isArray(history)) {\n        this.searchHistory = history\n      }\n    },\n\n    // 初始化门店营业额图表\n    initStoreChart() {\n      if (!this.$refs.storeChart) return\n\n      this.storeChart = echarts.init(this.$refs.storeChart)\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          data: ['营业额', '增长率'],\n          top: 10\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.chartData.stores,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万)',\n            position: 'left',\n            axisLabel: {\n              formatter: '{value}'\n            }\n          },\n          {\n            type: 'value',\n            name: '增长率(%)',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额',\n            type: 'bar',\n            data: this.chartData.revenue,\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: '#83bff6' },\n                { offset: 0.5, color: '#188df0' },\n                { offset: 1, color: '#188df0' }\n              ])\n            },\n            emphasis: {\n              itemStyle: {\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                  { offset: 0, color: '#2378f7' },\n                  { offset: 0.7, color: '#2378f7' },\n                  { offset: 1, color: '#83bff6' }\n                ])\n              }\n            }\n          },\n          {\n            name: '增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.chartData.growth,\n            itemStyle: {\n              color: '#ffc658'\n            },\n            lineStyle: {\n              color: '#ffc658',\n              width: 2\n            }\n          }\n        ]\n      }\n\n      this.storeChart.setOption(option)\n\n      // 响应式处理\n      window.addEventListener('resize', () => {\n        if (this.storeChart) {\n          this.storeChart.resize()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dataeye-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n\n  .ai-qa-section {\n    background-color: #fff;\n    padding: 20px;\n    border-radius: 4px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n\n    .section-title {\n      text-align: center;\n      font-size: 20px;\n      color: #303133;\n      margin-bottom: 20px;\n    }\n\n    .qa-content {\n      .question-input {\n        margin-bottom: 20px;\n\n        .input-area {\n          ::v-deep .el-input__inner {\n            border-radius: 20px;\n            height: 40px;\n            line-height: 40px;\n          }\n\n          ::v-deep .el-input-group__append {\n            border-radius: 0 20px 20px 0;\n            border-left: none;\n\n            .el-button {\n              border-radius: 0 20px 20px 0;\n              height: 38px;\n              width: 40px;\n              padding: 0;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n            }\n          }\n        }\n      }\n\n      .history-section {\n        .history-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n\n          .history-search-title {\n            font-size: 14px;\n            color: #909399;\n            font-weight: 500;\n          }\n\n          .clear-btn {\n            color: #409EFF;\n            padding: 0;\n            font-size: 12px;\n\n            &:hover {\n              color: #66b1ff;\n            }\n          }\n        }\n\n        .history-tags {\n          margin-bottom: 20px;\n\n          .history-tag {\n            background-color: #e8f4ff;\n            border: 1px solid #b3d8ff;\n            color: #409EFF;\n            border-radius: 15px;\n            cursor: pointer;\n            margin-right: 8px;\n            margin-bottom: 8px;\n            padding: 6px 12px;\n            font-size: 13px;\n\n            &:hover {\n              background-color: #409EFF;\n              color: #fff;\n              border-color: #409EFF;\n            }\n\n            ::v-deep .el-tag__close {\n              color: #409EFF;\n\n              &:hover {\n                background-color: #fff;\n                color: #f56c6c;\n              }\n            }\n          }\n\n          .toggle-btn {\n            color: #909399;\n            padding: 0;\n            font-size: 12px;\n            margin-left: 8px;\n\n            &:hover {\n              color: #409EFF;\n            }\n          }\n        }\n\n        .common-questions-section {\n          .section-subtitle {\n            font-size: 14px;\n            color: #909399;\n            margin-bottom: 10px;\n            font-weight: 500;\n          }\n\n          .question-tags {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 8px;\n\n            .question-tag {\n              background-color: #f0f2f5;\n              border: 1px solid #dcdfe6;\n              border-radius: 15px;\n              color: #606266;\n              cursor: pointer;\n              padding: 6px 12px;\n              font-size: 13px;\n              transition: all 0.3s;\n\n              &:hover {\n                background-color: #409EFF;\n                color: #fff;\n                border-color: #409EFF;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  // 数据仪表板样式\n  .dashboard-section {\n    margin-top: 20px;\n\n    .data-cards-section {\n      display: grid;\n      grid-template-columns: 300px 1fr;\n      gap: 20px;\n      margin-bottom: 20px;\n\n      .data-card {\n        background-color: #fff;\n        border-radius: 8px;\n        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n        overflow: hidden;\n\n        &.wide-card {\n          grid-column: span 1;\n        }\n\n        .card-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 16px 20px;\n          border-bottom: 1px solid #f0f0f0;\n          background-color: #fafafa;\n\n          .card-title {\n            display: flex;\n            align-items: center;\n            font-size: 14px;\n            font-weight: 600;\n            color: #303133;\n\n            i {\n              margin-right: 8px;\n              color: #409EFF;\n            }\n          }\n\n          .card-actions {\n            display: flex;\n            gap: 4px;\n\n            .el-button {\n              padding: 4px 8px;\n              color: #909399;\n\n              &:hover {\n                color: #409EFF;\n              }\n            }\n          }\n        }\n\n        .card-content {\n          padding: 20px;\n\n          .main-value {\n            font-size: 32px;\n            font-weight: bold;\n            color: #303133;\n            margin-bottom: 8px;\n          }\n\n          .sub-info {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n\n            .date-info {\n              font-size: 12px;\n              color: #909399;\n            }\n\n            .trend-info {\n              font-size: 12px;\n              display: flex;\n              align-items: center;\n\n              &.positive {\n                color: #67C23A;\n              }\n\n              &.negative {\n                color: #F56C6C;\n              }\n\n              i {\n                margin-right: 2px;\n              }\n            }\n          }\n\n          .date-info {\n            font-size: 12px;\n            color: #909399;\n            margin-bottom: 16px;\n          }\n\n          .chart-container {\n            .chart {\n              width: 100%;\n            }\n          }\n        }\n      }\n    }\n\n    .insights-section {\n      .insights-card {\n        background-color: #fff;\n        border-radius: 8px;\n        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n        overflow: hidden;\n\n        .card-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 16px 20px;\n          border-bottom: 1px solid #f0f0f0;\n          background-color: #fafafa;\n\n          .card-title {\n            display: flex;\n            align-items: center;\n            font-size: 14px;\n            font-weight: 600;\n            color: #303133;\n\n            i {\n              margin-right: 8px;\n              color: #E6A23C;\n            }\n          }\n\n          .card-actions {\n            .el-button {\n              padding: 4px 12px;\n              font-size: 12px;\n              color: #409EFF;\n\n              &:hover {\n                background-color: #ecf5ff;\n              }\n            }\n          }\n        }\n\n        .card-content {\n          padding: 20px;\n\n          .insight-item {\n            display: flex;\n            align-items: flex-start;\n            margin-bottom: 16px;\n\n            &:last-child {\n              margin-bottom: 0;\n            }\n\n            .insight-icon {\n              width: 32px;\n              height: 32px;\n              border-radius: 50%;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              margin-right: 12px;\n              background-color: #FDF6EC;\n              color: #E6A23C;\n\n              &.success {\n                background-color: #F0F9FF;\n                color: #67C23A;\n              }\n\n              i {\n                font-size: 16px;\n              }\n            }\n\n            .insight-content {\n              flex: 1;\n\n              .insight-title {\n                font-size: 14px;\n                font-weight: 600;\n                color: #303133;\n                margin-bottom: 4px;\n              }\n\n              .insight-desc {\n                font-size: 12px;\n                color: #606266;\n                line-height: 1.5;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAwKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,iBAAA;MAAA;MACAC,eAAA,GACA,UACA,aACA,kBACA,yBACA,SACA,cACA,eACA,UACA,2BACA;MACAC,UAAA;MACAC,SAAA;QACAC,MAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MACA,SAAAT,cAAA;QACA,YAAAD,aAAA;MACA;MACA,YAAAA,aAAA,CAAAW,KAAA,SAAAT,iBAAA;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,SAAA;MACAF,KAAA,CAAAG,cAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAb,UAAA;MACA,KAAAA,UAAA,CAAAc,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAC,KAAA,QAAAtB,QAAA,CAAAuB,IAAA;MACA,KAAAD,KAAA;QACA,KAAAE,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAC,YAAA,CAAAJ,KAAA;;MAEA;MACA,KAAAE,QAAA,CAAAG,OAAA,8BAAAC,MAAA,CAAAN,KAAA;;MAEA;MACA,KAAAtB,QAAA;IACA;IAEA;IACA6B,cAAA,WAAAA,eAAA7B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA;MACA;IACA;IAEA;IACA0B,YAAA,WAAAA,aAAAJ,KAAA;MACA;MACA,IAAAQ,aAAA,QAAA7B,aAAA,CAAA8B,SAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA,KAAAX,KAAA;MAAA;MAEA,IAAAQ,aAAA;QACA;QACA,KAAA7B,aAAA,CAAAiC,MAAA,CAAAJ,aAAA;MACA;;MAEA;MACA,KAAA7B,aAAA,CAAAkC,OAAA;QACAF,OAAA,EAAAX,KAAA;QACAc,IAAA,MAAAC,IAAA,GAAAC,cAAA;MACA;;MAEA;MACA,SAAArC,aAAA,CAAAsC,MAAA;QACA,KAAAtC,aAAA,QAAAA,aAAA,CAAAW,KAAA;MACA;;MAEA;MACA,KAAA4B,iBAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAAC,KAAA;MACA,IAAAC,WAAA,QAAAzC,cAAA,GAAAwC,KAAA,GAAAA,KAAA;MACA,KAAAzC,aAAA,CAAAiC,MAAA,CAAAS,WAAA;MACA,KAAAH,iBAAA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAA5C,aAAA;QACA4C,MAAA,CAAA3C,cAAA;QACA2C,MAAA,CAAAL,iBAAA;QACAK,MAAA,CAAArB,QAAA,CAAAG,OAAA;MACA,GAAAwB,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAX,iBAAA,WAAAA,kBAAA;MACAY,cAAA,CAAAC,KAAA,CAAAC,OAAA,gCAAArD,aAAA;IACA;IAEA;IACAc,iBAAA,WAAAA,kBAAA;MACA,IAAAwC,OAAA,GAAAH,cAAA,CAAAC,KAAA,CAAAG,OAAA;MACA,IAAAD,OAAA,IAAAE,KAAA,CAAAC,OAAA,CAAAH,OAAA;QACA,KAAAtD,aAAA,GAAAsD,OAAA;MACA;IACA;IAEA;IACAtC,cAAA,WAAAA,eAAA;MAAA,IAAA0C,MAAA;MACA,UAAAC,KAAA,CAAAvD,UAAA;MAEA,KAAAA,UAAA,GAAAV,OAAA,CAAAkE,IAAA,MAAAD,KAAA,CAAAvD,UAAA;MAEA,IAAAyD,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAhB,IAAA;YACAiB,UAAA;cACAC,KAAA;YACA;UACA;QACA;QACAC,MAAA;UACAtE,IAAA;UACAuE,GAAA;QACA;QACAC,KAAA,GACA;UACArB,IAAA;UACAnD,IAAA,OAAAQ,SAAA,CAAAC,MAAA;UACA0D,WAAA;YACAhB,IAAA;UACA;UACAsB,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA,EACA;QACAC,KAAA,GACA;UACAzB,IAAA;UACApD,IAAA;UACA8E,QAAA;UACAJ,SAAA;YACAK,SAAA;UACA;QACA,GACA;UACA3B,IAAA;UACApD,IAAA;UACA8E,QAAA;UACAJ,SAAA;YACAK,SAAA;UACA;QACA,EACA;QACAC,MAAA,GACA;UACAhF,IAAA;UACAoD,IAAA;UACAnD,IAAA,OAAAQ,SAAA,CAAAE,OAAA;UACAsE,SAAA;YACAX,KAAA,MAAAxE,OAAA,CAAAoF,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAAd,KAAA;YAAA,GACA;cAAAc,MAAA;cAAAd,KAAA;YAAA,GACA;cAAAc,MAAA;cAAAd,KAAA;YAAA,EACA;UACA;UACAe,QAAA;YACAJ,SAAA;cACAX,KAAA,MAAAxE,OAAA,CAAAoF,OAAA,CAAAC,cAAA,cACA;gBAAAC,MAAA;gBAAAd,KAAA;cAAA,GACA;gBAAAc,MAAA;gBAAAd,KAAA;cAAA,GACA;gBAAAc,MAAA;gBAAAd,KAAA;cAAA,EACA;YACA;UACA;QACA,GACA;UACAtE,IAAA;UACAoD,IAAA;UACAkC,UAAA;UACArF,IAAA,OAAAQ,SAAA,CAAAG,MAAA;UACAqE,SAAA;YACAX,KAAA;UACA;UACAiB,SAAA;YACAjB,KAAA;YACAkB,KAAA;UACA;QACA;MAEA;MAEA,KAAAhF,UAAA,CAAAiF,SAAA,CAAAxB,MAAA;;MAEA;MACAyB,MAAA,CAAAC,gBAAA;QACA,IAAA7B,MAAA,CAAAtD,UAAA;UACAsD,MAAA,CAAAtD,UAAA,CAAAoF,MAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}