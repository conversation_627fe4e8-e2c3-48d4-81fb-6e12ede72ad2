{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\system\\user\\profile\\userInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\system\\user\\profile\\userInfo.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "props", "user", "type", "Object", "data", "form", "rules", "nick<PERSON><PERSON>", "required", "message", "trigger", "email", "phonenumber", "pattern", "watch", "handler", "sex", "immediate", "methods", "submit", "_this", "$refs", "validate", "valid", "updateUserProfile", "then", "response", "$modal", "msgSuccess", "close", "$tab", "closePage"], "sources": ["src/views/system/user/profile/userInfo.vue"], "sourcesContent": ["<template>\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n    <el-form-item label=\"用户昵称\" prop=\"nickName\">\n      <el-input v-model=\"form.nickName\" maxlength=\"30\" />\n    </el-form-item> \n    <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n      <el-input v-model=\"form.phonenumber\" maxlength=\"11\" />\n    </el-form-item>\n    <el-form-item label=\"邮箱\" prop=\"email\">\n      <el-input v-model=\"form.email\" maxlength=\"50\" />\n    </el-form-item>\n    <el-form-item label=\"性别\">\n      <el-radio-group v-model=\"form.sex\">\n        <el-radio label=\"0\">男</el-radio>\n        <el-radio label=\"1\">女</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item>\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\n    </el-form-item>\n  </el-form>\n</template>\n\n<script>\nimport { updateUserProfile } from \"@/api/system/user\";\n\nexport default {\n  props: {\n    user: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      form: {},\n      // 表单校验\n      rules: {\n        nickName: [\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"邮箱地址不能为空\", trigger: \"blur\" },\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phonenumber: [\n          { required: true, message: \"手机号码不能为空\", trigger: \"blur\" },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  watch: {\n    user: {\n      handler(user) {\n        if (user) {\n          this.form = { nickName: user.nickName, phonenumber: user.phonenumber, email: user.email, sex: user.sex };\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    submit() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          updateUserProfile(this.form).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.user.phonenumber = this.form.phonenumber;\n            this.user.email = this.form.email;\n          });\n        }\n      });\n    },\n    close() {\n      this.$tab.closePage();\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;AAyBA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,KAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAR,IAAA;UACAO,OAAA;UACAC,OAAA;QACA,EACA;QACAE,WAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAG,OAAA;UACAJ,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAI,KAAA;IACAb,IAAA;MACAc,OAAA,WAAAA,QAAAd,IAAA;QACA,IAAAA,IAAA;UACA,KAAAI,IAAA;YAAAE,QAAA,EAAAN,IAAA,CAAAM,QAAA;YAAAK,WAAA,EAAAX,IAAA,CAAAW,WAAA;YAAAD,KAAA,EAAAV,IAAA,CAAAU,KAAA;YAAAK,GAAA,EAAAf,IAAA,CAAAe;UAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,uBAAA,EAAAJ,KAAA,CAAAf,IAAA,EAAAoB,IAAA,WAAAC,QAAA;YACAN,KAAA,CAAAO,MAAA,CAAAC,UAAA;YACAR,KAAA,CAAAnB,IAAA,CAAAW,WAAA,GAAAQ,KAAA,CAAAf,IAAA,CAAAO,WAAA;YACAQ,KAAA,CAAAnB,IAAA,CAAAU,KAAA,GAAAS,KAAA,CAAAf,IAAA,CAAAM,KAAA;UACA;QACA;MACA;IACA;IACAkB,KAAA,WAAAA,MAAA;MACA,KAAAC,IAAA,CAAAC,SAAA;IACA;EACA;AACA", "ignoreList": []}]}