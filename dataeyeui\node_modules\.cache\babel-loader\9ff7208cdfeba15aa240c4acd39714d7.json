{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\inline.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\inline.js", "mtime": 1749172158582}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_break", "_interopRequireDefault", "_text", "_Inline", "Inline", "_InlineBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "formatAt", "index", "length", "name", "compare", "statics", "blotName", "scroll", "query", "<PERSON><PERSON>", "BLOT", "blot", "isolate", "wrap", "_superPropGet2", "optimize", "context", "parent", "offset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "self", "other", "selfIndex", "order", "indexOf", "otherIndex", "InlineBlot", "_defineProperty2", "Break", "EmbedBlot", "Text", "_default", "exports"], "sources": ["../../src/blots/inline.ts"], "sourcesContent": ["import { EmbedBlot, InlineBlot, Scope } from 'parchment';\nimport type { BlotConstructor } from 'parchment';\nimport Break from './break.js';\nimport Text from './text.js';\n\nclass Inline extends InlineBlot {\n  static allowedChildren: BlotConstructor[] = [Inline, Break, EmbedBlot, Text];\n  // Lower index means deeper in the DOM tree, since not found (-1) is for embeds\n  static order = [\n    'cursor',\n    'inline', // Must be lower\n    'link', // Chrome wants <a> to be lower\n    'underline',\n    'strike',\n    'italic',\n    'bold',\n    'script',\n    'code', // Must be higher\n  ];\n\n  static compare(self: string, other: string) {\n    const selfIndex = Inline.order.indexOf(self);\n    const otherIndex = Inline.order.indexOf(other);\n    if (selfIndex >= 0 || otherIndex >= 0) {\n      return selfIndex - otherIndex;\n    }\n    if (self === other) {\n      return 0;\n    }\n    if (self < other) {\n      return -1;\n    }\n    return 1;\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (\n      Inline.compare(this.statics.blotName, name) < 0 &&\n      this.scroll.query(name, Scope.BLOT)\n    ) {\n      const blot = this.isolate(index, length);\n      if (value) {\n        blot.wrap(name, value);\n      }\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    if (\n      this.parent instanceof Inline &&\n      Inline.compare(this.statics.blotName, this.parent.statics.blotName) > 0\n    ) {\n      const parent = this.parent.isolate(this.offset(), this.length());\n      // @ts-expect-error TODO: make isolate generic\n      this.moveChildren(parent);\n      parent.wrap(this);\n    }\n  }\n}\n\nexport default Inline;\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AAA4B,IAAAI,OAAA;AAAA,IAEtBC,MAAM,0BAAAC,WAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,MAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,MAAA,EAAAC,WAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,MAAA;IAAAQ,GAAA;IAAAC,KAAA,EA8BV,SAAAC,QAAQA,CAACC,KAAa,EAAEC,MAAc,EAAEC,IAAY,EAAEJ,KAAc,EAAE;MACpE,IACET,MAAM,CAACc,OAAO,CAAC,IAAI,CAACC,OAAO,CAACC,QAAQ,EAAEH,IAAI,CAAC,GAAG,CAAC,IAC/C,IAAI,CAACI,MAAM,CAACC,KAAK,CAACL,IAAI,EAAEM,gBAAK,CAACC,IAAI,CAAC,EACnC;QACA,IAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACX,KAAK,EAAEC,MAAM,CAAC;QACxC,IAAIH,KAAK,EAAE;UACTY,IAAI,CAACE,IAAI,CAACV,IAAI,EAAEJ,KAAK,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAAe,cAAA,CAAArB,OAAA,EAAAH,MAAA,wBAAeW,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,KAAK;MAC3C;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAgB,QAAQA,CAACC,OAA+B,EAAE;MACxC,IAAAF,cAAA,CAAArB,OAAA,EAAAH,MAAA,wBAAe0B,OAAO;MACtB,IACE,IAAI,CAACC,MAAM,YAAY3B,MAAM,IAC7BA,MAAM,CAACc,OAAO,CAAC,IAAI,CAACC,OAAO,CAACC,QAAQ,EAAE,IAAI,CAACW,MAAM,CAACZ,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,EACvE;QACA,IAAMW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACL,OAAO,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAAChB,MAAM,CAAC,CAAC,CAAC;QAChE;QACA,IAAI,CAACiB,YAAY,CAACF,MAAM,CAAC;QACzBA,MAAM,CAACJ,IAAI,CAAC,IAAI,CAAC;MACnB;IACF;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAxCA,SAAOK,OAAOA,CAACgB,IAAY,EAAEC,KAAa,EAAE;MAC1C,IAAMC,SAAS,GAAGhC,MAAM,CAACiC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC;MAC5C,IAAMK,UAAU,GAAGnC,MAAM,CAACiC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC;MAC9C,IAAIC,SAAS,IAAI,CAAC,IAAIG,UAAU,IAAI,CAAC,EAAE;QACrC,OAAOH,SAAS,GAAGG,UAAU;MAC/B;MACA,IAAIL,IAAI,KAAKC,KAAK,EAAE;QAClB,OAAO,CAAC;MACV;MACA,IAAID,IAAI,GAAGC,KAAK,EAAE;QAChB,OAAO,CAAC,CAAC;MACX;MACA,OAAO,CAAC;IACV;EAAA;AAAA,EA5BmBK,qBAAU;AAAArC,OAAA,GAAzBC,MAAM;AAAA,IAAAqC,gBAAA,CAAAlC,OAAA,EAANH,MAAM,qBACkC,CAACA,OAAM,EAAEsC,cAAK,EAAEC,oBAAS,EAAEC,aAAI,CAAC;AAC5E;AAAA,IAAAH,gBAAA,CAAAlC,OAAA,EAFIH,MAAM,WAGK,CACb,QAAQ,EACR,QAAQ;AAAE;AACV,MAAM;AAAE;AACR,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,CAAE;AAAA,CACT;AAAA,IAAAyC,QAAA,GAAAC,OAAA,CAAAvC,OAAA,GA6CYH,MAAM", "ignoreList": []}]}