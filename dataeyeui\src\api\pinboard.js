import request from '@/utils/request'

// 获取我的Pinboard列表
export function getMyPinboards(query) {
  return request({
    url: '/pinboard/my',
    method: 'get',
    params: query
  })
}

// 获取Pinboard列表
export function getPinboardList(query) {
  return request({
    url: '/pinboard/list',
    method: 'get',
    params: query
  })
}

// 创建Pinboard
export function createPinboard(data) {
  return request({
    url: '/pinboard/create',
    method: 'post',
    data: data
  })
}

// 删除Pinboard
export function deletePinboard(pinboardUid) {
  return request({
    url: `/pinboard/${pinboardUid}`,
    method: 'delete'
  })
}

// 获取标签列表
export function getTags(query) {
  return request({
    url: '/pinboard/tags',
    method: 'get',
    params: query
  })
}

// 获取协作报告列表
export function getCollaborateReports(query) {
  return request({
    url: '/pinboard/collaborate',
    method: 'get',
    params: query
  })
}

// 获取共享报告列表
export function getSharedReports(query) {
  return request({
    url: '/pinboard/shared',
    method: 'get',
    params: query
  })
}

// 获取关注报告列表
export function getFollowedReports(query) {
  return request({
    url: '/pinboard/followed',
    method: 'get',
    params: query
  })
}

// 获取定时提醒列表
export function getReminders(query) {
  return request({
    url: '/pinboard/reminders',
    method: 'get',
    params: query
  })
}

// 创建定时提醒
export function createReminder(data) {
  return request({
    url: '/pinboard/reminders',
    method: 'post',
    data: data
  })
}

// 获取回收站报告列表
export function getTrashReports(query) {
  return request({
    url: '/pinboard/trash',
    method: 'get',
    params: query
  })
}

// 恢复报告
export function restoreReport(pinboardUid) {
  return request({
    url: `/pinboard/trash/${pinboardUid}/restore`,
    method: 'post'
  })
}

// 获取可用报告列表（用于提醒设置）
export function getAvailableReports() {
  return request({
    url: '/pinboard/list',
    method: 'get',
    params: {
      page: 1,
      size: 100 // 获取更多报告供选择
    }
  })
}

// 获取模板列表（暂时返回静态数据）
export function getTemplates() {
  return Promise.resolve({
    code: 200,
    data: [
      {
        id: 1,
        name: '人工智能发展趋势分析报告1',
        description: '一个AI报告',
        isNew: true,
        previewImage: '/static/template-preview-placeholder.svg',
        category: 'AI分析'
      },
      {
        id: 2,
        name: '人工智能发展趋势',
        description: 'AI发展趋势分析',
        isNew: false,
        previewImage: '/static/template-preview-placeholder.svg',
        category: 'AI分析'
      },
      {
        id: 3,
        name: '对于AI',
        description: 'AI相关报告模板',
        isNew: false,
        previewImage: '/static/template-preview-placeholder.svg',
        category: 'AI分析'
      }
    ]
  })
}
