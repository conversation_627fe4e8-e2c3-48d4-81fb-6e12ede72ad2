import asyncio
import logging
from sqlalchemy import text
from config.database import async_engine

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def verify_tables():
    """验证新创建的表结构"""
    logger.info("开始验证新创建的表结构...")
    
    # 要验证的表
    tables_to_verify = [
        'pinboard_shares',
        'user_notification_settings', 
        'pinboard_updates',
        'pinboard_reminders',
        'pinboard_reminder_logs',
        'pinboard_delete_records'
    ]
    
    try:
        async with async_engine.begin() as conn:
            # 检查表是否存在
            for table_name in tables_to_verify:
                result = await conn.execute(text(
                    "SELECT COUNT(*) FROM information_schema.tables "
                    "WHERE table_schema = DATABASE() AND table_name = :table_name"
                ), {"table_name": table_name})
                count = result.scalar()
                
                if count > 0:
                    logger.info(f"✅ 表 {table_name} 存在")
                    
                    # 获取表结构
                    result = await conn.execute(text(f"DESCRIBE {table_name}"))
                    columns = result.fetchall()
                    
                    logger.info(f"📋 表 {table_name} 的字段结构:")
                    for column in columns:
                        logger.info(f"   - {column[0]}: {column[1]} {column[2]} {column[3]} {column[4]} {column[5]}")
                    logger.info("")
                else:
                    logger.error(f"❌ 表 {table_name} 不存在")
                    
    except Exception as e:
        logger.error(f"验证表结构时出错: {e}")
    
    # 关闭数据库连接
    await async_engine.dispose()

if __name__ == "__main__":
    asyncio.run(verify_tables())
