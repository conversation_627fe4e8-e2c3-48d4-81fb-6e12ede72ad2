{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\block.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\block.js", "mtime": 1749172156829}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "_break", "_inline", "_text", "NEWLINE_LENGTH", "Block", "exports", "default", "_BlockBlot", "_this", "_classCallCheck2", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper2", "concat", "_defineProperty2", "_inherits2", "_createClass2", "key", "value", "delta", "cache", "blockDelta", "deleteAt", "index", "_superPropGet2", "formatAt", "name", "scroll", "query", "<PERSON><PERSON>", "BLOCK", "format", "Math", "min", "insertAt", "def", "lines", "split", "text", "shift", "children", "tail", "block", "reduce", "lineIndex", "line", "insertBefore", "blot", "ref", "head", "Break", "remove", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "optimize", "context", "path", "<PERSON><PERSON><PERSON><PERSON>", "child", "force", "undefined", "clone", "parent", "next", "BlockBlot", "blotName", "tagName", "defaultChild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Inline", "EmbedBlot", "TextBlot", "BlockEmbed", "_EmbedBlot", "attach", "attributes", "AttributorStore", "domNode", "Delta", "insert", "_objectSpread2", "formats", "values", "attribute", "BLOCK_ATTRIBUTE", "_this2", "pop", "blocks", "map", "create", "for<PERSON>ach", "scope", "BLOCK_BLOT", "filter", "descendants", "LeafBlot", "leaf", "bubbleFormats", "statics"], "sources": ["../../src/blots/block.ts"], "sourcesContent": ["import {\n  Attributor<PERSON><PERSON>,\n  BlockBlot,\n  EmbedBlot,\n  LeafBlot,\n  Scope,\n} from 'parchment';\nimport type { Blot, Parent } from 'parchment';\nimport Delta from 'quill-delta';\nimport Break from './break.js';\nimport Inline from './inline.js';\nimport TextBlot from './text.js';\n\nconst NEWLINE_LENGTH = 1;\n\nclass Block extends BlockBlot {\n  cache: { delta?: Delta | null; length?: number } = {};\n\n  delta(): Delta {\n    if (this.cache.delta == null) {\n      this.cache.delta = blockDelta(this);\n    }\n    return this.cache.delta;\n  }\n\n  deleteAt(index: number, length: number) {\n    super.deleteAt(index, length);\n    this.cache = {};\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (length <= 0) return;\n    if (this.scroll.query(name, Scope.BLOCK)) {\n      if (index + length === this.length()) {\n        this.format(name, value);\n      }\n    } else {\n      super.formatAt(\n        index,\n        Math.min(length, this.length() - index - 1),\n        name,\n        value,\n      );\n    }\n    this.cache = {};\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      this.cache = {};\n      return;\n    }\n    if (value.length === 0) return;\n    const lines = value.split('\\n');\n    const text = lines.shift() as string;\n    if (text.length > 0) {\n      if (index < this.length() - 1 || this.children.tail == null) {\n        super.insertAt(Math.min(index, this.length() - 1), text);\n      } else {\n        this.children.tail.insertAt(this.children.tail.length(), text);\n      }\n      this.cache = {};\n    }\n    // TODO: Fix this next time the file is edited.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let block: Blot | this = this;\n    lines.reduce((lineIndex, line) => {\n      // @ts-expect-error Fix me later\n      block = block.split(lineIndex, true);\n      block.insertAt(0, line);\n      return line.length;\n    }, index + text.length);\n  }\n\n  insertBefore(blot: Blot, ref?: Blot | null) {\n    const { head } = this.children;\n    super.insertBefore(blot, ref);\n    if (head instanceof Break) {\n      head.remove();\n    }\n    this.cache = {};\n  }\n\n  length() {\n    if (this.cache.length == null) {\n      this.cache.length = super.length() + NEWLINE_LENGTH;\n    }\n    return this.cache.length;\n  }\n\n  moveChildren(target: Parent, ref?: Blot | null) {\n    super.moveChildren(target, ref);\n    this.cache = {};\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    this.cache = {};\n  }\n\n  path(index: number) {\n    return super.path(index, true);\n  }\n\n  removeChild(child: Blot) {\n    super.removeChild(child);\n    this.cache = {};\n  }\n\n  split(index: number, force: boolean | undefined = false): Blot | null {\n    if (force && (index === 0 || index >= this.length() - NEWLINE_LENGTH)) {\n      const clone = this.clone();\n      if (index === 0) {\n        this.parent.insertBefore(clone, this);\n        return this;\n      }\n      this.parent.insertBefore(clone, this.next);\n      return clone;\n    }\n    const next = super.split(index, force);\n    this.cache = {};\n    return next;\n  }\n}\nBlock.blotName = 'block';\nBlock.tagName = 'P';\nBlock.defaultChild = Break;\nBlock.allowedChildren = [Break, Inline, EmbedBlot, TextBlot];\n\nclass BlockEmbed extends EmbedBlot {\n  attributes: AttributorStore;\n  domNode: HTMLElement;\n\n  attach() {\n    super.attach();\n    this.attributes = new AttributorStore(this.domNode);\n  }\n\n  delta() {\n    return new Delta().insert(this.value(), {\n      ...this.formats(),\n      ...this.attributes.values(),\n    });\n  }\n\n  format(name: string, value: unknown) {\n    const attribute = this.scroll.query(name, Scope.BLOCK_ATTRIBUTE);\n    if (attribute != null) {\n      // @ts-expect-error TODO: Scroll#query() should return Attributor when scope is attribute\n      this.attributes.attribute(attribute, value);\n    }\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    this.format(name, value);\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      return;\n    }\n    const lines = value.split('\\n');\n    const text = lines.pop();\n    const blocks = lines.map((line) => {\n      const block = this.scroll.create(Block.blotName);\n      block.insertAt(0, line);\n      return block;\n    });\n    const ref = this.split(index);\n    blocks.forEach((block) => {\n      this.parent.insertBefore(block, ref);\n    });\n    if (text) {\n      this.parent.insertBefore(this.scroll.create('text', text), ref);\n    }\n  }\n}\nBlockEmbed.scope = Scope.BLOCK_BLOT;\n// It is important for cursor behavior BlockEmbeds use tags that are block level elements\n\nfunction blockDelta(blot: BlockBlot, filter = true) {\n  return blot\n    .descendants(LeafBlot)\n    .reduce((delta, leaf) => {\n      if (leaf.length() === 0) {\n        return delta;\n      }\n      return delta.insert(leaf.value(), bubbleFormats(leaf, {}, filter));\n    }, new Delta())\n    .insert('\\n', bubbleFormats(blot));\n}\n\nfunction bubbleFormats(\n  blot: Blot | null,\n  formats: Record<string, unknown> = {},\n  filter = true,\n): Record<string, unknown> {\n  if (blot == null) return formats;\n  if ('formats' in blot && typeof blot.formats === 'function') {\n    formats = {\n      ...formats,\n      ...blot.formats(),\n    };\n    if (filter) {\n      // exclude syntax highlighting from deltas and getFormat()\n      delete formats['code-token'];\n    }\n  }\n  if (\n    blot.parent == null ||\n    blot.parent.statics.blotName === 'scroll' ||\n    blot.parent.statics.scope !== blot.statics.scope\n  ) {\n    return formats;\n  }\n  return bubbleFormats(blot.parent, formats, filter);\n}\n\nexport { blockDelta, bubbleFormats, BlockEmbed, Block as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAQA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,OAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,KAAA,GAAAH,sBAAA,CAAAF,OAAA;AAEA,IAAMM,cAAc,GAAG,CAAC;AAAA,IAElBC,KAAK,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,UAAA;EAAA,SAAAH,MAAA;IAAA,IAAAI,KAAA;IAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,KAAA;IAAA,SAAAM,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,OAAAQ,WAAA,CAAAV,OAAA,QAAAF,KAAA,KAAAa,MAAA,CAAAJ,IAAA;IAAA,IAAAK,gBAAA,CAAAZ,OAAA,EAAAE,KAAA,WAC0C,CAAC,CAAC;IAAA,OAAAA,KAAA;EAAA;EAAA,IAAAW,UAAA,CAAAb,OAAA,EAAAF,KAAA,EAAAG,UAAA;EAAA,WAAAa,aAAA,CAAAd,OAAA,EAAAF,KAAA;IAAAiB,GAAA;IAAAC,KAAA,EAErD,SAAAC,KAAKA,CAAA,EAAU;MACb,IAAI,IAAI,CAACC,KAAK,CAACD,KAAK,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACC,KAAK,CAACD,KAAK,GAAGE,UAAU,CAAC,IAAI,CAAC;MACrC;MACA,OAAO,IAAI,CAACD,KAAK,CAACD,KAAK;IACzB;EAAA;IAAAF,GAAA;IAAAC,KAAA,EAEA,SAAAI,QAAQA,CAACC,KAAa,EAAEf,MAAc,EAAE;MACtC,IAAAgB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAeuB,KAAK,EAAEf,MAAM;MAC5B,IAAI,CAACY,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAO,QAAQA,CAACF,KAAa,EAAEf,MAAc,EAAEkB,IAAY,EAAER,KAAc,EAAE;MACpE,IAAIV,MAAM,IAAI,CAAC,EAAE;MACjB,IAAI,IAAI,CAACmB,MAAM,CAACC,KAAK,CAACF,IAAI,EAAEG,gBAAK,CAACC,KAAK,CAAC,EAAE;QACxC,IAAIP,KAAK,GAAGf,MAAM,KAAK,IAAI,CAACA,MAAM,CAAC,CAAC,EAAE;UACpC,IAAI,CAACuB,MAAM,CAACL,IAAI,EAAER,KAAK,CAAC;QAC1B;MACF,CAAC,MAAM;QACL,IAAAM,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBACEuB,KAAK,EACLS,IAAI,CAACC,GAAG,CAACzB,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,GAAGe,KAAK,GAAG,CAAC,CAAC,EAC3CG,IAAI,EACJR,KACF;MACF;MACA,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAgB,QAAQA,CAACX,KAAa,EAAEL,KAAa,EAAEiB,GAAa,EAAE;MACpD,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,IAAAX,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAeuB,KAAK,EAAEL,KAAK,EAAEiB,GAAG;QAChC,IAAI,CAACf,KAAK,GAAG,CAAC,CAAC;QACf;MACF;MACA,IAAIF,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE;MACxB,IAAM4B,KAAK,GAAGlB,KAAK,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC/B,IAAMC,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAW;MACpC,IAAID,IAAI,CAAC9B,MAAM,GAAG,CAAC,EAAE;QACnB,IAAIe,KAAK,GAAG,IAAI,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgC,QAAQ,CAACC,IAAI,IAAI,IAAI,EAAE;UAC3D,IAAAjB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAegC,IAAI,CAACC,GAAG,CAACV,KAAK,EAAE,IAAI,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE8B,IAAI;QACzD,CAAC,MAAM;UACL,IAAI,CAACE,QAAQ,CAACC,IAAI,CAACP,QAAQ,CAAC,IAAI,CAACM,QAAQ,CAACC,IAAI,CAACjC,MAAM,CAAC,CAAC,EAAE8B,IAAI,CAAC;QAChE;QACA,IAAI,CAAClB,KAAK,GAAG,CAAC,CAAC;MACjB;MACA;MACA;MACA,IAAIsB,KAAkB,GAAG,IAAI;MAC7BN,KAAK,CAACO,MAAM,CAAC,UAACC,SAAS,EAAEC,IAAI,EAAK;QAChC;QACAH,KAAK,GAAGA,KAAK,CAACL,KAAK,CAACO,SAAS,EAAE,IAAI,CAAC;QACpCF,KAAK,CAACR,QAAQ,CAAC,CAAC,EAAEW,IAAI,CAAC;QACvB,OAAOA,IAAI,CAACrC,MAAM;MACpB,CAAC,EAAEe,KAAK,GAAGe,IAAI,CAAC9B,MAAM,CAAC;IACzB;EAAA;IAAAS,GAAA;IAAAC,KAAA,EAEA,SAAA4B,YAAYA,CAACC,IAAU,EAAEC,GAAiB,EAAE;MAC1C,IAAQC,IAAA,GAAS,IAAI,CAACT,QAAQ,CAAtBS,IAAA;MACR,IAAAzB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,4BAAmB+C,IAAI,EAAEC,GAAG;MAC5B,IAAIC,IAAI,YAAYC,cAAK,EAAE;QACzBD,IAAI,CAACE,MAAM,CAAC,CAAC;MACf;MACA,IAAI,CAAC/B,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAV,MAAMA,CAAA,EAAG;MACP,IAAI,IAAI,CAACY,KAAK,CAACZ,MAAM,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACY,KAAK,CAACZ,MAAM,GAAG,IAAAgB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,2BAAiBD,cAAc;MACrD;MACA,OAAO,IAAI,CAACqB,KAAK,CAACZ,MAAM;IAC1B;EAAA;IAAAS,GAAA;IAAAC,KAAA,EAEA,SAAAkC,YAAYA,CAACC,MAAc,EAAEL,GAAiB,EAAE;MAC9C,IAAAxB,cAAA,CAAAtB,OAAA,EAAAF,KAAA,4BAAmBqD,MAAM,EAAEL,GAAG;MAC9B,IAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAoC,QAAQA,CAACC,OAA+B,EAAE;MACxC,IAAA/B,cAAA,CAAAtB,OAAA,EAAAF,KAAA,wBAAeuD,OAAO;MACtB,IAAI,CAACnC,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAsC,IAAIA,CAACjC,KAAa,EAAE;MAClB,WAAAC,cAAA,CAAAtB,OAAA,EAAAF,KAAA,oBAAkBuB,KAAK,EAAE,IAAI;IAC/B;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAuC,WAAWA,CAACC,KAAW,EAAE;MACvB,IAAAlC,cAAA,CAAAtB,OAAA,EAAAF,KAAA,2BAAkB0D,KAAK;MACvB,IAAI,CAACtC,KAAK,GAAG,CAAC,CAAC;IACjB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAmB,KAAKA,CAACd,KAAa,EAAmD;MAAA,IAAjDoC,KAA0B,GAAApD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,KAAK;MACrD,IAAIoD,KAAK,KAAKpC,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACf,MAAM,CAAC,CAAC,GAAGT,cAAc,CAAC,EAAE;QACrE,IAAM8D,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;QAC1B,IAAItC,KAAK,KAAK,CAAC,EAAE;UACf,IAAI,CAACuC,MAAM,CAAChB,YAAY,CAACe,KAAK,EAAE,IAAI,CAAC;UACrC,OAAO,IAAI;QACb;QACA,IAAI,CAACC,MAAM,CAAChB,YAAY,CAACe,KAAK,EAAE,IAAI,CAACE,IAAI,CAAC;QAC1C,OAAOF,KAAK;MACd;MACA,IAAME,IAAI,OAAAvC,cAAA,CAAAtB,OAAA,EAAAF,KAAA,qBAAeuB,KAAK,EAAEoC,KAAK,EAAC;MACtC,IAAI,CAACvC,KAAK,GAAG,CAAC,CAAC;MACf,OAAO2C,IAAI;IACb;EAAA;AAAA,EA5GkBC,oBAAS;AA8G7BhE,KAAK,CAACiE,QAAQ,GAAG,OAAO;AACxBjE,KAAK,CAACkE,OAAO,GAAG,GAAG;AACnBlE,KAAK,CAACmE,YAAY,GAAGjB,cAAK;AAC1BlD,KAAK,CAACoE,eAAe,GAAG,CAAClB,cAAK,EAAEmB,eAAM,EAAEC,oBAAS,EAAEC,aAAQ,CAAC;AAAA,IAEtDC,UAAU,GAAAvE,OAAA,CAAAuE,UAAA,0BAAAC,UAAA;EAAA,SAAAD,WAAA;IAAA,IAAAnE,gBAAA,CAAAH,OAAA,QAAAsE,UAAA;IAAA,WAAA5D,WAAA,CAAAV,OAAA,QAAAsE,UAAA,EAAAjE,SAAA;EAAA;EAAA,IAAAQ,UAAA,CAAAb,OAAA,EAAAsE,UAAA,EAAAC,UAAA;EAAA,WAAAzD,aAAA,CAAAd,OAAA,EAAAsE,UAAA;IAAAvD,GAAA;IAAAC,KAAA,EAId,SAAAwD,MAAMA,CAAA,EAAG;MACP,IAAAlD,cAAA,CAAAtB,OAAA,EAAAsE,UAAA;MACA,IAAI,CAACG,UAAU,GAAG,IAAIC,0BAAe,CAAC,IAAI,CAACC,OAAO,CAAC;IACrD;EAAA;IAAA5D,GAAA;IAAAC,KAAA,EAEA,SAAAC,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI2D,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC7D,KAAK,CAAC,CAAC,MAAA8D,cAAA,CAAA9E,OAAA,MAAA8E,cAAA,CAAA9E,OAAA,MACjC,IAAI,CAAC+E,OAAO,CAAC,CAAC,GACd,IAAI,CAACN,UAAU,CAACO,MAAM,CAAC,EAC3B,CAAC;IACJ;EAAA;IAAAjE,GAAA;IAAAC,KAAA,EAEA,SAAAa,MAAMA,CAACL,IAAY,EAAER,KAAc,EAAE;MACnC,IAAMiE,SAAS,GAAG,IAAI,CAACxD,MAAM,CAACC,KAAK,CAACF,IAAI,EAAEG,gBAAK,CAACuD,eAAe,CAAC;MAChE,IAAID,SAAS,IAAI,IAAI,EAAE;QACrB;QACA,IAAI,CAACR,UAAU,CAACQ,SAAS,CAACA,SAAS,EAAEjE,KAAK,CAAC;MAC7C;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAO,QAAQA,CAACF,KAAa,EAAEf,MAAc,EAAEkB,IAAY,EAAER,KAAc,EAAE;MACpE,IAAI,CAACa,MAAM,CAACL,IAAI,EAAER,KAAK,CAAC;IAC1B;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAgB,QAAQA,CAACX,KAAa,EAAEL,KAAa,EAAEiB,GAAa,EAAE;MAAA,IAAAkD,MAAA;MACpD,IAAIlD,GAAG,IAAI,IAAI,EAAE;QACf,IAAAX,cAAA,CAAAtB,OAAA,EAAAsE,UAAA,wBAAejD,KAAK,EAAEL,KAAK,EAAEiB,GAAG;QAChC;MACF;MACA,IAAMC,KAAK,GAAGlB,KAAK,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC/B,IAAMC,IAAI,GAAGF,KAAK,CAACkD,GAAG,CAAC,CAAC;MACxB,IAAMC,MAAM,GAAGnD,KAAK,CAACoD,GAAG,CAAE,UAAA3C,IAAI,EAAK;QACjC,IAAMH,KAAK,GAAG2C,MAAI,CAAC1D,MAAM,CAAC8D,MAAM,CAACzF,KAAK,CAACiE,QAAQ,CAAC;QAChDvB,KAAK,CAACR,QAAQ,CAAC,CAAC,EAAEW,IAAI,CAAC;QACvB,OAAOH,KAAK;MACd,CAAC,CAAC;MACF,IAAMM,GAAG,GAAG,IAAI,CAACX,KAAK,CAACd,KAAK,CAAC;MAC7BgE,MAAM,CAACG,OAAO,CAAE,UAAAhD,KAAK,EAAK;QACxB2C,MAAI,CAACvB,MAAM,CAAChB,YAAY,CAACJ,KAAK,EAAEM,GAAG,CAAC;MACtC,CAAC,CAAC;MACF,IAAIV,IAAI,EAAE;QACR,IAAI,CAACwB,MAAM,CAAChB,YAAY,CAAC,IAAI,CAACnB,MAAM,CAAC8D,MAAM,CAAC,MAAM,EAAEnD,IAAI,CAAC,EAAEU,GAAG,CAAC;MACjE;IACF;EAAA;AAAA,EA/CuBsB,oBAAS;AAiDlCE,UAAU,CAACmB,KAAK,GAAG9D,gBAAK,CAAC+D,UAAU;AACnC;;AAEA,SAASvE,UAAUA,CAAC0B,IAAe,EAAiB;EAAA,IAAf8C,MAAM,GAAAtF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,IAAI;EAChD,OAAOwC,IAAI,CACR+C,WAAW,CAACC,mBAAQ,CAAC,CACrBpD,MAAM,CAAC,UAACxB,KAAK,EAAE6E,IAAI,EAAK;IACvB,IAAIA,IAAI,CAACxF,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB,OAAOW,KAAK;IACd;IACA,OAAOA,KAAK,CAAC4D,MAAM,CAACiB,IAAI,CAAC9E,KAAK,CAAC,CAAC,EAAE+E,aAAa,CAACD,IAAI,EAAE,CAAC,CAAC,EAAEH,MAAM,CAAC,CAAC;EACpE,CAAC,EAAE,IAAIf,mBAAK,CAAC,CAAC,CAAC,CACdC,MAAM,CAAC,IAAI,EAAEkB,aAAa,CAAClD,IAAI,CAAC,CAAC;AACtC;AAEA,SAASkD,aAAaA,CACpBlD,IAAiB,EAGQ;EAAA,IAFzBkC,OAAgC,GAAA1E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,CAAC,CAAC;EAAA,IACrCsF,MAAM,GAAAtF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAqD,SAAA,GAAArD,SAAA,MAAG,IAAI;EAEb,IAAIwC,IAAI,IAAI,IAAI,EAAE,OAAOkC,OAAO;EAChC,IAAI,SAAS,IAAIlC,IAAI,IAAI,OAAOA,IAAI,CAACkC,OAAO,KAAK,UAAU,EAAE;IAC3DA,OAAO,OAAAD,cAAA,CAAA9E,OAAA,MAAA8E,cAAA,CAAA9E,OAAA,MACF+E,OAAO,GACPlC,IAAI,CAACkC,OAAO,CAAC,EACjB;IACD,IAAIY,MAAM,EAAE;MACV;MACA,OAAOZ,OAAO,CAAC,YAAY,CAAC;IAC9B;EACF;EACA,IACElC,IAAI,CAACe,MAAM,IAAI,IAAI,IACnBf,IAAI,CAACe,MAAM,CAACoC,OAAO,CAACjC,QAAQ,KAAK,QAAQ,IACzClB,IAAI,CAACe,MAAM,CAACoC,OAAO,CAACP,KAAK,KAAK5C,IAAI,CAACmD,OAAO,CAACP,KAAK,EAChD;IACA,OAAOV,OAAO;EAChB;EACA,OAAOgB,aAAa,CAAClD,IAAI,CAACe,MAAM,EAAEmB,OAAO,EAAEY,MAAM,CAAC;AACpD", "ignoreList": []}]}