# 金刚数瞳系统数据库设计

基于API文档分析，以下是支持金刚数瞳系统所有接口的数据库表结构设计。

## 用户和权限管理

### 1. 用户表 (sys_user)

已存在，不需要创建。包含以下字段：
- user_id: 用户ID (主键)
- dept_id: 部门ID
- user_name: 用户名
- nick_name: 昵称
- user_type: 用户类型
- email: 邮箱
- phonenumber: 手机号
- sex: 性别
- avatar: 头像
- password: 密码
- status: 状态
- del_flag: 删除标志
- login_ip: 登录IP
- login_date: 登录日期
- create_by: 创建者
- create_time: 创建时间
- update_by: 更新者
- update_time: 更新时间
- remark: 备注

### 2. 部门表 (sys_dept)

已存在，不需要创建。包含以下字段：
- dept_id: 部门ID (主键)
- parent_id: 父部门ID
- ancestors: 祖先列表
- dept_name: 部门名称
- order_num: 显示顺序
- leader: 负责人
- phone: 联系电话
- email: 邮箱
- status: 部门状态
- del_flag: 删除标志
- create_by: 创建者
- create_time: 创建时间
- update_by: 更新者
- update_time: 更新时间

### 3. 角色表 (sys_role)

已存在，不需要创建。包含以下字段：
- role_id: 角色ID (主键)
- role_name: 角色名称
- role_key: 角色权限字符串
- role_sort: 显示顺序
- data_scope: 数据范围
- menu_check_strictly: 菜单树选择项是否关联显示
- dept_check_strictly: 部门树选择项是否关联显示
- status: 角色状态
- del_flag: 删除标志
- create_by: 创建者
- create_time: 创建时间
- update_by: 更新者
- update_time: 更新时间
- remark: 备注

## 项目管理

### 4. 项目表 (account_projects)

```sql
CREATE TABLE account_projects (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    `desc` TEXT COMMENT '项目详细描述',
    owner_id BIGINT NOT NULL COMMENT '项目所有者ID',
    locked TINYINT(1) DEFAULT 0 COMMENT '项目锁定状态',
    preset TINYINT(1) DEFAULT 0 COMMENT '预置项目标识',
    adapter INT COMMENT '数据适配器类型编号',
    need_sync_keyword TINYINT(1) DEFAULT 0 COMMENT '关键词同步需求',
    config_ready TINYINT(1) DEFAULT 0 COMMENT '配置就绪状态',
    ws_status INT COMMENT '数据工作空间状态码',
    ws_etag_cut VARCHAR(32) COMMENT '精确匹配模式数据版本标识',
    ws_etag_fuzzy VARCHAR(32) COMMENT '模糊匹配模式数据版本标识',
    can_access_data TINYINT(1) DEFAULT 1 COMMENT '数据访问权限标识',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (owner_id) REFERENCES sys_user(user_id)
);
```

### 5. 项目成员表 (account_project_members)

```sql
CREATE TABLE account_project_members (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL COMMENT '角色(owner, admin, member, viewer)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
    UNIQUE KEY (project_id, user_id)
);
```

### 6. 成员洞察配置表 (member_insights)

```sql
CREATE TABLE member_insights (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    insight_type VARCHAR(50) NOT NULL COMMENT '分析类型标识',
    insight_text VARCHAR(100) NOT NULL COMMENT '分析类型说明',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
    UNIQUE KEY (project_id, user_id, insight_type)
);
```

## 会话和消息管理

### 7. 线程表 (threads)

```sql
CREATE TABLE threads (
    uid VARCHAR(36) PRIMARY KEY COMMENT '线程唯一标识符(UUIDv4格式)',
    project_id BIGINT NOT NULL COMMENT '所属项目ID',
    name VARCHAR(100) NOT NULL COMMENT '线程显示名称',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 8. 消息表 (messages)

```sql
CREATE TABLE messages (
    id VARCHAR(36) PRIMARY KEY COMMENT '消息唯一标识符(UUIDv4格式)',
    thread_uid VARCHAR(36) COMMENT '所属线程ID',
    title VARCHAR(50) NOT NULL COMMENT '消息标题',
    content TEXT COMMENT '消息内容(支持富文本)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建者',
    FOREIGN KEY (thread_uid) REFERENCES threads(uid)
);
```

## 看板和卡片管理

### 9. Pinboard表 (pinboards)

```sql
CREATE TABLE pinboards (
    uid VARCHAR(36) PRIMARY KEY COMMENT 'Pinboard唯一标识符',
    project_id BIGINT NOT NULL COMMENT '所属项目ID',
    name VARCHAR(100) NOT NULL COMMENT 'Pinboard名称',
    description TEXT COMMENT 'Pinboard描述',
    owner_id BIGINT NOT NULL COMMENT '创建者ID',
    is_template TINYINT(1) DEFAULT 0 COMMENT '是否为模板',
    is_shared TINYINT(1) DEFAULT 0 COMMENT '是否共享',
    share_type ENUM('none', 'user', 'all') DEFAULT 'none' COMMENT '共享类型',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否在回收站',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (owner_id) REFERENCES sys_user(user_id)
);
```

### 10. Pinboard标签表 (pinboard_tags)

```sql
CREATE TABLE pinboard_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
    project_id BIGINT NOT NULL COMMENT '所属项目ID',
    name VARCHAR(50) NOT NULL COMMENT '标签名称',
    color VARCHAR(20) COMMENT '标签颜色',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    UNIQUE KEY (project_id, name)
);
```

### 11. Pinboard标签关联表 (pinboard_tag_relations)

```sql
CREATE TABLE pinboard_tag_relations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
    FOREIGN KEY (tag_id) REFERENCES pinboard_tags(id),
    UNIQUE KEY (pinboard_uid, tag_id)
);
```

### 12. 卡片表 (cards)

```sql
CREATE TABLE cards (
    uid VARCHAR(36) PRIMARY KEY COMMENT '卡片唯一标识符',
    pinboard_uid VARCHAR(36) NOT NULL COMMENT '所属Pinboard ID',
    title VARCHAR(200) NOT NULL COMMENT '卡片标题',
    card_type INT NOT NULL COMMENT '卡片类型(1-分析卡片 2-监控卡片 3-报表卡片 4-仪表盘卡片)',
    sentence_type INT DEFAULT 0 COMMENT '语句类型',
    result_type INT DEFAULT 0 COMMENT '结果类型',
    y_axis_scale TINYINT(1) DEFAULT 1 COMMENT '纵轴缩放开关',
    datetime_anchor VARCHAR(30) COMMENT '时间锚点',
    position_x INT DEFAULT 0 COMMENT 'X坐标位置',
    position_y INT DEFAULT 0 COMMENT 'Y坐标位置',
    width INT DEFAULT 4 COMMENT '卡片宽度',
    height INT DEFAULT 3 COMMENT '卡片高度',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid)
);
```

### 13. 卡片配置表 (card_configs)

```sql
CREATE TABLE card_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    card_uid VARCHAR(36) NOT NULL COMMENT '卡片ID',
    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值(JSON格式)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (card_uid) REFERENCES cards(uid),
    UNIQUE KEY (card_uid, config_key)
);
```

### 14. 卡片数据单元表 (card_units)

```sql
CREATE TABLE card_units (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '单元ID',
    card_uid VARCHAR(36) NOT NULL COMMENT '卡片ID',
    identity VARCHAR(50) NOT NULL COMMENT '单元唯一标识',
    unit_type INT NOT NULL COMMENT '单元类型标识',
    unit_candidate_type INT NOT NULL COMMENT '单元候选类型标识',
    start_time VARCHAR(30) COMMENT '单元起始时间',
    end_time VARCHAR(30) COMMENT '单元结束时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (card_uid) REFERENCES cards(uid)
);
```

### 15. 卡片单元分词表 (card_unit_tokens)

```sql
CREATE TABLE card_unit_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分词ID',
    unit_id BIGINT NOT NULL COMMENT '单元ID',
    word VARCHAR(100) NOT NULL COMMENT '分词词语内容',
    prop VARCHAR(50) NOT NULL COMMENT '分词属性标识符',
    start_time VARCHAR(30) COMMENT '分词起始位置',
    end_time VARCHAR(30) COMMENT '分词结束位置',
    is_locked TINYINT(1) DEFAULT 0 COMMENT '分词是否被锁定',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (unit_id) REFERENCES card_units(id)
);
```

### 16. 卡片过滤器表 (card_filters)

```sql
CREATE TABLE card_filters (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '过滤器ID',
    card_uid VARCHAR(36) NOT NULL COMMENT '卡片ID',
    text VARCHAR(200) NOT NULL COMMENT '过滤器文本',
    filter_type VARCHAR(50) NOT NULL COMMENT '过滤器类型',
    can_delete TINYINT(1) DEFAULT 1 COMMENT '是否可删除',
    time_type VARCHAR(50) COMMENT '时间类型',
    word VARCHAR(100) COMMENT '词语',
    prop VARCHAR(50) COMMENT '属性',
    time_unit INT COMMENT '时间单位',
    start_time VARCHAR(30) COMMENT '开始时间',
    end_time VARCHAR(30) COMMENT '结束时间',
    value INT COMMENT '值',
    time_regex_granularity VARCHAR(50) COMMENT '时间正则粒度',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (card_uid) REFERENCES cards(uid)
);
```

## 任务和导出管理

### 17. 异步任务表 (async_tasks)

```sql
CREATE TABLE async_tasks (
    uid VARCHAR(36) PRIMARY KEY COMMENT '任务唯一标识符',
    project_id BIGINT NOT NULL COMMENT '所属项目ID',
    task_type INT NOT NULL COMMENT '任务类型(1-数据导入，2-模型训练，3-数据预处理，4-数据清洗，5-数据标注，6-数据验证，7-数据转换，8-数据导出)',
    status INT NOT NULL DEFAULT 0 COMMENT '任务状态(0-进行中，1-成功，2-失败，3-已取消，4-已暂停)',
    progress_percentage INT DEFAULT 0 COMMENT '进度百分比',
    estimated_time_remaining VARCHAR(50) COMMENT '预计剩余时间',
    download_url VARCHAR(255) COMMENT '下载URL',
    params TEXT COMMENT '任务参数(JSON格式)',
    result TEXT COMMENT '任务结果(JSON格式)',
    error_message TEXT COMMENT '错误信息',
    creator BIGINT COMMENT '创建者ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (creator) REFERENCES sys_user(user_id)
);
```

## 用户行为和查询记录

### 18. 问题收藏表 (question_stars)

```sql
CREATE TABLE question_stars (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question TEXT NOT NULL COMMENT '问题内容',
    tokens TEXT COMMENT '问题标记(JSON格式)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id)
);
```

### 19. 历史查询记录表 (history_queries)

```sql
CREATE TABLE history_queries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    identity VARCHAR(32) NOT NULL COMMENT '查询唯一标识(MD5哈希)',
    tokens TEXT COMMENT '查询标记(JSON格式)',
    star TINYINT(1) DEFAULT 0 COMMENT '是否收藏',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id)
);
```

### 20. 热门问题表 (top_questions)

```sql
CREATE TABLE top_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    tokens TEXT NOT NULL COMMENT '问题标记(JSON格式)',
    query_count INT DEFAULT 1 COMMENT '查询次数',
    last_queried_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后查询时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 21. 常用问题表 (frequent_questions)

```sql
CREATE TABLE frequent_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    tokens TEXT NOT NULL COMMENT '问题标记(JSON格式)',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 22. Pinboard收藏表 (pinboard_stars)

```sql
CREATE TABLE pinboard_stars (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏ID',
    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
    UNIQUE KEY (pinboard_uid, user_id)
);
```

### 23. Pinboard共享表 (pinboard_shares)

```sql
CREATE TABLE pinboard_shares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '共享ID',
    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
    user_id BIGINT COMMENT '共享目标用户ID(为空表示共享给所有人)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建者',
    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id)
);
```

## 监控和预警

### 24. 预警表 (alerts)

```sql
CREATE TABLE alerts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预警ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    card_uid VARCHAR(36) COMMENT '关联卡片ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '预警类型(DEVICE_OFFLINE, DATA_ANOMALY等)',
    alert_name VARCHAR(100) NOT NULL COMMENT '预警名称',
    alert_condition TEXT NOT NULL COMMENT '预警条件(JSON格式)',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    last_triggered_at DATETIME COMMENT '最后触发时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id),
    FOREIGN KEY (card_uid) REFERENCES cards(uid)
);
```

### 25. 预警记录表 (alert_logs)

```sql
CREATE TABLE alert_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    alert_id BIGINT NOT NULL COMMENT '预警ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '预警类型',
    triggered_at DATETIME NOT NULL COMMENT '触发时间',
    alert_data TEXT COMMENT '触发数据(JSON格式)',
    FOREIGN KEY (alert_id) REFERENCES alerts(id)
);
```

## 数据分析和模型

### 26. 项目模型表 (project_models)

```sql
CREATE TABLE project_models (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模型ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    model_type VARCHAR(50) NOT NULL COMMENT '模型类型',
    model_config TEXT COMMENT '模型配置(JSON格式)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 27. 维度令牌表 (dimension_tokens)

```sql
CREATE TABLE dimension_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '令牌ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    token_name VARCHAR(100) NOT NULL COMMENT '令牌名称',
    token_key VARCHAR(50) NOT NULL COMMENT '令牌键',
    token_value VARCHAR(255) NOT NULL COMMENT '令牌值',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 28. 项目洞察表 (project_insights)

```sql
CREATE TABLE project_insights (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '洞察ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    insight_type VARCHAR(50) NOT NULL COMMENT '洞察类型',
    metrics TEXT COMMENT '核心指标数值(JSON格式)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 29. 洞察时间线表 (insight_timelines)

```sql
CREATE TABLE insight_timelines (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '时间线ID',
    insight_id BIGINT NOT NULL COMMENT '洞察ID',
    timestamp DATETIME NOT NULL COMMENT '时间点',
    value DECIMAL(20,4) NOT NULL COMMENT '数值',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (insight_id) REFERENCES project_insights(id)
);
```

### 30. 问题引导表 (question_guides)

```sql
CREATE TABLE question_guides (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '引导ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    guide_text VARCHAR(200) NOT NULL COMMENT '引导文本',
    guide_tokens TEXT COMMENT '引导标记(JSON格式)',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

### 31. 自动补全建议表 (auto_complete_suggestions)

```sql
CREATE TABLE auto_complete_suggestions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '建议ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    input_prefix VARCHAR(100) NOT NULL COMMENT '输入前缀',
    suggestion VARCHAR(200) NOT NULL COMMENT '补全建议',
    weight INT DEFAULT 0 COMMENT '权重',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES account_projects(id)
);
```

## 系统配置

### 32. API文档配置表 (api_docs_config)

```sql
CREATE TABLE api_docs_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    UNIQUE KEY (config_key)
);
```

### 33. 前端初始化配置表 (react_init_config)

```sql
CREATE TABLE react_init_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    UNIQUE KEY (config_key)
);
``` 