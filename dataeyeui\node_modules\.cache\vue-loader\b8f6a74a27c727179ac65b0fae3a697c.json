{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\ImagePreview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\ImagePreview\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzRXh0ZXJuYWwgfSBmcm9tICJAL3V0aWxzL3ZhbGlkYXRlIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSW1hZ2VQcmV2aWV3IiwKICBwcm9wczogewogICAgc3JjOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogIiIKICAgIH0sCiAgICB3aWR0aDogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICBkZWZhdWx0OiAiIgogICAgfSwKICAgIGhlaWdodDogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICBkZWZhdWx0OiAiIgogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHJlYWxTcmMoKSB7CiAgICAgIGlmICghdGhpcy5zcmMpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IHJlYWxfc3JjID0gdGhpcy5zcmMuc3BsaXQoIiwiKVswXTsKICAgICAgaWYgKGlzRXh0ZXJuYWwocmVhbF9zcmMpKSB7CiAgICAgICAgcmV0dXJuIHJlYWxfc3JjOwogICAgICB9CiAgICAgIHJldHVybiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgcmVhbF9zcmM7CiAgICB9LAogICAgcmVhbFNyY0xpc3QoKSB7CiAgICAgIGlmICghdGhpcy5zcmMpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IHJlYWxfc3JjX2xpc3QgPSB0aGlzLnNyYy5zcGxpdCgiLCIpOwogICAgICBsZXQgc3JjTGlzdCA9IFtdOwogICAgICByZWFsX3NyY19saXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGlzRXh0ZXJuYWwoaXRlbSkpIHsKICAgICAgICAgIHJldHVybiBzcmNMaXN0LnB1c2goaXRlbSk7CiAgICAgICAgfQogICAgICAgIHJldHVybiBzcmNMaXN0LnB1c2gocHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArIGl0ZW0pOwogICAgICB9KTsKICAgICAgcmV0dXJuIHNyY0xpc3Q7CiAgICB9LAogICAgcmVhbFdpZHRoKCkgewogICAgICByZXR1cm4gdHlwZW9mIHRoaXMud2lkdGggPT0gInN0cmluZyIgPyB0aGlzLndpZHRoIDogYCR7dGhpcy53aWR0aH1weGA7CiAgICB9LAogICAgcmVhbEhlaWdodCgpIHsKICAgICAgcmV0dXJuIHR5cGVvZiB0aGlzLmhlaWdodCA9PSAic3RyaW5nIiA/IHRoaXMuaGVpZ2h0IDogYCR7dGhpcy5oZWlnaHR9cHhgOwogICAgfQogIH0sCn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImagePreview", "sourcesContent": ["<template>\n  <el-image\n    :src=\"`${realSrc}`\"\n    fit=\"cover\"\n    :style=\"`width:${realWidth};height:${realHeight};`\"\n    :preview-src-list=\"realSrcList\"\n  >\n    <div slot=\"error\" class=\"image-slot\">\n      <i class=\"el-icon-picture-outline\"></i>\n    </div>\n  </el-image>\n</template>\n\n<script>\nimport { isExternal } from \"@/utils/validate\";\n\nexport default {\n  name: \"ImagePreview\",\n  props: {\n    src: {\n      type: String,\n      default: \"\"\n    },\n    width: {\n      type: [Number, String],\n      default: \"\"\n    },\n    height: {\n      type: [Number, String],\n      default: \"\"\n    }\n  },\n  computed: {\n    realSrc() {\n      if (!this.src) {\n        return;\n      }\n      let real_src = this.src.split(\",\")[0];\n      if (isExternal(real_src)) {\n        return real_src;\n      }\n      return process.env.VUE_APP_BASE_API + real_src;\n    },\n    realSrcList() {\n      if (!this.src) {\n        return;\n      }\n      let real_src_list = this.src.split(\",\");\n      let srcList = [];\n      real_src_list.forEach(item => {\n        if (isExternal(item)) {\n          return srcList.push(item);\n        }\n        return srcList.push(process.env.VUE_APP_BASE_API + item);\n      });\n      return srcList;\n    },\n    realWidth() {\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`;\n    },\n    realHeight() {\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`;\n    }\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.el-image {\n  border-radius: 5px;\n  background-color: #ebeef5;\n  box-shadow: 0 0 5px 1px #ccc;\n  ::v-deep .el-image__inner {\n    transition: all 0.3s;\n    cursor: pointer;\n    &:hover {\n      transform: scale(1.2);\n    }\n  }\n  ::v-deep .image-slot {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n    color: #909399;\n    font-size: 30px;\n  }\n}\n</style>\n"]}]}