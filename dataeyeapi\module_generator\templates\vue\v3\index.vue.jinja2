<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      {% for column in columns %}
      {% if column.query %}
      {% set dictType = column.dict_type %}
      {% set AttrName = column.python_field[0] | upper + column.python_field[1:] %}
      {% set parentheseIndex = column.column_comment.find("（") %}
      {% set comment = column.column_comment[:parentheseIndex] if parentheseIndex != -1 else column.column_comment %}
      {% if column.html_type == "input" %}
      <el-form-item label="{{ comment }}" prop="{{ column.python_field }}">
        <el-input
          v-model="queryParams.{{ column.python_field }}"
          placeholder="请输入{{ comment }}"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      {% elif (column.html_type == "select" or column.html_type == "radio") and dictType %}
      <el-form-item label="{{ comment }}" prop="{{ column.python_field }}">
        <el-select v-model="queryParams.{{ column.python_field }}" placeholder="请选择{{ comment }}" clearable style="width: 240px">
          <el-option
            v-for="dict in {{ dictType }}"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      {% elif (column.html_type == "select" or column.html_type == "radio") and not dictType %}
      <el-form-item label="{{ comment }}" prop="{{ column.python_field }}">
        <el-select v-model="queryParams.{{ column.python_field }}" placeholder="请选择{{ comment }}" clearable style="width: 240px">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      {% elif column.html_type == "datetime" and column.query_type != "BETWEEN" %}
      <el-form-item label="{{ comment }}" prop="{{ column.python_field }}">
        <el-date-picker
          v-model="queryParams.{{ column.python_field }}"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择{{ comment }}"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      {% elif column.html_type == "datetime" and column.query_type == "BETWEEN" %}
      <el-form-item label="{{ comment }}" style="width: 308px">
        <el-date-picker
          v-model="daterange{{ AttrName }}"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        />
      </el-form-item>
      {% endif %}
      {% endif %}
      {% endfor %}
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['{{ moduleName }}:{{ businessName }}:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['{{ moduleName }}:{{ businessName }}:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['{{ moduleName }}:{{ businessName }}:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['{{ moduleName }}:{{ businessName }}:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="{{ businessName }}List" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      {% for column in columns %}
      {% set pythonField = column.python_field %}
      {% set parentheseIndex = column.column_comment.find("（") %}
      {% set comment = column.column_comment[:parentheseIndex] if parentheseIndex != -1 else column.column_comment %}
      {% if column.pk %}
      <el-table-column label="{{ comment }}" align="center" prop="{{ pythonField }}" />
      {% elif column.list and column.html_type == "datetime" %}
      <el-table-column label="{{ comment }}" align="center" prop="{{ pythonField }}" width="180">
        <template #default="scope">
          <span>{% raw %}{{{% endraw %} parseTime(scope.row.{{ pythonField }}, '{y}-{m}-{d}') {% raw %}}}{% endraw %}</span>
        </template>
      </el-table-column>
      {% elif column.list and column.html_type == "imageUpload" %}
      <el-table-column label="{{ comment }}" align="center" prop="{{ pythonField }}" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.{{ pythonField }}" :width="50" :height="50"/>
        </template>
      </el-table-column>
      {% elif column.list and column.dict_type %}
      <el-table-column label="{{ comment }}" align="center" prop="{{ pythonField }}">
        <template #default="scope">
          {% if column.html_type == "checkbox" %}
            <dict-tag :options="{{ column.dict_type }}" :value="scope.row.{{ pythonField }} ? scope.row.{{ pythonField }}.split(',') : []"/>
          {% else %}
            <dict-tag :options="{{ column.dict_type }}" :value="scope.row.{{ pythonField }}"/>
          {% endif %}
        </template>
      </el-table-column>
      {% elif column.list and pythonField %}
      <el-table-column label="{{ comment }}" align="center" prop="{{ pythonField }}" />
      {% endif %}
      {% endfor %}
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['{{ moduleName }}:{{ businessName }}:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['{{ moduleName }}:{{ businessName }}:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改{{ functionName }}对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="{{ businessName }}Ref" :model="form" :rules="rules" label-width="80px">
      {% for column in columns %}
      {% set field = column.python_field %}
      {% if (column.insert or column.edit) and not column.pk and column.column_name not in column_not_add_show + column_not_edit_show %}
      {% if column.usable_column or not column.super_column %}
      {% set parentheseIndex = column.column_comment.find("（") %}
      {% set comment = column.column_comment[:parentheseIndex] if parentheseIndex != -1 else column.column_comment %}
      {% set dictType = column.dict_type %}
      {% if column.html_type == "input" %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-input v-model="form.{{ field }}" placeholder="请输入{{ comment }}" />
      </el-form-item>
      {% elif column.html_type == "imageUpload" %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <image-upload v-model="form.{{ field }}"/>
      </el-form-item>
      {% elif column.html_type == "fileUpload" %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <file-upload v-model="form.{{ field }}"/>
      </el-form-item>
      {% elif column.html_type == "editor" %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <editor v-model="form.{{ field }}" :min-height="192"/>
      </el-form-item>
      {% elif column.html_type == "select" and dictType %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-select v-model="form.{{ field }}" placeholder="请选择{{ comment }}">
          <el-option
            v-for="dict in {{ dictType }}"
            :key="dict.value"
            :label="dict.label"
            {% if column.python_type == 'int' %}
            :value="parseInt(dict.value)"
            {% else %}
            :value="dict.value"
            {% endif %}
          ></el-option>
        </el-select>
      </el-form-item>
      {% elif column.html_type == "select" and not dictType %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-select v-model="form.{{ field }}" placeholder="请选择{{ comment }}">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      {% elif column.html_type == "checkbox" and dictType %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-checkbox-group v-model="form.{{ field }}">
          <el-checkbox
            v-for="dict in {{ dictType }}"
            :key="dict.value"
            :label="dict.value">
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      {% elif column.html_type == "checkbox" and not dictType %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-checkbox-group v-model="form.{{ field }}">
          <el-checkbox>请选择字典生成</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      {% elif column.html_type == "radio" and dictType %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-radio-group v-model="form.{{ field }}">
          <el-radio
            v-for="dict in {{ dictType }}"
            :key="dict.value"
            {% if column.python_type == 'int' %}
            :label="parseInt(dict.value)"
            {% else %}
            :label="dict.value"
            {% endif %}>
            {% raw %}{{{% endraw %} dict.label {% raw %}}}{% endraw %}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      {% elif column.html_type == "radio" and not dictType %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-radio-group v-model="form.{{ field }}">
          <el-radio label="请选择字典生成" value="" />
        </el-radio-group>
      </el-form-item>
      {% elif column.html_type == "datetime" %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-date-picker clearable
          v-model="form.{{ field }}"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择{{ comment }}">
        </el-date-picker>
      </el-form-item>
      {% elif column.html_type == "textarea" %}
      <el-form-item v-if="renderField({{ column.insert | lower }}, {{ column.edit | lower }})" label="{{ comment }}" prop="{{ field }}">
        <el-input v-model="form.{{ field }}" type="textarea" placeholder="请输入内容" />
      </el-form-item>
      {% endif %}
      {% endif %}
      {% endif %}
      {% endfor %}

      {% if table.sub %}
      <el-divider content-position="center">{{ subTable.functionName }}信息</el-divider>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="Plus" @click="handleAdd{{ subClassName }}">添加</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="Delete" @click="handleDelete{{ subClassName }}">删除</el-button>
        </el-col>
      </el-row>
      <el-table :data="{{ subclassName }}List" :row-class-name="row{{ subClassName }}Index" @selection-change="handle{{ subClassName }}SelectionChange" ref="{{ subclassName }}">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" align="center" prop="index" width="50"/>
        {% for column in subTable.columns %}
        {% set pythonField = column.python_field %}
        {% set parentheseIndex = column.column_comment.find("（") %}
        {% set comment = column.column_comment[:parentheseIndex] if parentheseIndex != -1 else column.column_comment %}
        {% if column.pk or pythonField == subTableFkclassName %}
        {% elif column.list and column.html_type == 'input' %}
        <el-table-column label="{{ comment }}" prop="{{ pythonField }}" width="150">
          <template #default="scope">
            <el-input v-model="scope.row.{{ pythonField }}" placeholder="请输入{{ comment }}" />
          </template>
        </el-table-column>
        {% elif column.list and column.html_type == 'datetime' %}
        <el-table-column label="{{ comment }}" prop="{{ pythonField }}" width="240">
          <template #default="scope">
            <el-date-picker clearable
              v-model="scope.row.{{ pythonField }}"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择{{ comment }}">
            </el-date-picker>
          </template>
        </el-table-column>
        {% elif column.list and (column.html_type == 'select' or column.html_type == 'radio') and column.dict_type %}
        <el-table-column label="{{ comment }}" prop="{{ pythonField }}" width="150">
          <template #default="scope">
            <el-select v-model="scope.row.{{ pythonField }}" placeholder="请选择{{ comment }}">
              <el-option
                v-for="dict in {{ column.dict_type }}"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        {% elif column.list and (column.html_type == 'select' or column.html_type == 'radio') and not column.dict_type %}
        <el-table-column label="{{ comment }}" prop="{{ pythonField }}" width="150">
          <template #default="scope">
            <el-select v-model="scope.row.{{ pythonField }}" placeholder="请选择{{ comment }}">
              <el-option label="请选择字典生成" value="" />
            </el-select>
          </template>
        </el-table-column>
        {% endif %}
        {% endfor %}
      </el-table>
      {% endif %}
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="{{ BusinessName }}">
import { list{{ BusinessName }}, get{{ BusinessName }}, del{{ BusinessName }}, add{{ BusinessName }}, update{{ BusinessName }} } from "@/api/{{ moduleName }}/{{ businessName }}";

const { proxy } = getCurrentInstance();
{% if dicts %}
{% set dictsNoSymbol = dicts.replace("'", "") %}
const { {{ dictsNoSymbol }} } = proxy.useDict({{ dicts }});
{% endif %}

const {{ businessName }}List = ref([]);
{% if table.sub %}
const {{ subclassName }}List = ref([]);
{% endif %}
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
{% if table.sub %}
const checked{{ subClassName }} = ref([]);
{% endif %}
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
{% for column in columns %}
{% if column.html_type == "datetime" and column.query_type == "BETWEEN" %}
{% set AttrName = column.python_field[0] | upper + column.python_field[1:] %}
const daterange{{ AttrName }} = ref([]);
{% endif %}
{% endfor %}

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    {% for column in columns %}
    {% if column.query %}
    {{ column.python_field }}: null,
    {% endif %}
    {% endfor %}
  },
  rules: {
    {% for column in columns %}
    {% if column.required %}
    {% set parentheseIndex = column.column_comment.find("（") %}
    {% set comment = column.column_comment[:parentheseIndex] if parentheseIndex != -1 else column.column_comment %}
    {{ column.python_field }}: [
      { required: true, message: "{{ comment }}不能为空", trigger: "{% if column.html_type == 'select' or column.html_type == 'radio' %}change{% else %}blur{% endif %}" }
    ],
    {% endif %}
    {% endfor %}
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询{{ functionName }}列表 */
function getList() {
  loading.value = true;
  {% for column in columns %}
  {% if column.html_type == "datetime" and column.query_type == "BETWEEN" %}
  queryParams.value.params = {};
  {% endif %}
  {% endfor %}
  {% for column in columns %}
  {% if column.html_type == "datetime" and column.query_type == "BETWEEN" %}
  {% set AttrName = column.python_field[0] | upper + column.python_field[1:] %}
  if (null != daterange{{ AttrName }} && '' != daterange{{ AttrName }}) {
    queryParams.value.begin{{ AttrName }} = daterange{{ AttrName }}.value[0];
    queryParams.value.end{{ AttrName }} = daterange{{ AttrName }}.value[1];
  }
  {% endif %}
  {% endfor %}
  list{{ BusinessName }}(queryParams.value).then(response => {
    {{ businessName }}List.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    {% for column in columns %}
    {% if column.html_type == "checkbox" %}
    {{ column.python_field }}: [],
    {% else %}
    {{ column.python_field }}: null,
    {% endif %}
    {% endfor %}
  };
  {% if table.sub %}
  {{ subclassName }}List.value = [];
  {% endif %}
  proxy.resetForm("{{ businessName }}Ref");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  {% for column in columns %}
  {% if column.html_type == "datetime" and column.query_type == "BETWEEN" %}
  {% set AttrName = column.python_field[0] | upper + column.python_field[1:] %}
  daterange{{ AttrName }}.value = [];
  {% endif %}
  {% endfor %}
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.{{ pkColumn.python_field }});
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加{{ functionName }}";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _{{ pkColumn.python_field }} = row.{{ pkColumn.python_field }} || ids.value;
  get{{ BusinessName }}(_{{ pkColumn.python_field }}).then(response => {
    form.value = response.data;
    {% for column in columns %}
    {% if column.html_type == "checkbox" %}
    form.value.{{ column.python_field }} = form.value.{{ column.python_field }}.split(",");
    {% endif %}
    {% endfor %}
    {% if table.sub %}
    {{ subclassName }}List.value = response.data.{{ subclassName }}List;
    {% endif %}
    open.value = true;
    title.value = "修改{{ functionName }}";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["{{ businessName }}Ref"].validate(valid => {
    if (valid) {
      {% for column in columns %}
      {% if column.html_type == "checkbox" %}
      form.value.{{ column.python_field }} = form.value.{{ column.python_field }}.join(",");
      {% endif %}
      {% endfor %}
      {% if table.sub %}
      form.value.{{ subclassName }}List = {{ subclassName }}List.value;
      {% endif %}
      if (form.value.{{ pkColumn.python_field }} != null) {
        update{{ BusinessName }}(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        add{{ BusinessName }}(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _{{ pkColumn.python_field }}s = row.{{ pkColumn.python_field }} || ids.value;
  proxy.$modal.confirm('是否确认删除{{ functionName }}编号为"' + _{{ pkColumn.python_field }}s + '"的数据项？').then(function() {
    return del{{ BusinessName }}(_{{ pkColumn.python_field }}s);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

{% if table.sub %}
/** {{ subTable.functionName }}序号 */
function row{{ subClassName }}Index({ row, rowIndex }) {
  row.index = rowIndex + 1;
}

/** {{ subTable.functionName }}添加按钮操作 */
function handleAdd{{ subClassName }}() {
  let obj = {};
  {% for column in subTable.columns %}
  {% if column.pk or column.python_field == subTableFkclassName %}
  {% elif column.list and column.python_field %}
  obj.{{ column.python_field }} = "";
  {% endif %}
  {% endfor %}
  {{ subclassName }}List.value.push(obj);
}

/** {{ subTable.functionName }}删除按钮操作 */
function handleDelete{{ subClassName }}() {
  if (checked{{ subClassName }}.value.length == 0) {
    proxy.$modal.msgError("请先选择要删除的{{ subTable.functionName }}数据");
  } else {
    const {{ subclassName }}s = {{ subclassName }}List.value;
    const checked{{ subClassName }}s = checked{{ subClassName }}.value;
    {{ subclassName }}List.value = {{ subclassName }}s.filter(function(item) {
      return checked{{ subClassName }}s.indexOf(item.index) == -1
    });
  }
}

/** 复选框选中数据 */
function handle{{ subClassName }}SelectionChange(selection) {
  checked{{ subClassName }}.value = selection.map(item => item.index)
}
{% endif %}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('{{ moduleName }}/{{ businessName }}/export', {
    ...queryParams.value
  }, `{{ businessName }}_${new Date().getTime()}.xlsx`);
}

/** 是否渲染字段 */
function renderField(insert, edit) {
  return form.value.{{ pkColumn.python_field }} == null ? insert : edit;
}

getList();
</script>