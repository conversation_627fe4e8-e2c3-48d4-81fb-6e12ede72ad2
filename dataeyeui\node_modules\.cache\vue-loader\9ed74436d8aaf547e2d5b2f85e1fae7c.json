{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\ImageUpload\\index.vue?vue&type=style&index=0&id=82a94682&scoped=true&lang=scss", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\ImageUpload\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgovLyAuZWwtdXBsb2FkLS1waWN0dXJlLWNhcmQg5o6n5Yi25Yqg5Y+36YOo5YiGCjo6di1kZWVwLmhpZGUgLmVsLXVwbG9hZC0tcGljdHVyZS1jYXJkIHsKICAgIGRpc3BsYXk6IG5vbmU7Cn0KLy8g5Y675o6J5Yqo55S75pWI5p6cCjo6di1kZWVwIC5lbC1saXN0LWVudGVyLWFjdGl2ZSwKOjp2LWRlZXAgLmVsLWxpc3QtbGVhdmUtYWN0aXZlIHsKICAgIHRyYW5zaXRpb246IGFsbCAwczsKfQoKOjp2LWRlZXAgLmVsLWxpc3QtZW50ZXIsIC5lbC1saXN0LWxlYXZlLWFjdGl2ZSB7CiAgb3BhY2l0eTogMDsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\n  <div class=\"component-upload-image\">\n    <el-upload\n      multiple\n      :action=\"uploadImgUrl\"\n      list-type=\"picture-card\"\n      :on-success=\"handleUploadSuccess\"\n      :before-upload=\"handleBeforeUpload\"\n      :limit=\"limit\"\n      :on-error=\"handleUploadError\"\n      :on-exceed=\"handleExceed\"\n      ref=\"imageUpload\"\n      :on-remove=\"handleDelete\"\n      :show-file-list=\"true\"\n      :headers=\"headers\"\n      :file-list=\"fileList\"\n      :on-preview=\"handlePictureCardPreview\"\n      :class=\"{hide: this.fileList.length >= this.limit}\"\n    >\n      <i class=\"el-icon-plus\"></i>\n    </el-upload>\n\n    <!-- 上传提示 -->\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\n      请上传\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\n      的文件\n    </div>\n\n    <el-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"预览\"\n      width=\"800\"\n      append-to-body\n    >\n      <img\n        :src=\"dialogImageUrl\"\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { isExternal } from \"@/utils/validate\";\n\nexport default {\n  props: {\n    value: [String, Object, Array],\n    // 图片数量限制\n    limit: {\n      type: Number,\n      default: 5,\n    },\n    // 大小限制(MB)\n    fileSize: {\n       type: Number,\n      default: 5,\n    },\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\n    fileType: {\n      type: Array,\n      default: () => [\"png\", \"jpg\", \"jpeg\"],\n    },\n    // 是否显示提示\n    isShowTip: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      number: 0,\n      uploadList: [],\n      dialogImageUrl: \"\",\n      dialogVisible: false,\n      hideUpload: false,\n      baseUrl: process.env.VUE_APP_BASE_API,\n      uploadImgUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n      fileList: []\n    };\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val) {\n          // 首先将值转为数组\n          const list = Array.isArray(val) ? val : this.value.split(',');\n          // 然后将数组转为对象数组\n          this.fileList = list.map(item => {\n            if (typeof item === \"string\") {\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\n                  item = { name: this.baseUrl + item, url: this.baseUrl + item };\n              } else {\n                  item = { name: item, url: item };\n              }\n            }\n            return item;\n          });\n        } else {\n          this.fileList = [];\n          return [];\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  computed: {\n    // 是否显示提示\n    showTip() {\n      return this.isShowTip && (this.fileType || this.fileSize);\n    },\n  },\n  methods: {\n    // 上传前loading加载\n    handleBeforeUpload(file) {\n      let isImg = false;\n      if (this.fileType.length) {\n        let fileExtension = \"\";\n        if (file.name.lastIndexOf(\".\") > -1) {\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1);\n        }\n        isImg = this.fileType.some(type => {\n          if (file.type.indexOf(type) > -1) return true;\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true;\n          return false;\n        });\n      } else {\n        isImg = file.type.indexOf(\"image\") > -1;\n      }\n\n      if (!isImg) {\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`);\n        return false;\n      }\n      if (file.name.includes(',')) {\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!');\n        return false;\n      }\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\n        if (!isLt) {\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);\n          return false;\n        }\n      }\n      this.$modal.loading(\"正在上传图片，请稍候...\");\n      this.number++;\n    },\n    // 文件个数超出\n    handleExceed() {\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);\n    },\n    // 上传成功回调\n    handleUploadSuccess(res, file) {\n      if (res.code === 200) {\n        this.uploadList.push({ name: res.fileName, url: res.fileName });\n        this.uploadedSuccessfully();\n      } else {\n        this.number--;\n        this.$modal.closeLoading();\n        this.$modal.msgError(res.msg);\n        this.$refs.imageUpload.handleRemove(file);\n        this.uploadedSuccessfully();\n      }\n    },\n    // 删除图片\n    handleDelete(file) {\n      const findex = this.fileList.map(f => f.name).indexOf(file.name);\n      if (findex > -1) {\n        this.fileList.splice(findex, 1);\n        this.$emit(\"input\", this.listToString(this.fileList));\n      }\n    },\n    // 上传失败\n    handleUploadError() {\n      this.$modal.msgError(\"上传图片失败，请重试\");\n      this.$modal.closeLoading();\n    },\n    // 上传结束处理\n    uploadedSuccessfully() {\n      if (this.number > 0 && this.uploadList.length === this.number) {\n        this.fileList = this.fileList.concat(this.uploadList);\n        this.uploadList = [];\n        this.number = 0;\n        this.$emit(\"input\", this.listToString(this.fileList));\n        this.$modal.closeLoading();\n      }\n    },\n    // 预览\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.dialogVisible = true;\n    },\n    // 对象转成指定字符串分隔\n    listToString(list, separator) {\n      let strs = \"\";\n      separator = separator || \",\";\n      for (let i in list) {\n        if (list[i].url) {\n          strs += list[i].url.replace(this.baseUrl, \"\") + separator;\n        }\n      }\n      return strs != '' ? strs.substr(0, strs.length - 1) : '';\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n// .el-upload--picture-card 控制加号部分\n::v-deep.hide .el-upload--picture-card {\n    display: none;\n}\n// 去掉动画效果\n::v-deep .el-list-enter-active,\n::v-deep .el-list-leave-active {\n    transition: all 0s;\n}\n\n::v-deep .el-list-enter, .el-list-leave-active {\n  opacity: 0;\n  transform: translateY(0);\n}\n</style>\n\n"]}]}