{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\text.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\text.js", "mtime": 1749172159477}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKZXhwb3J0cy5lc2NhcGVUZXh0ID0gZXNjYXBlVGV4dDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyIpOwp2YXIgX2NyZWF0ZUNsYXNzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jbGFzc0NhbGxDaGVjay5qcyIpKTsKdmFyIF9jYWxsU3VwZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jYWxsU3VwZXIuanMiKSk7CnZhciBfaW5oZXJpdHMyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9wYXJjaG1lbnQgPSByZXF1aXJlKCJwYXJjaG1lbnQiKTsKdmFyIFRleHQgPSBleHBvcnRzLmRlZmF1bHQgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKF9UZXh0QmxvdCkgewogIGZ1bmN0aW9uIFRleHQoKSB7CiAgICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBUZXh0KTsKICAgIHJldHVybiAoMCwgX2NhbGxTdXBlcjIuZGVmYXVsdCkodGhpcywgVGV4dCwgYXJndW1lbnRzKTsKICB9CiAgKDAsIF9pbmhlcml0czIuZGVmYXVsdCkoVGV4dCwgX1RleHRCbG90KTsKICByZXR1cm4gKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoVGV4dCk7Cn0oX3BhcmNobWVudC5UZXh0QmxvdCk7CmZ1bmN0aW9uIGVzY2FwZVRleHQodGV4dCkgewogIHJldHVybiB0ZXh0LnJlcGxhY2UoL1smPD4iJ10vZywgZnVuY3Rpb24gKHMpIHsKICAgIC8vIGh0dHBzOi8vbG9kYXNoLmNvbS9kb2NzI2VzY2FwZQogICAgdmFyIGVudGl0eU1hcCA9IHsKICAgICAgJyYnOiAnJmFtcDsnLAogICAgICAnPCc6ICcmbHQ7JywKICAgICAgJz4nOiAnJmd0OycsCiAgICAgICciJzogJyZxdW90OycsCiAgICAgICInIjogJyYjMzk7JwogICAgfTsKICAgIHJldHVybiBlbnRpdHlNYXBbc107CiAgfSk7Cn0="}, {"version": 3, "names": ["_parchment", "require", "Text", "exports", "default", "_TextBlot", "_classCallCheck2", "_callSuper2", "arguments", "_inherits2", "_createClass2", "TextBlot", "escapeText", "text", "replace", "s", "entityMap"], "sources": ["../../src/blots/text.ts"], "sourcesContent": ["import { TextBlot } from 'parchment';\n\nclass Text extends TextBlot {}\n\nfunction escapeText(text: string) {\n  return text.replace(/[&<>\"']/g, (s) => {\n    // https://lodash.com/docs#escape\n    const entityMap: Record<string, string> = {\n      '&': '&amp;',\n      '<': '&lt;',\n      '>': '&gt;',\n      '\"': '&quot;',\n      \"'\": '&#39;',\n    };\n    return entityMap[s];\n  });\n}\n\nexport { Text as default, escapeText };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAoC,IAE9BC,IAAI,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,SAAA;EAAA,SAAAH,KAAA;IAAA,IAAAI,gBAAA,CAAAF,OAAA,QAAAF,IAAA;IAAA,WAAAK,WAAA,CAAAH,OAAA,QAAAF,IAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAL,OAAA,EAAAF,IAAA,EAAAG,SAAA;EAAA,WAAAK,aAAA,CAAAN,OAAA,EAAAF,IAAA;AAAA,EAASS,mBAAQ;AAE3B,SAASC,UAAUA,CAACC,IAAY,EAAE;EAChC,OAAOA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAG,UAAAC,CAAC,EAAK;IACrC;IACA,IAAMC,SAAiC,GAAG;MACxC,GAAG,EAAE,OAAO;MACZ,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,QAAQ;MACb,GAAG,EAAE;IACP,CAAC;IACD,OAAOA,SAAS,CAACD,CAAC,CAAC;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}]}