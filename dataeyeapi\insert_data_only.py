#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def main():
    # 数据库连接配置
    config = {
        'host': 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com',
        'user': 'dataeye',
        'password': 'jg@050877',
        'database': 'jgdataeye',
        'charset': 'utf8mb4'
    }
    
    # 示例数据SQL语句
    insert_statements = [
        # 插入项目数据
        """INSERT IGNORE INTO `account_projects` (`id`, `name`, `desc`, `owner_id`, `locked`, `preset`, `adapter`, `need_sync_keyword`, `config_ready`, `ws_status`, `ws_etag_cut`, `ws_etag_fuzzy`, `can_access_data`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
        (1, '金刚数瞳分析项目', '基于AI的数据分析和洞察项目', 1, 0, 0, 1, 0, 1, 1, 'etag_cut_001', 'etag_fuzzy_001', 1, '2024-12-01 10:00:00', '2024-12-28 15:30:00', 'admin', 'admin'),
        (2, '市场趋势分析', '市场数据分析和趋势预测项目', 2, 0, 0, 2, 1, 1, 1, 'etag_cut_002', 'etag_fuzzy_002', 1, '2024-12-05 14:20:00', '2024-12-27 09:15:00', 'niangao', 'niangao'),
        (3, '用户行为分析', '用户行为数据挖掘和分析项目', 1, 0, 1, 1, 0, 1, 1, 'etag_cut_003', 'etag_fuzzy_003', 1, '2024-12-10 16:45:00', '2024-12-28 11:20:00', 'admin', 'admin')""",
        
        # 插入Pinboard数据
        """INSERT IGNORE INTO `pinboards` (`uid`, `project_id`, `name`, `description`, `owner_id`, `is_template`, `is_shared`, `share_type`, `is_deleted`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
        ('pb-001-uuid-001', 1, '我的问题测试1', '这是一个测试报告，用于验证数据分析功能', 1, 0, 0, 'none', 0, '2024-12-20 14:29:29', '2024-12-28 14:29:29', 'admin', 'admin'),
        ('pb-002-uuid-002', 1, '人工智能发展趋势分析报告', '基于最新数据的AI发展趋势深度分析', 1, 1, 1, 'all', 0, '2024-12-15 10:15:00', '2024-12-27 16:45:00', 'admin', 'admin'),
        ('pb-003-uuid-003', 2, '市场数据洞察报告', '市场趋势和用户行为分析报告', 2, 0, 1, 'user', 0, '2024-12-18 09:30:00', '2024-12-28 11:20:00', 'niangao', 'niangao'),
        ('pb-004-uuid-004', 3, '用户行为分析看板', '用户行为数据可视化分析看板', 1, 0, 0, 'none', 0, '2024-12-22 16:00:00', '2024-12-28 08:15:00', 'admin', 'admin'),
        ('pb-005-uuid-005', 1, '数据质量监控报告', '数据质量和完整性监控分析', 1, 0, 1, 'user', 0, '2024-12-25 13:45:00', '2024-12-28 12:30:00', 'admin', 'admin')""",
        
        # 插入标签数据
        """INSERT IGNORE INTO `pinboard_tags` (`id`, `project_id`, `name`, `color`, `created_at`, `updated_at`, `create_by`, `update_by`) VALUES
        (1, 1, 'AI分析', '#409EFF', '2024-12-01 10:00:00', '2024-12-01 10:00:00', 'admin', 'admin'),
        (2, 1, '数据洞察', '#67C23A', '2024-12-01 10:05:00', '2024-12-01 10:05:00', 'admin', 'admin'),
        (3, 1, '趋势分析', '#E6A23C', '2024-12-01 10:10:00', '2024-12-01 10:10:00', 'admin', 'admin'),
        (4, 2, '市场研究', '#F56C6C', '2024-12-05 14:20:00', '2024-12-05 14:20:00', 'niangao', 'niangao'),
        (5, 3, '用户行为', '#909399', '2024-12-10 16:45:00', '2024-12-10 16:45:00', 'admin', 'admin'),
        (6, 1, '质量监控', '#9C27B0', '2024-12-15 11:30:00', '2024-12-15 11:30:00', 'admin', 'admin')""",
        
        # 插入标签关联数据
        """INSERT IGNORE INTO `pinboard_tag_relations` (`pinboard_uid`, `tag_id`, `created_at`) VALUES
        ('pb-001-uuid-001', 2, '2024-12-20 14:30:00'),
        ('pb-002-uuid-002', 1, '2024-12-15 10:16:00'),
        ('pb-002-uuid-002', 3, '2024-12-15 10:16:00'),
        ('pb-003-uuid-003', 4, '2024-12-18 09:31:00'),
        ('pb-004-uuid-004', 5, '2024-12-22 16:01:00'),
        ('pb-005-uuid-005', 6, '2024-12-25 13:46:00'),
        ('pb-005-uuid-005', 2, '2024-12-25 13:46:00')""",
        
        # 插入分享数据
        """INSERT IGNORE INTO `pinboard_shares` (`pinboard_uid`, `user_id`, `created_at`, `create_by`) VALUES
        ('pb-002-uuid-002', NULL, '2024-12-15 10:20:00', 'admin'),
        ('pb-003-uuid-003', 1, '2024-12-18 09:35:00', 'niangao'),
        ('pb-005-uuid-005', 2, '2024-12-25 13:50:00', 'admin')""",
        
        # 插入收藏数据
        """INSERT IGNORE INTO `pinboard_stars` (`pinboard_uid`, `user_id`, `created_at`) VALUES
        ('pb-002-uuid-002', 1, '2024-12-15 10:25:00'),
        ('pb-002-uuid-002', 2, '2024-12-16 14:30:00'),
        ('pb-003-uuid-003', 1, '2024-12-18 15:20:00'),
        ('pb-004-uuid-004', 2, '2024-12-23 09:15:00')"""
    ]
    
    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("🔗 数据库连接成功")
        
        # 执行每个INSERT语句
        for i, statement in enumerate(insert_statements, 1):
            try:
                cursor.execute(statement)
                print(f"✅ 执行成功: 插入语句 {i}")
            except Exception as e:
                print(f"❌ 执行失败: 插入语句 {i}")
                print(f"   错误: {e}")
        
        # 提交事务
        connection.commit()
        print("\n✅ 所有数据插入完成")
        
        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM account_projects")
        project_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pinboards")
        pinboard_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pinboard_tags")
        tag_count = cursor.fetchone()[0]
        
        print(f"\n📊 数据统计:")
        print(f"   项目数量: {project_count}")
        print(f"   Pinboard数量: {pinboard_count}")
        print(f"   标签数量: {tag_count}")
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
