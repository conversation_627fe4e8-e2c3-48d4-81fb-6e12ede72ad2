{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\login.vue", "mtime": 1747038934000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmxvZ2luIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgaGVpZ2h0OiAxMDAlOwogIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vYXNzZXRzL2ltYWdlcy9sb2dpbi1iYWNrZ3JvdW5kLmpwZyIpOwogIGJhY2tncm91bmQtc2l6ZTogY292ZXI7Cn0KLnRpdGxlIHsKICBtYXJnaW46IDBweCBhdXRvIDMwcHggYXV0bzsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgY29sb3I6ICM3MDcwNzA7Cn0KCi5sb2dpbi1mb3JtIHsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICB3aWR0aDogNDAwcHg7CiAgcGFkZGluZzogMjVweCAyNXB4IDVweCAyNXB4OwogIC5lbC1pbnB1dCB7CiAgICBoZWlnaHQ6IDM4cHg7CiAgICBpbnB1dCB7CiAgICAgIGhlaWdodDogMzhweDsKICAgIH0KICB9CiAgLmlucHV0LWljb24gewogICAgaGVpZ2h0OiAzOXB4OwogICAgd2lkdGg6IDE0cHg7CiAgICBtYXJnaW4tbGVmdDogMnB4OwogIH0KfQoubG9naW4tdGlwIHsKICBmb250LXNpemU6IDEzcHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGNvbG9yOiAjYmZiZmJmOwp9Ci5sb2dpbi1jb2RlIHsKICB3aWR0aDogMzMlOwogIGhlaWdodDogMzhweDsKICBmbG9hdDogcmlnaHQ7CiAgaW1nIHsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgfQp9Ci5lbC1sb2dpbi1mb290ZXIgewogIGhlaWdodDogNDBweDsKICBsaW5lLWhlaWdodDogNDBweDsKICBwb3NpdGlvbjogZml4ZWQ7CiAgYm90dG9tOiAwOwogIHdpZHRoOiAxMDAlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogI2ZmZjsKICBmb250LWZhbWlseTogQXJpYWw7CiAgZm9udC1zaXplOiAxMnB4OwogIGxldHRlci1zcGFjaW5nOiAxcHg7Cn0KLmxvZ2luLWNvZGUtaW1nIHsKICBoZWlnaHQ6IDM4cHg7Cn0K"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"login\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\n      <h3 class=\"title\">金刚数瞳</h3>\n      <el-form-item prop=\"username\">\n        <el-input\n          v-model=\"loginForm.username\"\n          type=\"text\"\n          auto-complete=\"off\"\n          placeholder=\"账号\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"loginForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"loginForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"login-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleLogin\"\n        >\n          <span v-if=\"!loading\">登 录</span>\n          <span v-else>登 录 中...</span>\n        </el-button>\n        <div style=\"float: right;\" v-if=\"register\">\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-login-footer\">\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\";\nimport Cookies from \"js-cookie\";\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\n\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      codeUrl: \"\",\n      loginForm: {\n        username: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\"\n      },\n      loginRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      // 验证码开关\n      captchaEnabled: true,\n      // 注册开关\n      register: false,\n      redirect: undefined\n    };\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect;\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getCode();\n    this.getCookie();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\n        this.register = res.registerEnabled === undefined ? false : res.registerEnabled;\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\n          this.loginForm.uuid = res.uuid;\n        }\n      });\n    },\n    getCookie() {\n      const username = Cookies.get(\"username\");\n      const password = Cookies.get(\"password\");\n      const rememberMe = Cookies.get('rememberMe')\n      this.loginForm = {\n        username: username === undefined ? this.loginForm.username : username,\n        password: password === undefined ? this.loginForm.password : decrypt(password),\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\n      };\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          if (this.loginForm.rememberMe) {\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\n          } else {\n            Cookies.remove(\"username\");\n            Cookies.remove(\"password\");\n            Cookies.remove('rememberMe');\n          }\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\n            this.$router.push({ path: this.redirect || \"/\" }).catch(()=>{});\n          }).catch(() => {\n            this.loading = false;\n            if (this.captchaEnabled) {\n              this.getCode();\n            }\n          });\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.login {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.login-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.login-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.login-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-login-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.login-code-img {\n  height: 38px;\n}\n</style>\n"]}]}