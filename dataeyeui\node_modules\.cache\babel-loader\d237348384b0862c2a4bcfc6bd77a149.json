{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\store\\modules\\app.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\store\\modules\\app.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_js<PERSON><PERSON>ie", "_interopRequireDefault", "require", "state", "sidebar", "opened", "Cookies", "get", "withoutAnimation", "hide", "device", "size", "mutations", "TOGGLE_SIDEBAR", "set", "CLOSE_SIDEBAR", "TOGGLE_DEVICE", "SET_SIZE", "SET_SIDEBAR_HIDE", "status", "actions", "toggleSideBar", "_ref", "commit", "closeSideBar", "_ref2", "_ref3", "toggleDevice", "_ref4", "setSize", "_ref5", "toggleSideBarHide", "_ref6", "_default", "exports", "default", "namespaced"], "sources": ["D:/jgst/dataeyeui/src/store/modules/app.js"], "sourcesContent": ["import Cookies from 'js-cookie'\n\nconst state = {\n  sidebar: {\n    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,\n    withoutAnimation: false,\n    hide: false\n  },\n  device: 'desktop',\n  size: Cookies.get('size') || 'medium'\n}\n\nconst mutations = {\n  TOGGLE_SIDEBAR: state => {\n    if (state.sidebar.hide) {\n      return false;\n    }\n    state.sidebar.opened = !state.sidebar.opened\n    state.sidebar.withoutAnimation = false\n    if (state.sidebar.opened) {\n      Cookies.set('sidebarStatus', 1)\n    } else {\n      Cookies.set('sidebarStatus', 0)\n    }\n  },\n  CLOSE_SIDEBAR: (state, withoutAnimation) => {\n    Cookies.set('sidebarStatus', 0)\n    state.sidebar.opened = false\n    state.sidebar.withoutAnimation = withoutAnimation\n  },\n  TOGGLE_DEVICE: (state, device) => {\n    state.device = device\n  },\n  SET_SIZE: (state, size) => {\n    state.size = size\n    Cookies.set('size', size)\n  },\n  SET_SIDEBAR_HIDE: (state, status) => {\n    state.sidebar.hide = status\n  }\n}\n\nconst actions = {\n  toggleSideBar({ commit }) {\n    commit('TOGGLE_SIDEBAR')\n  },\n  closeSideBar({ commit }, { withoutAnimation }) {\n    commit('CLOSE_SIDEBAR', withoutAnimation)\n  },\n  toggleDevice({ commit }, device) {\n    commit('TOGGLE_DEVICE', device)\n  },\n  setSize({ commit }, size) {\n    commit('SET_SIZE', size)\n  },\n  toggleSideBarHide({ commit }, status) {\n    commit('SET_SIDEBAR_HIDE', status)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,KAAK,GAAG;EACZC,OAAO,EAAE;IACPC,MAAM,EAAEC,iBAAO,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAACD,iBAAO,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI;IAC7EC,gBAAgB,EAAE,KAAK;IACvBC,IAAI,EAAE;EACR,CAAC;EACDC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAEL,iBAAO,CAACC,GAAG,CAAC,MAAM,CAAC,IAAI;AAC/B,CAAC;AAED,IAAMK,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAEV,KAAK,EAAI;IACvB,IAAIA,KAAK,CAACC,OAAO,CAACK,IAAI,EAAE;MACtB,OAAO,KAAK;IACd;IACAN,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,CAACF,KAAK,CAACC,OAAO,CAACC,MAAM;IAC5CF,KAAK,CAACC,OAAO,CAACI,gBAAgB,GAAG,KAAK;IACtC,IAAIL,KAAK,CAACC,OAAO,CAACC,MAAM,EAAE;MACxBC,iBAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC,CAAC,MAAM;MACLR,iBAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC;EACF,CAAC;EACDC,aAAa,EAAE,SAAfA,aAAaA,CAAGZ,KAAK,EAAEK,gBAAgB,EAAK;IAC1CF,iBAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IAC/BX,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,KAAK;IAC5BF,KAAK,CAACC,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB;EACnD,CAAC;EACDQ,aAAa,EAAE,SAAfA,aAAaA,CAAGb,KAAK,EAAEO,MAAM,EAAK;IAChCP,KAAK,CAACO,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDO,QAAQ,EAAE,SAAVA,QAAQA,CAAGd,KAAK,EAAEQ,IAAI,EAAK;IACzBR,KAAK,CAACQ,IAAI,GAAGA,IAAI;IACjBL,iBAAO,CAACQ,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC3B,CAAC;EACDO,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGf,KAAK,EAAEgB,MAAM,EAAK;IACnChB,KAAK,CAACC,OAAO,CAACK,IAAI,GAAGU,MAAM;EAC7B;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,IAAA,EAAa;IAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACpBA,MAAM,CAAC,gBAAgB,CAAC;EAC1B,CAAC;EACDC,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAAC,KAAA,EAAmC;IAAA,IAAhCH,MAAM,GAAAE,KAAA,CAANF,MAAM;IAAA,IAAMf,gBAAgB,GAAAkB,KAAA,CAAhBlB,gBAAgB;IACzCe,MAAM,CAAC,eAAe,EAAEf,gBAAgB,CAAC;EAC3C,CAAC;EACDmB,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAalB,MAAM,EAAE;IAAA,IAAlBa,MAAM,GAAAK,KAAA,CAANL,MAAM;IACnBA,MAAM,CAAC,eAAe,EAAEb,MAAM,CAAC;EACjC,CAAC;EACDmB,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAanB,IAAI,EAAE;IAAA,IAAhBY,MAAM,GAAAO,KAAA,CAANP,MAAM;IACdA,MAAM,CAAC,UAAU,EAAEZ,IAAI,CAAC;EAC1B,CAAC;EACDoB,iBAAiB,WAAjBA,iBAAiBA,CAAAC,KAAA,EAAab,MAAM,EAAE;IAAA,IAAlBI,MAAM,GAAAS,KAAA,CAANT,MAAM;IACxBA,MAAM,CAAC,kBAAkB,EAAEJ,MAAM,CAAC;EACpC;AACF,CAAC;AAAA,IAAAc,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACbC,UAAU,EAAE,IAAI;EAChBjC,KAAK,EAALA,KAAK;EACLS,SAAS,EAATA,SAAS;EACTQ,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}