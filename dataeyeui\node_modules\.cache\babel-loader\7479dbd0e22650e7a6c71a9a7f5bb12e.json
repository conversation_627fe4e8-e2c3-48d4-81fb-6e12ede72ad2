{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\layout\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_RightPanel", "_interopRequireDefault", "require", "_components", "_ResizeHandler", "_vuex", "_variables2", "name", "components", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "RightPanel", "Settings", "Sidebar", "TagsView", "mixins", "ResizeMixin", "computed", "_objectSpread2", "default", "mapState", "theme", "state", "settings", "sideTheme", "sidebar", "app", "device", "needTagsView", "tagsView", "fixedHeader", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "variables", "methods", "handleClickOutside", "$store", "dispatch"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar/>\n        <tags-view v-if=\"needTagsView\"/>\n      </div>\n      <app-main/>\n      <right-panel>\n        <settings/>\n      </right-panel>\n    </div>\n  </div>\n</template>\n\n<script>\nimport RightPanel from '@/components/RightPanel'\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    RightPanel,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      theme: state => state.settings.theme,\n      sideTheme: state => state.settings.sideTheme,\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    },\n    variables() {\n      return variables;\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/assets/styles/mixin.scss\";\n  @import \"~@/assets/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$base-sidebar-width});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px);\n  }\n\n  .sidebarHide .fixed-header {\n    width: 100%;\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"], "mappings": ";;;;;;;;AAkBA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA,oBAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA;EACA;EACAC,MAAA,GAAAC,sBAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA;IACAC,KAAA,WAAAA,MAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAF,KAAA;IAAA;IACAG,SAAA,WAAAA,UAAAF,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAC,SAAA;IAAA;IACAC,OAAA,WAAAA,QAAAH,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAD,OAAA;IAAA;IACAE,MAAA,WAAAA,OAAAL,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAC,MAAA;IAAA;IACAC,YAAA,WAAAA,aAAAN,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAM,QAAA;IAAA;IACAC,WAAA,WAAAA,YAAAR,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAO,WAAA;IAAA;EACA;IACAC,QAAA,WAAAA,SAAA;MACA;QACAC,WAAA,QAAAP,OAAA,CAAAQ,MAAA;QACAC,WAAA,OAAAT,OAAA,CAAAQ,MAAA;QACAE,gBAAA,OAAAV,OAAA,CAAAU,gBAAA;QACAC,MAAA,OAAAT,MAAA;MACA;IACA;IACAU,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;QAAAN,gBAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}