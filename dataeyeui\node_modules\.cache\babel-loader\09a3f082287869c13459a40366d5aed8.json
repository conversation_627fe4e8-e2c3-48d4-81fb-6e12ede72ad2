{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\color.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\color.js", "mtime": 1749172157719}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "ColorAttributor", "exports", "_StyleAttributor", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "domNode", "_superPropGet2", "startsWith", "replace", "hex", "split", "map", "component", "concat", "parseInt", "toString", "slice", "join", "StyleAttributor", "ColorClass", "ClassAttributor", "scope", "<PERSON><PERSON>", "INLINE", "ColorStyle"], "sources": ["../../src/formats/color.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nclass ColorAttributor extends StyleAttributor {\n  value(domNode: HTMLElement) {\n    let value = super.value(domNode) as string;\n    if (!value.startsWith('rgb(')) return value;\n    value = value.replace(/^[^\\d]+/, '').replace(/[^\\d]+$/, '');\n    const hex = value\n      .split(',')\n      .map((component) => `00${parseInt(component, 10).toString(16)}`.slice(-2))\n      .join('');\n    return `#${hex}`;\n  }\n}\n\nconst ColorClass = new ClassAttributor('color', 'ql-color', {\n  scope: Scope.INLINE,\n});\nconst ColorStyle = new ColorAttributor('color', 'color', {\n  scope: Scope.INLINE,\n});\n\nexport { ColorAttributor, ColorClass, ColorStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAmE,IAE7DC,eAAe,GAAAC,OAAA,CAAAD,eAAA,0BAAAE,gBAAA;EAAA,SAAAF,gBAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,eAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,eAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,eAAA,EAAAE,gBAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,eAAA;IAAAS,GAAA;IAAAC,KAAA,EACnB,SAAAA,KAAKA,CAACC,OAAoB,EAAE;MAC1B,IAAID,KAAK,OAAAE,cAAA,CAAAR,OAAA,EAAAJ,eAAA,qBAAeW,OAAO,EAAW;MAC1C,IAAI,CAACD,KAAK,CAACG,UAAU,CAAC,MAAM,CAAC,EAAE,OAAOH,KAAK;MAC3CA,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAC3D,IAAMC,GAAG,GAAGL,KAAK,CACdM,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAE,UAAAC,SAAS;QAAA,OAAM,KAAAC,MAAA,CAAIC,QAAQ,CAACF,SAAS,EAAE,EAAE,CAAC,CAACG,QAAQ,CAAC,EAAE,CAAE,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;MAAA,EAAC,CACzEC,IAAI,CAAC,EAAE,CAAC;MACX,WAAAJ,MAAA,CAAWJ,GAAI;IACjB;EAAA;AAAA,EAV4BS,0BAAe;AAa7C,IAAMC,UAAU,GAAAxB,OAAA,CAAAwB,UAAA,GAAG,IAAIC,0BAAe,CAAC,OAAO,EAAE,UAAU,EAAE;EAC1DC,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC;AACF,IAAMC,UAAU,GAAA7B,OAAA,CAAA6B,UAAA,GAAG,IAAI9B,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE;EACvD2B,KAAK,EAAEC,gBAAK,CAACC;AACf,CAAC,CAAC", "ignoreList": []}]}