import asyncio
import logging
from sqlalchemy import text
from config.database import async_engine

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseTableCreator:
    def __init__(self):
        self.engine = async_engine

    async def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute(text(
                    "SELECT COUNT(*) FROM information_schema.tables "
                    "WHERE table_schema = DATABASE() AND table_name = :table_name"
                ), {"table_name": table_name})
                count = result.scalar()
                return count > 0
        except Exception as e:
            logger.error(f"检查表 {table_name} 是否存在时出错: {e}")
            return False

    async def execute_sql(self, sql: str, description: str = "") -> bool:
        """执行SQL语句"""
        try:
            async with self.engine.begin() as conn:
                await conn.execute(text(sql))
                logger.info(f"✓ {description}")
                return True
        except Exception as e:
            logger.error(f"✗ {description} - 错误: {e}")
            return False

    async def create_tables(self):
        """创建所有表"""
        logger.info("开始创建金刚数瞳系统数据库表...")

        # 定义表创建SQL语句（按依赖顺序）
        table_sqls = [
            # 1. 项目管理模块
            {
                "name": "account_projects",
                "description": "创建项目表",
                "sql": """
                CREATE TABLE account_projects (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
                    name VARCHAR(100) NOT NULL COMMENT '项目名称',
                    `desc` TEXT COMMENT '项目详细描述',
                    owner_id BIGINT NOT NULL COMMENT '项目所有者ID',
                    locked TINYINT(1) DEFAULT 0 COMMENT '项目锁定状态',
                    preset TINYINT(1) DEFAULT 0 COMMENT '预置项目标识',
                    adapter INT COMMENT '数据适配器类型编号',
                    need_sync_keyword TINYINT(1) DEFAULT 0 COMMENT '关键词同步需求',
                    config_ready TINYINT(1) DEFAULT 0 COMMENT '配置就绪状态',
                    ws_status INT COMMENT '数据工作空间状态码',
                    ws_etag_cut VARCHAR(32) COMMENT '精确匹配模式数据版本标识',
                    ws_etag_fuzzy VARCHAR(32) COMMENT '模糊匹配模式数据版本标识',
                    can_access_data TINYINT(1) DEFAULT 1 COMMENT '数据访问权限标识',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (owner_id) REFERENCES sys_user(user_id)
                )
                """
            },
            {
                "name": "account_project_members",
                "description": "创建项目成员表",
                "sql": """
                CREATE TABLE account_project_members (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    user_id BIGINT NOT NULL COMMENT '用户ID',
                    role VARCHAR(20) NOT NULL COMMENT '角色(owner, admin, member, viewer)',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id),
                    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
                    UNIQUE KEY (project_id, user_id)
                )
                """
            },
            {
                "name": "member_insights",
                "description": "创建成员洞察配置表",
                "sql": """
                CREATE TABLE member_insights (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    user_id BIGINT NOT NULL COMMENT '用户ID',
                    insight_type VARCHAR(50) NOT NULL COMMENT '分析类型标识',
                    insight_text VARCHAR(100) NOT NULL COMMENT '分析类型说明',
                    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id),
                    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
                    UNIQUE KEY (project_id, user_id, insight_type)
                )
                """
            },

            # 2. 会话和消息管理模块
            {
                "name": "threads",
                "description": "创建线程表",
                "sql": """
                CREATE TABLE threads (
                    uid VARCHAR(36) PRIMARY KEY COMMENT '线程唯一标识符(UUIDv4格式)',
                    project_id BIGINT NOT NULL COMMENT '所属项目ID',
                    name VARCHAR(100) NOT NULL COMMENT '线程显示名称',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id)
                )
                """
            },
            {
                "name": "messages",
                "description": "创建消息表",
                "sql": """
                CREATE TABLE messages (
                    id VARCHAR(36) PRIMARY KEY COMMENT '消息唯一标识符(UUIDv4格式)',
                    thread_uid VARCHAR(36) COMMENT '所属线程ID',
                    title VARCHAR(50) NOT NULL COMMENT '消息标题',
                    content TEXT COMMENT '消息内容(支持富文本)',
                    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    FOREIGN KEY (thread_uid) REFERENCES threads(uid)
                )
                """
            },

            # 3. 看板和卡片管理模块
            {
                "name": "pinboards",
                "description": "创建Pinboard表",
                "sql": """
                CREATE TABLE pinboards (
                    uid VARCHAR(36) PRIMARY KEY COMMENT 'Pinboard唯一标识符',
                    project_id BIGINT NOT NULL COMMENT '所属项目ID',
                    name VARCHAR(100) NOT NULL COMMENT 'Pinboard名称',
                    description TEXT COMMENT 'Pinboard描述',
                    owner_id BIGINT NOT NULL COMMENT '创建者ID',
                    is_template TINYINT(1) DEFAULT 0 COMMENT '是否为模板',
                    is_shared TINYINT(1) DEFAULT 0 COMMENT '是否共享',
                    share_type ENUM('none', 'user', 'all') DEFAULT 'none' COMMENT '共享类型',
                    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否在回收站',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id),
                    FOREIGN KEY (owner_id) REFERENCES sys_user(user_id)
                )
                """
            },
            {
                "name": "pinboard_tags",
                "description": "创建Pinboard标签表",
                "sql": """
                CREATE TABLE pinboard_tags (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
                    project_id BIGINT NOT NULL COMMENT '所属项目ID',
                    name VARCHAR(50) NOT NULL COMMENT '标签名称',
                    color VARCHAR(20) COMMENT '标签颜色',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id),
                    UNIQUE KEY (project_id, name)
                )
                """
            },
            {
                "name": "pinboard_tag_relations",
                "description": "创建Pinboard标签关联表",
                "sql": """
                CREATE TABLE pinboard_tag_relations (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
                    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
                    tag_id BIGINT NOT NULL COMMENT '标签ID',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
                    FOREIGN KEY (tag_id) REFERENCES pinboard_tags(id),
                    UNIQUE KEY (pinboard_uid, tag_id)
                )
                """
            },
            {
                "name": "cards",
                "description": "创建卡片表",
                "sql": """
                CREATE TABLE cards (
                    uid VARCHAR(36) PRIMARY KEY COMMENT '卡片唯一标识符',
                    pinboard_uid VARCHAR(36) NOT NULL COMMENT '所属Pinboard ID',
                    title VARCHAR(200) NOT NULL COMMENT '卡片标题',
                    card_type INT NOT NULL COMMENT '卡片类型(1-分析卡片 2-监控卡片 3-报表卡片 4-仪表盘卡片)',
                    sentence_type INT DEFAULT 0 COMMENT '语句类型',
                    result_type INT DEFAULT 0 COMMENT '结果类型',
                    y_axis_scale TINYINT(1) DEFAULT 1 COMMENT '纵轴缩放开关',
                    datetime_anchor VARCHAR(30) COMMENT '时间锚点',
                    position_x INT DEFAULT 0 COMMENT 'X坐标位置',
                    position_y INT DEFAULT 0 COMMENT 'Y坐标位置',
                    width INT DEFAULT 4 COMMENT '卡片宽度',
                    height INT DEFAULT 3 COMMENT '卡片高度',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid)
                )
                """
            },
            {
                "name": "card_configs",
                "description": "创建卡片配置表",
                "sql": """
                CREATE TABLE card_configs (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
                    card_uid VARCHAR(36) NOT NULL COMMENT '卡片ID',
                    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
                    config_value TEXT COMMENT '配置值(JSON格式)',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (card_uid) REFERENCES cards(uid),
                    UNIQUE KEY (card_uid, config_key)
                )
                """
            },
            {
                "name": "card_units",
                "description": "创建卡片数据单元表",
                "sql": """
                CREATE TABLE card_units (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '单元ID',
                    card_uid VARCHAR(36) NOT NULL COMMENT '卡片ID',
                    identity VARCHAR(50) NOT NULL COMMENT '单元唯一标识',
                    unit_type INT NOT NULL COMMENT '单元类型标识',
                    unit_candidate_type INT NOT NULL COMMENT '单元候选类型标识',
                    start_time VARCHAR(30) COMMENT '单元起始时间',
                    end_time VARCHAR(30) COMMENT '单元结束时间',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (card_uid) REFERENCES cards(uid)
                )
                """
            },
            {
                "name": "card_unit_tokens",
                "description": "创建卡片单元分词表",
                "sql": """
                CREATE TABLE card_unit_tokens (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分词ID',
                    unit_id BIGINT NOT NULL COMMENT '单元ID',
                    word VARCHAR(100) NOT NULL COMMENT '分词词语内容',
                    prop VARCHAR(50) NOT NULL COMMENT '分词属性标识符',
                    start_time VARCHAR(30) COMMENT '分词起始位置',
                    end_time VARCHAR(30) COMMENT '分词结束位置',
                    is_locked TINYINT(1) DEFAULT 0 COMMENT '分词是否被锁定',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (unit_id) REFERENCES card_units(id)
                )
                """
            },
            {
                "name": "card_filters",
                "description": "创建卡片过滤器表",
                "sql": """
                CREATE TABLE card_filters (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '过滤器ID',
                    card_uid VARCHAR(36) NOT NULL COMMENT '卡片ID',
                    text VARCHAR(200) NOT NULL COMMENT '过滤器文本',
                    filter_type VARCHAR(50) NOT NULL COMMENT '过滤器类型',
                    can_delete TINYINT(1) DEFAULT 1 COMMENT '是否可删除',
                    time_type VARCHAR(50) COMMENT '时间类型',
                    word VARCHAR(100) COMMENT '词语',
                    prop VARCHAR(50) COMMENT '属性',
                    time_unit INT COMMENT '时间单位',
                    start_time VARCHAR(30) COMMENT '开始时间',
                    end_time VARCHAR(30) COMMENT '结束时间',
                    value INT COMMENT '值',
                    time_regex_granularity VARCHAR(50) COMMENT '时间正则粒度',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (card_uid) REFERENCES cards(uid)
                )
                """
            },



            # 4. 任务和导出管理模块
            {
                "name": "async_tasks",
                "description": "创建异步任务表",
                "sql": """
                CREATE TABLE async_tasks (
                    uid VARCHAR(36) PRIMARY KEY COMMENT '任务唯一标识符',
                    project_id BIGINT NOT NULL COMMENT '所属项目ID',
                    task_type INT NOT NULL COMMENT '任务类型(1-数据导入，2-模型训练，3-数据预处理，4-数据清洗，5-数据标注，6-数据验证，7-数据转换，8-数据导出)',
                    status INT NOT NULL DEFAULT 0 COMMENT '任务状态(0-进行中，1-成功，2-失败，3-已取消，4-已暂停)',
                    progress_percentage INT DEFAULT 0 COMMENT '进度百分比',
                    estimated_time_remaining VARCHAR(50) COMMENT '预计剩余时间',
                    download_url VARCHAR(255) COMMENT '下载URL',
                    params TEXT COMMENT '任务参数(JSON格式)',
                    result TEXT COMMENT '任务结果(JSON格式)',
                    error_message TEXT COMMENT '错误信息',
                    creator BIGINT COMMENT '创建者ID',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id),
                    FOREIGN KEY (creator) REFERENCES sys_user(user_id)
                )
                """
            },



            # 6. 监控和预警模块
            {
                "name": "alerts",
                "description": "创建预警表",
                "sql": """
                CREATE TABLE alerts (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预警ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    card_uid VARCHAR(36) COMMENT '关联卡片ID',
                    alert_type VARCHAR(50) NOT NULL COMMENT '预警类型(DEVICE_OFFLINE, DATA_ANOMALY等)',
                    alert_name VARCHAR(100) NOT NULL COMMENT '预警名称',
                    alert_condition TEXT NOT NULL COMMENT '预警条件(JSON格式)',
                    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
                    last_triggered_at DATETIME COMMENT '最后触发时间',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id),
                    FOREIGN KEY (card_uid) REFERENCES cards(uid)
                )
                """
            },
            {
                "name": "alert_logs",
                "description": "创建预警记录表",
                "sql": """
                CREATE TABLE alert_logs (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
                    alert_id BIGINT NOT NULL COMMENT '预警ID',
                    alert_type VARCHAR(50) NOT NULL COMMENT '预警类型',
                    triggered_at DATETIME NOT NULL COMMENT '触发时间',
                    alert_data TEXT COMMENT '触发数据(JSON格式)',
                    FOREIGN KEY (alert_id) REFERENCES alerts(id)
                )
                """
            },

            # 7. 数据分析和模型模块
            {
                "name": "project_models",
                "description": "创建项目模型表",
                "sql": """
                CREATE TABLE project_models (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模型ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
                    model_type VARCHAR(50) NOT NULL COMMENT '模型类型',
                    model_config TEXT COMMENT '模型配置(JSON格式)',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id)
                )
                """
            },
            {
                "name": "dimension_tokens",
                "description": "创建维度令牌表",
                "sql": """
                CREATE TABLE dimension_tokens (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '令牌ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    token_name VARCHAR(100) NOT NULL COMMENT '令牌名称',
                    token_key VARCHAR(50) NOT NULL COMMENT '令牌键',
                    token_value VARCHAR(255) NOT NULL COMMENT '令牌值',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id)
                )
                """
            },
            {
                "name": "project_insights",
                "description": "创建项目洞察表",
                "sql": """
                CREATE TABLE project_insights (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '洞察ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    insight_type VARCHAR(50) NOT NULL COMMENT '洞察类型',
                    metrics TEXT COMMENT '核心指标数值(JSON格式)',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id)
                )
                """
            },
            {
                "name": "insight_timelines",
                "description": "创建洞察时间线表",
                "sql": """
                CREATE TABLE insight_timelines (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '时间线ID',
                    insight_id BIGINT NOT NULL COMMENT '洞察ID',
                    timestamp DATETIME NOT NULL COMMENT '时间点',
                    value DECIMAL(20,4) NOT NULL COMMENT '数值',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    FOREIGN KEY (insight_id) REFERENCES project_insights(id)
                )
                """
            },
            {
                "name": "question_guides",
                "description": "创建问题引导表",
                "sql": """
                CREATE TABLE question_guides (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '引导ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    guide_text VARCHAR(200) NOT NULL COMMENT '引导文本',
                    guide_tokens TEXT COMMENT '引导标记(JSON格式)',
                    display_order INT DEFAULT 0 COMMENT '显示顺序',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id)
                )
                """
            },
            {
                "name": "auto_complete_suggestions",
                "description": "创建自动补全建议表",
                "sql": """
                CREATE TABLE auto_complete_suggestions (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '建议ID',
                    project_id BIGINT NOT NULL COMMENT '项目ID',
                    input_prefix VARCHAR(100) NOT NULL COMMENT '输入前缀',
                    suggestion VARCHAR(200) NOT NULL COMMENT '补全建议',
                    weight INT DEFAULT 0 COMMENT '权重',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (project_id) REFERENCES account_projects(id)
                )
                """
            },

            # 8. 系统配置模块
            {
                "name": "api_docs_config",
                "description": "创建API文档配置表",
                "sql": """
                CREATE TABLE api_docs_config (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
                    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
                    config_value TEXT COMMENT '配置值',
                    description VARCHAR(200) COMMENT '描述',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    UNIQUE KEY (config_key)
                )
                """
            },
            {
                "name": "react_init_config",
                "description": "创建前端初始化配置表",
                "sql": """
                CREATE TABLE react_init_config (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
                    config_key VARCHAR(50) NOT NULL COMMENT '配置键',
                    config_value TEXT COMMENT '配置值',
                    description VARCHAR(200) COMMENT '描述',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    UNIQUE KEY (config_key)
                )
                """
            },

            # 9. Pinboard扩展功能模块
            {
                "name": "pinboard_shares",
                "description": "创建Pinboard共享表(扩展版)",
                "sql": """
                CREATE TABLE pinboard_shares (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '共享ID',
                    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
                    user_id BIGINT COMMENT '共享目标用户ID(为空表示共享给所有人)',
                    share_url VARCHAR(255) COMMENT '分享链接',
                    access_password VARCHAR(100) COMMENT '访问密码',
                    expire_time DATETIME COMMENT '过期时间',
                    view_count INT DEFAULT 0 COMMENT '访问次数',
                    permissions JSON COMMENT '访问权限设置',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
                    FOREIGN KEY (user_id) REFERENCES sys_user(user_id)
                )
                """
            },
            {
                "name": "user_notification_settings",
                "description": "创建用户通知设置表",
                "sql": """
                CREATE TABLE user_notification_settings (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
                    user_id BIGINT NOT NULL COMMENT '用户ID',
                    email_enabled TINYINT(1) DEFAULT 1 COMMENT '邮件通知开关',
                    in_app_enabled TINYINT(1) DEFAULT 1 COMMENT '站内通知开关',
                    frequency ENUM('immediate', 'daily', 'weekly') DEFAULT 'immediate' COMMENT '通知频率',
                    notification_types JSON COMMENT '通知类型设置',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
                    UNIQUE KEY (user_id)
                )
                """
            },
            {
                "name": "pinboard_updates",
                "description": "创建报告更新记录表",
                "sql": """
                CREATE TABLE pinboard_updates (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '更新记录ID',
                    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
                    update_type ENUM('content_update', 'new_comment', 'author_post') COMMENT '更新类型',
                    update_content TEXT COMMENT '更新内容',
                    update_by BIGINT COMMENT '更新者ID',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
                    FOREIGN KEY (update_by) REFERENCES sys_user(user_id)
                )
                """
            },
            {
                "name": "pinboard_reminders",
                "description": "创建报告提醒表",
                "sql": """
                CREATE TABLE pinboard_reminders (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '提醒ID',
                    name VARCHAR(100) NOT NULL COMMENT '提醒名称',
                    description TEXT COMMENT '提醒描述',
                    pinboard_uid VARCHAR(36) NOT NULL COMMENT '关联报告ID',
                    frequency ENUM('daily', 'weekly', 'monthly', 'custom') COMMENT '提醒频率',
                    cron_expression VARCHAR(255) COMMENT 'Cron表达式',
                    execute_time TIME COMMENT '执行时间',
                    recipients JSON COMMENT '接收人列表',
                    push_methods JSON COMMENT '推送方式',
                    expire_date DATE COMMENT '过期日期',
                    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
                    next_run_time DATETIME COMMENT '下次执行时间',
                    job_id BIGINT COMMENT '关联的系统任务ID',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    create_by VARCHAR(64) COMMENT '创建者',
                    update_by VARCHAR(64) COMMENT '更新者',
                    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
                    FOREIGN KEY (job_id) REFERENCES sys_job(job_id)
                )
                """
            },
            {
                "name": "pinboard_reminder_logs",
                "description": "创建提醒执行日志表",
                "sql": """
                CREATE TABLE pinboard_reminder_logs (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
                    reminder_id BIGINT NOT NULL COMMENT '提醒ID',
                    execute_time DATETIME COMMENT '执行时间',
                    status ENUM('success', 'failed') COMMENT '执行状态',
                    recipients_count INT COMMENT '接收人数',
                    message TEXT COMMENT '执行消息',
                    duration VARCHAR(20) COMMENT '执行耗时',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    FOREIGN KEY (reminder_id) REFERENCES pinboard_reminders(id)
                )
                """
            },
            {
                "name": "pinboard_delete_records",
                "description": "创建删除记录表",
                "sql": """
                CREATE TABLE pinboard_delete_records (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '删除记录ID',
                    pinboard_uid VARCHAR(36) NOT NULL COMMENT 'Pinboard ID',
                    original_path VARCHAR(500) COMMENT '原路径',
                    delete_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
                    delete_by BIGINT COMMENT '删除者ID',
                    delete_reason TEXT COMMENT '删除原因',
                    file_size VARCHAR(20) COMMENT '文件大小',
                    expire_time DATETIME COMMENT '过期时间(30天后永久删除)',
                    restored TINYINT(1) DEFAULT 0 COMMENT '是否已恢复',
                    restore_time DATETIME COMMENT '恢复时间',
                    restore_by BIGINT COMMENT '恢复者ID',
                    restore_path VARCHAR(500) COMMENT '恢复路径',
                    FOREIGN KEY (pinboard_uid) REFERENCES pinboards(uid),
                    FOREIGN KEY (delete_by) REFERENCES sys_user(user_id),
                    FOREIGN KEY (restore_by) REFERENCES sys_user(user_id)
                )
                """
            },
            {
                "name": "user_settings",
                "description": "创建用户设置表",
                "sql": """
                CREATE TABLE user_settings (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
                    user_id BIGINT NOT NULL COMMENT '用户ID',
                    setting_type VARCHAR(50) NOT NULL COMMENT '设置类型(search_settings, analysis_settings)',
                    setting_key VARCHAR(100) NOT NULL COMMENT '设置键',
                    setting_value TEXT COMMENT '设置值(JSON格式)',
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    FOREIGN KEY (user_id) REFERENCES sys_user(user_id),
                    UNIQUE KEY unique_user_setting (user_id, setting_type, setting_key)
                ) COMMENT='用户个性化设置表'
                """
            }
        ]

        # 执行表创建
        success_count = 0
        total_count = len(table_sqls)

        for table_info in table_sqls:
            table_name = table_info["name"]
            description = table_info["description"]
            sql = table_info["sql"]

            # 检查表是否已存在
            if await self.check_table_exists(table_name):
                logger.info(f"⚠ 表 {table_name} 已存在，跳过创建")
                success_count += 1
                continue

            # 创建表
            if await self.execute_sql(sql, description):
                success_count += 1

        logger.info(f"表创建完成: {success_count}/{total_count}")
        return success_count == total_count

async def main():
    """主函数"""
    creator = DatabaseTableCreator()
    success = await creator.create_tables()

    if success:
        logger.info("🎉 所有表创建成功！")
    else:
        logger.error("❌ 部分表创建失败，请检查日志")

    # 关闭数据库连接
    await creator.engine.dispose()

if __name__ == "__main__":
    asyncio.run(main())