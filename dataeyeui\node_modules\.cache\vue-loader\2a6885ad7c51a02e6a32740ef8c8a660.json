{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=style&index=0&id=b84ac022&lang=scss&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1750056641055}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZGFzaGJvYXJkLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAyMHB4Owp9CgovLyDmkJzntKLmoI/moLflvI8KLnNlYXJjaC1jb250YWluZXIgewogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyOiAxcHggc29saWQgI2U4ZThlODsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgcGFkZGluZzogMTJweCAxNnB4OwogIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTsKCiAgLnNlYXJjaC1mb3JtIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiAxMnB4OwoKICAgIC5zZWFyY2gtaW5wdXQtd3JhcHBlciB7CiAgICAgIGZsZXg6IDE7CiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsgLy8g5re75Yqg55u45a+55a6a5L2N77yM5L2/5by556qX6IO95q2j56Gu5a6a5L2NCgogICAgICAuc2VhcmNoLWlucHV0IHsKICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICBoZWlnaHQ6IDMycHg7CiAgICAgICAgcGFkZGluZzogNHB4IDEycHg7CiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjMzMzOwogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgICB0cmFuc2l0aW9uOiBib3JkZXItY29sb3IgMC4zczsKCiAgICAgICAgJjpmb2N1cyB7CiAgICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgyNCwgMTQ0LCAyNTUsIDAuMik7CiAgICAgICAgfQoKICAgICAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgICAgICBjb2xvcjogIzk5OTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KCiAgICAuc2VhcmNoLWJ1dHRvbnMgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBnYXA6IDhweDsKCiAgICAgIC5idG4taWNvbiB7CiAgICAgICAgd2lkdGg6IDMycHg7CiAgICAgICAgaGVpZ2h0OiAzMnB4OwogICAgICAgIHBhZGRpbmc6IDZweDsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKICAgICAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDsKICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgICAgICAgICBib3JkZXItY29sb3I6ICM0MGE5ZmY7CiAgICAgICAgfQoKICAgICAgICAuY2xvc2UtaWNvbiB7CiAgICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICAgIGhlaWdodDogMTZweDsKICAgICAgICAgIGJhY2tncm91bmQ6IHVybCgnZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlCM2FXUjBhRDBpTVRZaUlHaGxhV2RvZEQwaU1UWWlJSFpwWlhkQ2IzZzlJakFnTUNBeE5pQXhOaUlnWm1sc2JEMGlibTl1WlNJZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWo0S1BIQmhkR2dnWkQwaVRURXlJRFJNTkNBMFREUWdNVEpNTVRJZ01USk1NVElnTkZvaUlITjBjbTlyWlQwaUl6WTJOaUlnYzNSeWIydGxMWGRwWkhSb1BTSXhMalVpSUhOMGNtOXJaUzFzYVc1bFkyRndQU0p5YjNWdVpDSWdjM1J5YjJ0bExXeHBibVZxYjJsdVBTSnliM1Z1WkNJdlBnbzhMM04yWno0SycpIG5vLXJlcGVhdCBjZW50ZXI7CiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IGNvbnRhaW47CiAgICAgICAgfQoKICAgICAgICAuc2VhcmNoLWljb24gewogICAgICAgICAgd2lkdGg6IDE2cHg7CiAgICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgICBiYWNrZ3JvdW5kOiB1cmwoJ2RhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QjNhV1IwYUQwaU1UWWlJR2hsYVdkb2REMGlNVFlpSUhacFpYZENiM2c5SWpBZ01DQXhOaUF4TmlJZ1ptbHNiRDBpYm05dVpTSWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklqNEtQSEJoZEdnZ1pEMGlUVGNnTVRKRE15NDJPRFl5T1NBeE1pQXhJRGt1TXpFek56RWdNU0EyUXpFZ01pNDJPRFl5T1NBekxqWTROakk1SURBZ055QXdRekV3TGpNeE16Y2dNQ0F4TXlBeUxqWTROakk1SURFeklEWkRNVE1nT1M0ek1UTTNNU0F4TUM0ek1UTTNJREV5SURjZ01USmFUVGNnTVRGRE9TNDNOakUwTWlBeE1TQXhNaUE0TGpjMk1UUXlJREV5SURaRE1USWdNeTR5TXpnMU9DQTVMamMyTVRReUlERWdOeUF4UXpRdU1qTTROVGdnTVNBeUlETXVNak00TlRnZ01pQTJReklnT0M0M05qRTBNaUEwTGpJek9EVTRJREV4SURjZ01URmFJaUJtYVd4c1BTSWpOalkySWk4K0NqeHdZWFJvSUdROUlrMHhNU0F4TVV3eE5TQXhOU0lnYzNSeWIydGxQU0lqTmpZMklpQnpkSEp2YTJVdGQybGtkR2c5SWpFdU5TSWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWk4K0Nqd3ZjM1puUGdvPScpIG5vLXJlcGVhdCBjZW50ZXI7CiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IGNvbnRhaW47CiAgICAgICAgfQoKICAgICAgICAubGVmdC1hcnJvdy1pY29uIHsKICAgICAgICAgIHdpZHRoOiAxNnB4OwogICAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNVFlpSUdobGFXZG9kRDBpTVRZaUlIWnBaWGRDYjNnOUlqQWdNQ0F4TmlBeE5pSWdabWxzYkQwaWJtOXVaU0lnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JajRLUEhCaGRHZ2daRDBpVFRFd0lERXlURFlnT0V3eE1DQTBJaUJ6ZEhKdmEyVTlJaU0yTmpZaUlITjBjbTlyWlMxM2FXUjBhRDBpTVM0MUlpQnpkSEp2YTJVdGJHbHVaV05oY0QwaWNtOTFibVFpSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlMejRLUEM5emRtYytDZz09Jykgbm8tcmVwZWF0IGNlbnRlcjsKICAgICAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjsKICAgICAgICB9CgogICAgICAgIC5yaWdodC1hcnJvdy1pY29uIHsKICAgICAgICAgIHdpZHRoOiAxNnB4OwogICAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNVFlpSUdobGFXZG9kRDBpTVRZaUlIWnBaWGRDYjNnOUlqQWdNQ0F4TmlBeE5pSWdabWxzYkQwaWJtOXVaU0lnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JajRLUEhCaGRHZ2daRDBpVFRZZ05Fd3hNQ0E0VERZZ01USWlJSE4wY205clpUMGlJelkyTmlJZ2MzUnliMnRsTFhkcFpIUm9QU0l4TGpVaUlITjBjbTlyWlMxc2FXNWxZMkZ3UFNKeWIzVnVaQ0lnYzNSeWIydGxMV3hwYm1WcWIybHVQU0p5YjNWdVpDSXZQZ284TDNOMlp6NEsnKSBuby1yZXBlYXQgY2VudGVyOwogICAgICAgICAgYmFja2dyb3VuZC1zaXplOiBjb250YWluOwogICAgICAgIH0KCiAgICAgICAgLmZpbHRlci1pY29uIHsKICAgICAgICAgIHdpZHRoOiAxNnB4OwogICAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNVFlpSUdobGFXZG9kRDBpTVRZaUlIWnBaWGRDYjNnOUlqQWdNQ0F4TmlBeE5pSWdabWxzYkQwaWJtOXVaU0lnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JajRLUEhCaGRHZ2daRDBpVFRJZ00wZ3hORXd4TUNBM1ZqRXpURFlnTVRGV04wd3lJRE5hSWlCemRISnZhMlU5SWlNMk5qWWlJSE4wY205clpTMTNhV1IwYUQwaU1TNDFJaUJ6ZEhKdmEyVXRiR2x1WldOaGNEMGljbTkxYm1RaUlITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpTHo0S1BDOXpkbWMrQ2c9PScpIG5vLXJlcGVhdCBjZW50ZXI7CiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IGNvbnRhaW47CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9CgovLyDnrZvpgInlvLnnqpfmoLflvI8KLmZpbHRlci1wb3B1cCB7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4OwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgd2lkdGg6IDI4MHB4OwogIG1heC1oZWlnaHQ6IDQ1MHB4OwogIG92ZXJmbG93OiBoaWRkZW47CgogIC5wb3B1cC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBwYWRkaW5nOiA4cHggMTJweDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogICAgYmFja2dyb3VuZDogI2ZhZmFmYTsKICAgIGJvcmRlci1yYWRpdXM6IDZweCA2cHggMCAwOwoKICAgIHNwYW4gewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgfQoKICAgIC5wb3B1cC1jbG9zZSB7CiAgICAgIGJhY2tncm91bmQ6IG5vbmU7CiAgICAgIGJvcmRlcjogbm9uZTsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBjb2xvcjogIzhjOGM4YzsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICBwYWRkaW5nOiAwOwogICAgICB3aWR0aDogMjBweDsKICAgICAgaGVpZ2h0OiAyMHB4OwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgYm9yZGVyLXJhZGl1czogMnB4OwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTsKICAgICAgICBjb2xvcjogIzU5NTk1OTsKICAgICAgfQogICAgfQogIH0KCiAgLnBvcHVwLXNlYXJjaCB7CiAgICBwYWRkaW5nOiAxMnB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CgogICAgLnNlYXJjaC1pbnB1dCB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgICBwYWRkaW5nOiA2cHggMTJweCA2cHggMzJweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBmb250LXNpemU6IDEzcHg7CiAgICAgIG91dGxpbmU6IG5vbmU7CiAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7CgogICAgICAmOmZvY3VzIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjEpOwogICAgICB9CgogICAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgICAgY29sb3I6ICNiZmJmYmY7CiAgICAgIH0KICAgIH0KCiAgICAuc2VhcmNoLWljb24gewogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIGxlZnQ6IDIwcHg7CiAgICAgIHRvcDogNTAlOwogICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7CiAgICAgIHdpZHRoOiAxNHB4OwogICAgICBoZWlnaHQ6IDE0cHg7CiAgICAgIGJhY2tncm91bmQ6IHVybCgnZGF0YTppbWFnZS9zdmcreG1sO3V0ZjgsPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIlMjM5OTkiIHN0cm9rZS13aWR0aD0iMiI+PGNpcmNsZSBjeD0iMTEiIGN5PSIxMSIgcj0iOCIvPjxwYXRoIGQ9Im0yMSAyMS00LjM1LTQuMzUiLz48L3N2Zz4nKSBuby1yZXBlYXQgY2VudGVyOwogICAgICBiYWNrZ3JvdW5kLXNpemU6IGNvbnRhaW47CiAgICB9CiAgfQoKICAucG9wdXAtdGFicyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwoKICAgIC50YWItaXRlbSB7CiAgICAgIGZsZXg6IDE7CiAgICAgIHBhZGRpbmc6IDhweCAxMnB4OwogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgY29sb3I6ICM1OTU5NTk7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CiAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCB0cmFuc3BhcmVudDsKCiAgICAgICY6aG92ZXIgewogICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgICAgIH0KCiAgICAgICYuYWN0aXZlIHsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogICAgICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICMxODkwZmY7CiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgfQogICAgfQogIH0KCiAgLnBvcHVwLWNvbnRlbnQgewogICAgcGFkZGluZzogMDsKICAgIG1heC1oZWlnaHQ6IDMwMHB4OwogICAgb3ZlcmZsb3cteTogYXV0bzsKCiAgICAudGFiLWNvbnRlbnQgewogICAgICAuZmlsdGVyLWl0ZW0gewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgcGFkZGluZzogMTBweCAxNnB4OwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CiAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmNWY1ZjU7CiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsKCiAgICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7CiAgICAgICAgfQoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNmMGY4ZmY7CiAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICB9CgogICAgICAgICY6YWN0aXZlIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNlNmY3ZmY7CiAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICB9CgogICAgICAgIC5hcnJvdy1pY29uIHsKICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgICAgICAgZm9udC1zdHlsZTogbm9ybWFsOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLnRpbWUtdW5pdHMtcm93IHsKICAgICAgICBwYWRkaW5nOiAxMHB4IDE2cHg7CiAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmNWY1ZjU7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBnYXA6IDE2cHg7CgogICAgICAgIC50aW1lLXVuaXQgewogICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgY29sb3I6ICMyNjI2MjY7CiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CgogICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmMGY4ZmY7CiAgICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLnRpbWUtaXRlbSB7CiAgICAgICAgcGFkZGluZzogMTBweCAxNnB4OwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CiAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmNWY1ZjU7CiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsKCiAgICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7CiAgICAgICAgfQoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNmMGY4ZmY7CiAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi5zZWFyY2gtZm9ybSB7CiAgLmZvcm0tcm93IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiAxNnB4OwogICAgZmxleC13cmFwOiB3cmFwOwogIH0KCiAgLmZvcm0tZ3JvdXAgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDZweDsKCiAgICAuZm9ybS1sYWJlbCB7CiAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgY29sb3I6ICM1OTU5NTk7CiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7CiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICAgIG1hcmdpbjogMDsKICAgIH0KCiAgICAuZm9ybS1pbnB1dCB7CiAgICAgIGhlaWdodDogMjhweDsKICAgICAgcGFkZGluZzogMCA4cHg7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuMnM7CgogICAgICAmOmZvY3VzIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjEpOwogICAgICB9CgogICAgICAmLmtleXdvcmQtaW5wdXQgewogICAgICAgIHdpZHRoOiAyMDBweDsKICAgICAgfQoKICAgICAgJi50aW1lLWlucHV0IHsKICAgICAgICB3aWR0aDogNjBweDsKICAgICAgfQoKICAgICAgJjo6cGxhY2Vob2xkZXIgewogICAgICAgIGNvbG9yOiAjYmZiZmJmOwogICAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgfQogICAgfQoKICAgIC5mb3JtLXNlbGVjdCB7CiAgICAgIGhlaWdodDogMjhweDsKICAgICAgcGFkZGluZzogMCAyMHB4IDAgOHB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgY29sb3I6ICMyNjI2MjY7CiAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmYgdXJsKCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nbm9uZScgdmlld0JveD0nMCAwIDIwIDIwJyUzZSUzY3BhdGggc3Ryb2tlPSclMjM2YjcyODAnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgc3Ryb2tlLXdpZHRoPScxLjUnIGQ9J202IDggNCA0IDQtNCcvJTNlJTNjL3N2ZyUzZSIpIG5vLXJlcGVhdCByaWdodCA2cHggY2VudGVyLzEycHggMTJweDsKICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgYXBwZWFyYW5jZTogbm9uZTsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICBtaW4td2lkdGg6IDgwcHg7CiAgICAgIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjJzOwoKICAgICAgJjpmb2N1cyB7CiAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4xKTsKICAgICAgfQogICAgfQogIH0KCiAgLmZvcm0tYnV0dG9ucyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogOHB4OwogICAgbWFyZ2luLWxlZnQ6IGF1dG87CgogICAgLmJ0biB7CiAgICAgIGhlaWdodDogMjhweDsKICAgICAgcGFkZGluZzogMCAxMnB4OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgZm9udC13ZWlnaHQ6IDQwMDsKICAgICAgYm9yZGVyOiAxcHggc29saWQ7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CgogICAgICAmLmJ0bi1wcmltYXJ5IHsKICAgICAgICBiYWNrZ3JvdW5kOiAjMTg5MGZmOwogICAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgICBjb2xvcjogI2ZmZmZmZjsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiAjNDBhOWZmOwogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDBhOWZmOwogICAgICAgIH0KICAgICAgfQoKICAgICAgJi5idG4tZGVmYXVsdCB7CiAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICAgICAgICBib3JkZXItY29sb3I6ICNkOWQ5ZDk7CiAgICAgICAgY29sb3I6ICM1OTU5NTk7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgfQogICAgICB9CgogICAgICAmLmJ0bi1zdWNjZXNzIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjNTJjNDFhOwogICAgICAgIGJvcmRlci1jb2xvcjogIzUyYzQxYTsKICAgICAgICBjb2xvcjogI2ZmZmZmZjsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiAjNzNkMTNkOwogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNzNkMTNkOwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQoKLy8g6aG26YOo5Yy65Z+f77ya6Zeo5bqX6JCl5Lia6aKd5YmN5Y2B55qEICsg5pm66IO95Yqp5omLCi50b3Atc2VjdGlvbiB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDIwcHg7CiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7Cn0KCi8vIOS4remXtOWMuuWfn++8muiQpeS4mumineWQjOavlCDljZXni6zkuIDooYwKLm1pZGRsZS1zZWN0aW9uIHsKICB3aWR0aDogMTAwJTsKfQoKLy8g5bqV6YOo5Yy65Z+f77ya5ZOB54mM6Zeo5bqX6JCl5Lia6aKd5YmN5Y2B55qEICsg5pm66IO95Yqp5omLCi5ib3R0b20tc2VjdGlvbiB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDIwcHg7CiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7Cn0KCi8vIOS4u+WbvuihqOWMuuWfnwoubWFpbi1jaGFydCB7CiAgZmxleDogMi41OyAvLyDlm77ooajljaDmja7mm7TlpJrnqbrpl7TvvIzmr5TkvovnuqbkuLogMi41OjEKICBtaW4td2lkdGg6IDA7Cn0KCi8vIOaZuuiDveWKqeaJi+mdouadvwouYXNzaXN0YW50LXBhbmVsIHsKICB3aWR0aDogMzIwcHg7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICBvdmVyZmxvdzogaGlkZGVuOwogIGZsZXgtc2hyaW5rOiAwOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBoZWlnaHQ6IDQwMHB4OyAvLyDlh4/lsJHmmbrog73liqnmiYvpnaLmnb/nmoTpq5jluqYKCiAgLnBhbmVsLWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIHBhZGRpbmc6IDE2cHggMjBweDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogICAgYmFja2dyb3VuZDogI2ZhZmJmYzsKICAgIGZsZXgtc2hyaW5rOiAwOwoKICAgIHNwYW4gewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgfQoKICAgIC5oZWFkZXItYWN0aW9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGdhcDogOHB4OwoKICAgICAgLnNlbmQtYnRuIHsKICAgICAgICB3aWR0aDogMjhweDsKICAgICAgICBoZWlnaHQ6IDI4cHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgICAgIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgICAgICAgYm9yZGVyOiBub25lOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogIzQwYTlmZjsKICAgICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7CiAgICAgICAgfQoKICAgICAgICAmOmFjdGl2ZSB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMDk2ZGQ5OwogICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5wYW5lbC1jbG9zZSB7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICBjb2xvcjogIzhjOGM4YzsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5wYW5lbC1jb250ZW50IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgZmxleDogMTsKICAgIG92ZXJmbG93OiBoaWRkZW47CgogICAgLmNoYXQtbWVzc2FnZXMgewogICAgICBmbGV4OiAxOwogICAgICBwYWRkaW5nOiAxNnB4OwogICAgICBvdmVyZmxvdy15OiBhdXRvOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICBnYXA6IDEycHg7CgogICAgICAubWVzc2FnZS1pdGVtIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogICAgICAgIGdhcDogOHB4OwoKICAgICAgICAmLnVzZXItbWVzc2FnZSB7CiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93LXJldmVyc2U7CgogICAgICAgICAgLm1lc3NhZ2UtY29udGVudCB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsKICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweCAxMnB4IDRweCAxMnB4OwogICAgICAgICAgICBtYXgtd2lkdGg6IDcwJTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgICYuYXNzaXN0YW50LW1lc3NhZ2UgewogICAgICAgICAgLm1lc3NhZ2UtYXZhdGFyIHsKICAgICAgICAgICAgd2lkdGg6IDMycHg7CiAgICAgICAgICAgIGhlaWdodDogMzJweDsKICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjBmOGZmOwogICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7CgogICAgICAgICAgICAuYXZhdGFyLWNpcmNsZSB7CiAgICAgICAgICAgICAgd2lkdGg6IDI0cHg7CiAgICAgICAgICAgICAgaGVpZ2h0OiAyNHB4OwogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTZmN2ZmOwogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICAgICAgfQogICAgICAgICAgfQoKICAgICAgICAgIC5tZXNzYWdlLWNvbnRlbnQgewogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1OwogICAgICAgICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweCAxMnB4IDEycHggNHB4OwogICAgICAgICAgICBtYXgtd2lkdGg6IDcwJTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC5tZXNzYWdlLWNvbnRlbnQgewogICAgICAgICAgcGFkZGluZzogOHB4IDEycHg7CgogICAgICAgICAgLm1lc3NhZ2UtdGV4dCB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4OwogICAgICAgICAgfQoKICAgICAgICAgIC5tZXNzYWdlLXRpbWUgewogICAgICAgICAgICBmb250LXNpemU6IDExcHg7CiAgICAgICAgICAgIG9wYWNpdHk6IDAuNzsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5zdWdnZXN0aW9uLWl0ZW0gewogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBwYWRkaW5nOiA4cHggMTJweDsKICAgICAgICBtYXJnaW46IDRweCAwOwogICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnM7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjsKICAgICAgICB9CgogICAgICAgIC5zdWdnZXN0aW9uLWljb24gewogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgfQoKICAgICAgICAuc3VnZ2VzdGlvbi10ZXh0IHsKICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgICAgIGNvbG9yOiAjNjY2OwogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC5pbnB1dC1hcmVhIHsKICAgICAgcGFkZGluZzogMTJweCAxNnB4OwogICAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2YwZjBmMDsKICAgICAgYmFja2dyb3VuZDogI2ZhZmJmYzsKICAgICAgZmxleC1zaHJpbms6IDA7CgogICAgICAuaW5wdXQtd3JhcHBlciB7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgIGdhcDogOHB4OwogICAgICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICAgICAgYm9yZGVyLXJhZGl1czogMjBweDsKICAgICAgICBwYWRkaW5nOiA2cHggMTJweDsKCiAgICAgICAgJjpmb2N1cy13aXRoaW4gewogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOwogICAgICAgIH0KCiAgICAgICAgLmNoYXQtaW5wdXQgewogICAgICAgICAgZmxleDogMTsKICAgICAgICAgIGJvcmRlcjogbm9uZTsKICAgICAgICAgIG91dGxpbmU6IG5vbmU7CiAgICAgICAgICBmb250LXNpemU6IDEzcHg7CiAgICAgICAgICBwYWRkaW5nOiA0cHggMDsKICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OwoKICAgICAgICAgICY6OnBsYWNlaG9sZGVyIHsKICAgICAgICAgICAgY29sb3I6ICNiZmJmYmY7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuaW5wdXQtc2VuZC1idG4gewogICAgICAgICAgd2lkdGg6IDI0cHg7CiAgICAgICAgICBoZWlnaHQ6IDI0cHg7CiAgICAgICAgICBib3JkZXI6IG5vbmU7CiAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsKICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKCiAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgYmFja2dyb3VuZDogI2YwZjhmZjsKICAgICAgICAgIH0KCiAgICAgICAgICAmOmFjdGl2ZSB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlNmY3ZmY7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9CgouY2hhcnQtY2FyZCwgLnZhbHVlLWNhcmQgewogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmNoYXJ0LWhlYWRlciwgLnZhbHVlLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBwYWRkaW5nOiAxNnB4IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgYmFja2dyb3VuZDogI2ZhZmJmYzsKfQoKLmNoYXJ0LXRpdGxlLCAudmFsdWUtdGl0bGUgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDhweDsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzI2MjYyNjsKCiAgLmNoYXJ0LWljb24gewogICAgd2lkdGg6IDE2cHg7CiAgICBoZWlnaHQ6IDE2cHg7CiAgICBiYWNrZ3JvdW5kOiAjMTg5MGZmOwogICAgYm9yZGVyLXJhZGl1czogMnB4OwogIH0KCiAgLmhlbHAtaWNvbiB7CiAgICB3aWR0aDogMTZweDsKICAgIGhlaWdodDogMTZweDsKICAgIGJhY2tncm91bmQ6ICNkOWQ5ZDk7CiAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgY29sb3I6ICNmZmZmZmY7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgfQp9CgouY2hhcnQtbWV0YSwgLnZhbHVlLW1ldGEgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDEycHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjOGM4YzhjOwoKICAuY2hhcnQtZGF0ZSwgLnZhbHVlLWRhdGUgewogICAgY29sb3I6ICM1OTU5NTk7CiAgfQoKICAuY2hhcnQtdHlwZSwgLnZhbHVlLXR5cGUgewogICAgYmFja2dyb3VuZDogI2YwZjBmMDsKICAgIHBhZGRpbmc6IDJweCA2cHg7CiAgICBib3JkZXItcmFkaXVzOiAycHg7CiAgfQoKICAuY2hhcnQtc291cmNlIHsKICAgIGNvbG9yOiAjMTg5MGZmOwogIH0KfQoKLmNoYXJ0LWFjdGlvbnMsIC52YWx1ZS1hY3Rpb25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiA4cHg7CgogIC5hY3Rpb24taWNvbiB7CiAgICB3aWR0aDogMTZweDsKICAgIGhlaWdodDogMTZweDsKICAgIGJvcmRlci1yYWRpdXM6IDJweDsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIGJhY2tncm91bmQtc2l6ZTogMTJweCAxMnB4OwogICAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyOwogICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDsKICAgIHRyYW5zaXRpb246IGFsbCAwLjJzOwoKICAgICYucmVmcmVzaCB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICM1MmM0MWE7CiAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIGZpbGw9J25vbmUnIHZpZXdCb3g9JzAgMCAyNCAyNCcgc3Ryb2tlPSd3aGl0ZScgc3Ryb2tlLXdpZHRoPScyJyUzZSUzY3BhdGggc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBkPSdNNCA0djVoLjU4Mm0xNS4zNTYgMkE4LjAwMSA4LjAwMSAwIDAwNC41ODIgOW0wIDBIOW0xMSAxMXYtNWgtLjU4MW0wIDBhOC4wMDMgOC4wMDMgMCAwMS0xNS4zNTctMm0xNS4zNTcgMkgxNScvJTNlJTNjL3N2ZyUzZSIpOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzczZDEzZDsKICAgICAgfQogICAgfQoKICAgICYuZG93bmxvYWQgewogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTg5MGZmOwogICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPSdub25lJyB2aWV3Qm94PScwIDAgMjQgMjQnIHN0cm9rZT0nd2hpdGUnIHN0cm9rZS13aWR0aD0nMiclM2UlM2NwYXRoIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgZD0nTTEyIDEwdjZtMCAwbC0zLTNtMyAzbDMtM20yIDhIN2EyIDIgMCAwMS0yLTJWNWEyIDIgMCAwMTItMmg1LjU4NmExIDEgMCAwMS43MDcuMjkzbDUuNDE0IDUuNDE0YTEgMSAwIDAxLjI5My43MDdWMTlhMiAyIDAgMDEtMiAyeicvJTNlJTNjL3N2ZyUzZSIpOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQwYTlmZjsKICAgICAgfQogICAgfQoKICAgICYubW9yZSB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICM4YzhjOGM7CiAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIGZpbGw9J25vbmUnIHZpZXdCb3g9JzAgMCAyNCAyNCcgc3Ryb2tlPSd3aGl0ZScgc3Ryb2tlLXdpZHRoPScyJyUzZSUzY3BhdGggc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBkPSdNMTIgNXYuMDFNMTIgMTJ2LjAxTTEyIDE5di4wMU0xMiA2YTEgMSAwIDExMC0yIDEgMSAwIDAxMCAyem0wIDdhMSAxIDAgMTEwLTIgMSAxIDAgMDEwIDJ6bTAgN2ExIDEgMCAxMTAtMiAxIDEgMCAwMTAgMnonLyUzZSUzYy9zdmclM2UiKTsKCiAgICAgICY6aG92ZXIgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNhNmE2YTY7CiAgICAgIH0KICAgIH0KCiAgICAmLnNldHRpbmdzIHsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzcyMmVkMTsKICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgdmlld0JveD0nMCAwIDI0IDI0JyBmaWxsPSd3aGl0ZSclM2UlM2NyZWN0IHg9JzMnIHk9JzEyJyB3aWR0aD0nNCcgaGVpZ2h0PSc5Jy8lM2UlM2NyZWN0IHg9JzEwJyB5PSc4JyB3aWR0aD0nNCcgaGVpZ2h0PScxMycvJTNlJTNjcmVjdCB4PScxNycgeT0nNCcgd2lkdGg9JzQnIGhlaWdodD0nMTcnLyUzZSUzYy9zdmclM2UiKTsKCiAgICAgICY6aG92ZXIgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM5MjU0ZGU7CiAgICAgIH0KICAgIH0KCiAgICAmLmNsb3NlIHsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZjsKICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nbm9uZScgdmlld0JveD0nMCAwIDI0IDI0JyBzdHJva2U9J3doaXRlJyBzdHJva2Utd2lkdGg9JzInJTNlJTNjcGF0aCBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIGQ9J002IDE4TDE4IDZNNiA2bDEyIDEyJy8lM2UlM2Mvc3ZnJTNlIik7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmY3ODc1OwogICAgICB9CiAgICB9CiAgfQoKICAuY2hhcnQtc3RhdHVzIHsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIGNvbG9yOiAjOGM4YzhjOwogICAgbWFyZ2luLWxlZnQ6IDhweDsKICB9Cn0KCi5jaGFydC1jb250ZW50IHsKICBwYWRkaW5nOiAyMHB4Owp9CgouY2hhcnQtbGVnZW5kIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAyMHB4OwogIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgZm9udC1zaXplOiAxMnB4OwoKICAubGVnZW5kLWl0ZW0gewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDZweDsKCiAgICAubGVnZW5kLWNvbG9yIHsKICAgICAgd2lkdGg6IDEycHg7CiAgICAgIGhlaWdodDogMTJweDsKICAgICAgYm9yZGVyLXJhZGl1czogMnB4OwoKICAgICAgJi5ibHVlIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjNUI4RkY5OwogICAgICB9CgogICAgICAmLnllbGxvdyB7CiAgICAgICAgYmFja2dyb3VuZDogI0ZGRDY2NjsKICAgICAgfQoKICAgICAgJi5saW5lIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjRkY2QjZCOwogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgICAgICB3aWR0aDogOHB4OwogICAgICAgIGhlaWdodDogOHB4OwogICAgICB9CiAgICB9CiAgfQp9CgouY2hhcnQtd3JhcHBlciB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAzMDBweDsKfQoKLmNoYXJ0IHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7Cn0KCi52YWx1ZS1jb250ZW50IHsKICBwYWRkaW5nOiAyMHB4Owp9CgoudmFsdWUtbWFpbiB7CiAgLnZhbHVlLWxhYmVsIHsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIGNvbG9yOiAjOGM4YzhjOwogICAgbWFyZ2luLWJvdHRvbTogOHB4OwogICAgZGlzcGxheTogYmxvY2s7CiAgfQoKICAudmFsdWUtbnVtYmVyIHsKICAgIGZvbnQtc2l6ZTogMzZweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgY29sb3I6ICMyNjI2MjY7CiAgICBsaW5lLWhlaWdodDogMTsKICAgIG1hcmdpbi1ib3R0b206IDEycHg7CgogICAgLnZhbHVlLXVuaXQgewogICAgICBmb250LXNpemU6IDE4cHg7CiAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgICBtYXJnaW4tbGVmdDogNHB4OwogICAgfQogIH0KCiAgLnZhbHVlLWNoYW5nZSB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogOHB4OwogICAgZm9udC1zaXplOiAxMnB4OwoKICAgIC5jaGFuZ2UtdGV4dCB7CiAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgfQoKICAgIC5jaGFuZ2UtdmFsdWUgewogICAgICAmLnBvc2l0aXZlIHsKICAgICAgICBjb2xvcjogIzUyYzQxYTsKICAgICAgfQoKICAgICAgJi5uZWdhdGl2ZSB7CiAgICAgICAgY29sb3I6ICNmZjRkNGY7CiAgICAgIH0KICAgIH0KCiAgICAuY2hhbmdlLWFycm93IHsKICAgICAgd2lkdGg6IDA7CiAgICAgIGhlaWdodDogMDsKCiAgICAgICYudXAgewogICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLXJpZ2h0OiA0cHggc29saWQgdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLWJvdHRvbTogNnB4IHNvbGlkICM1MmM0MWE7CiAgICAgIH0KCiAgICAgICYuZG93biB7CiAgICAgICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCB0cmFuc3BhcmVudDsKICAgICAgICBib3JkZXItcmlnaHQ6IDRweCBzb2xpZCB0cmFuc3BhcmVudDsKICAgICAgICBib3JkZXItdG9wOiA2cHggc29saWQgI2ZmNGQ0ZjsKICAgICAgfQogICAgfQogIH0KfQoKLmNvbnRyb2wtcGFuZWwgewogIHBvc2l0aW9uOiBmaXhlZDsKICByaWdodDogMjBweDsKICB0b3A6IDUwJTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7CiAgd2lkdGg6IDI4MHB4OwogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKICB6LWluZGV4OiAxMDAwOwoKICAucGFuZWwtaGVhZGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgcGFkZGluZzogMTZweCAyMHB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgICBiYWNrZ3JvdW5kOiAjZmFmYmZjOwogICAgYm9yZGVyLXJhZGl1czogOHB4IDhweCAwIDA7CgogICAgc3BhbiB7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgY29sb3I6ICMyNjI2MjY7CiAgICB9CgogICAgLnBhbmVsLWNsb3NlIHsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgfQogIH0KCiAgLnBhbmVsLWNvbnRlbnQgewogICAgcGFkZGluZzogMjBweDsKCiAgICAucGFuZWwtc2VjdGlvbiB7CiAgICAgIGg0IHsKICAgICAgICBtYXJnaW46IDAgMCAxMnB4IDA7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICB9CgogICAgICAuc2V0dGluZy1pdGVtIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwoKICAgICAgICBsYWJlbCB7CiAgICAgICAgICBkaXNwbGF5OiBibG9jazsKICAgICAgICAgIG1hcmdpbi1ib3R0b206IDZweDsKICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQoKLy8g5ZON5bqU5byP6K6+6K6hCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsKICAuc2VhcmNoLWNvbnRhaW5lciB7CiAgICAuc2VhcmNoLWZvcm0gewogICAgICAuZm9ybS1yb3cgewogICAgICAgIGdhcDogMTJweDsKICAgICAgfQoKICAgICAgLmZvcm0tZ3JvdXAgewogICAgICAgIC5mb3JtLWlucHV0LmtleXdvcmQtaW5wdXQgewogICAgICAgICAgd2lkdGg6IDE2MHB4OwogICAgICAgIH0KICAgICAgfQoKICAgICAgLmZvcm0tYnV0dG9ucyB7CiAgICAgICAgbWFyZ2luLWxlZnQ6IDA7CiAgICAgICAgbWFyZ2luLXRvcDogOHB4OwogICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsKICAgICAgfQogICAgfQogIH0KCiAgLnRvcC1zZWN0aW9uLCAuYm90dG9tLXNlY3Rpb24gewogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIGdhcDogMTVweDsKICB9CgogIC5hc3Npc3RhbnQtcGFuZWwgewogICAgd2lkdGg6IDEwMCU7CiAgICBvcmRlcjogLTE7IC8vIOaZuuiDveWKqeaJi+mdouadv+WcqOenu+WKqOerr+aYvuekuuWcqOWbvuihqOS4iuaWuQogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLmRhc2hib2FyZC1jb250YWluZXIgewogICAgcGFkZGluZzogMTBweDsKICAgIGdhcDogMTVweDsKICB9CgogIC5zZWFyY2gtY29udGFpbmVyIHsKICAgIC5zZWFyY2gtZm9ybSB7CiAgICAgIC5mb3JtLXJvdyB7CiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICAgICAgICBnYXA6IDEycHg7CiAgICAgIH0KCiAgICAgIC5mb3JtLWdyb3VwIHsKICAgICAgICB3aWR0aDogMTAwJTsKCiAgICAgICAgLmZvcm0taW5wdXQgewogICAgICAgICAgZmxleDogMTsKICAgICAgICAgIG1pbi13aWR0aDogMTIwcHg7CgogICAgICAgICAgJi5rZXl3b3JkLWlucHV0IHsKICAgICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuZm9ybS1zZWxlY3QgewogICAgICAgICAgZmxleDogMTsKICAgICAgICAgIG1pbi13aWR0aDogMTIwcHg7CiAgICAgICAgfQogICAgICB9CgogICAgICAuZm9ybS1idXR0b25zIHsKICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICBtYXJnaW4tdG9wOiAxMnB4OwogICAgICB9CiAgICB9CiAgfQoKICAudG9wLXNlY3Rpb24sIC5ib3R0b20tc2VjdGlvbiB7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgZ2FwOiAxMHB4OwogIH0KCiAgLmFzc2lzdGFudC1wYW5lbCB7CiAgICB3aWR0aDogMTAwJTsKICAgIG9yZGVyOiAtMTsKCiAgICAucGFuZWwtY29udGVudCB7CiAgICAgIHBhZGRpbmc6IDE1cHg7CgogICAgICAuYXNzaXN0YW50LWl0ZW0gewogICAgICAgIHBhZGRpbmc6IDEwcHggMDsKCiAgICAgICAgLmFzc2lzdGFudC1pY29uIHsKICAgICAgICAgIHdpZHRoOiAyOHB4OwogICAgICAgICAgaGVpZ2h0OiAyOHB4OwogICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgIH0KCiAgICAgICAgLmFzc2lzdGFudC10ZXh0IHsKICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5jaGFydC1oZWFkZXIsIC52YWx1ZS1oZWFkZXIgewogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwogICAgZ2FwOiA4cHg7CiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7CiAgfQoKICAuY2hhcnQtbWV0YSwgLnZhbHVlLW1ldGEgewogICAgb3JkZXI6IDE7CiAgfQoKICAuY2hhcnQtYWN0aW9ucywgLnZhbHVlLWFjdGlvbnMgewogICAgb3JkZXI6IDI7CiAgICBhbGlnbi1zZWxmOiBmbGV4LWVuZDsKICB9CgogIC5jaGFydC1jb250ZW50IHsKICAgIHBhZGRpbmc6IDE2cHg7CiAgfQoKICAudmFsdWUtY29udGVudCB7CiAgICBwYWRkaW5nOiAxNnB4OwogIH0KCiAgLmNoYXJ0LXdyYXBwZXIgewogICAgaGVpZ2h0OiAyNTBweDsKICB9CgogIC52YWx1ZS1udW1iZXIgewogICAgZm9udC1zaXplOiAyOHB4ICFpbXBvcnRhbnQ7CgogICAgLnZhbHVlLXVuaXQgewogICAgICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDsKICAgIH0KICB9Cn0KCi8vIOabtOWkmuaTjeS9nOW8ueeql+agt+W8jwoubW9yZS1wb3B1cCB7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4OwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgbWluLXdpZHRoOiAxNjBweDsKICBvdmVyZmxvdzogaGlkZGVuOwogIHBvc2l0aW9uOiBmaXhlZDsKICB6LWluZGV4OiAyMDAwOwoKICAubW9yZS1wb3B1cC1jb250ZW50IHsKICAgIC5tb3JlLWFjdGlvbi1pdGVtIHsKICAgICAgcGFkZGluZzogMTJweCAxNnB4OwogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzOwogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Y1ZjVmNTsKICAgICAgbGluZS1oZWlnaHQ6IDEuNDsKCiAgICAgICY6bGFzdC1jaGlsZCB7CiAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsKICAgICAgfQoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYmFja2dyb3VuZDogI2YwZjhmZjsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgfQoKICAgICAgJjphY3RpdmUgewogICAgICAgIGJhY2tncm91bmQ6ICNlNmY3ZmY7CiAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgIH0KICAgIH0KICB9Cn0KCi8vIOWIhuS6q+W8ueeql+agt+W8jwouc2hhcmUtcG9wdXAtb3ZlcmxheSB7CiAgcG9zaXRpb246IGZpeGVkOwogIHRvcDogMDsKICBsZWZ0OiAwOwogIHJpZ2h0OiAwOwogIGJvdHRvbTogMDsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNSk7CiAgei1pbmRleDogOTk5OTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7Cn0KCi5zaGFyZS1wb3B1cCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHdpZHRoOiA0MDBweDsKICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLnNoYXJlLXBvcHVwLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAxNnB4IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgYmFja2dyb3VuZDogI2ZhZmFmYTsKfQoKLnNoYXJlLXBvcHVwLXRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzMzMzsKfQoKLnNoYXJlLXBvcHVwLWNsb3NlIHsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlcjogbm9uZTsKICBmb250LXNpemU6IDIwcHg7CiAgY29sb3I6ICM5OTk7CiAgY3Vyc29yOiBwb2ludGVyOwogIHBhZGRpbmc6IDA7CiAgd2lkdGg6IDI0cHg7CiAgaGVpZ2h0OiAyNHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKCiAgJjpob3ZlciB7CiAgICBjb2xvcjogIzY2NjsKICB9Cn0KCi5zaGFyZS1wb3B1cC1jb250ZW50IHsKICBwYWRkaW5nOiAyMHB4Owp9Cgouc2hhcmUtZGVzY3JpcHRpb24gewogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCi5zaGFyZS1vcHRpb24gewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnNoYXJlLW9wdGlvbi1sYWJlbCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjMzMzOwp9Cgouc2hhcmUtdG9nZ2xlIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KCi50b2dnbGUtaW5wdXQgewogIGRpc3BsYXk6IG5vbmU7Cn0KCi50b2dnbGUtbGFiZWwgewogIGRpc3BsYXk6IGJsb2NrOwogIHdpZHRoOiA0NHB4OwogIGhlaWdodDogMjRweDsKICBiYWNrZ3JvdW5kOiAjZGRkOwogIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuM3M7CgogICY6OmFmdGVyIHsKICAgIGNvbnRlbnQ6ICcnOwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAycHg7CiAgICBsZWZ0OiAycHg7CiAgICB3aWR0aDogMjBweDsKICAgIGhlaWdodDogMjBweDsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3M7CiAgfQp9CgoudG9nZ2xlLWlucHV0OmNoZWNrZWQgKyAudG9nZ2xlLWxhYmVsIHsKICBiYWNrZ3JvdW5kOiAjMTg5MGZmOwoKICAmOjphZnRlciB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMjBweCk7CiAgfQp9Cgouc2hhcmUtbGluay1zZWN0aW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogOHB4Owp9Cgouc2hhcmUtbGluay1pbnB1dCB7CiAgZmxleDogMTsKICBoZWlnaHQ6IDM2cHg7CiAgcGFkZGluZzogMCAxMnB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzY2NjsKICBiYWNrZ3JvdW5kOiAjZjVmNWY1OwogIG91dGxpbmU6IG5vbmU7Cn0KCi5jb3B5LWxpbmstYnRuIHsKICBoZWlnaHQ6IDM2cHg7CiAgcGFkZGluZzogMCAxNnB4OwogIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgY29sb3I6IHdoaXRlOwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgZm9udC1zaXplOiAxNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuM3M7CgogICY6aG92ZXIgewogICAgYmFja2dyb3VuZDogIzQwYTlmZjsKICB9CgogICY6YWN0aXZlIHsKICAgIGJhY2tncm91bmQ6ICMwOTZkZDk7CiAgfQp9CgovLyDljaHniYfmj5DphpLlvLnnqpfmoLflvI8KLnJlbWluZGVyLXBvcHVwLW92ZXJsYXkgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpOwogIHotaW5kZXg6IDEwMDAwICFpbXBvcnRhbnQ7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgoucmVtaW5kZXItcG9wdXAgewogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICB3aWR0aDogNTIwcHg7CiAgbWF4LXdpZHRoOiA5MHZ3OwogIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHotaW5kZXg6IDEwMDAxICFpbXBvcnRhbnQ7CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgoucmVtaW5kZXItcG9wdXAtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDE2cHggMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwp9CgoucmVtaW5kZXItcG9wdXAtdGl0bGUgewogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogNTAwOwogIGNvbG9yOiAjMzMzOwp9CgoucmVtaW5kZXItcG9wdXAtY2xvc2UgewogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyOiBub25lOwogIGZvbnQtc2l6ZTogMjBweDsKICBjb2xvcjogIzk5OTsKICBjdXJzb3I6IHBvaW50ZXI7CiAgcGFkZGluZzogMDsKICB3aWR0aDogMjRweDsKICBoZWlnaHQ6IDI0cHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwoKICAmOmhvdmVyIHsKICAgIGNvbG9yOiAjNjY2OwogIH0KfQoKLnJlbWluZGVyLXBvcHVwLWNvbnRlbnQgewogIHBhZGRpbmc6IDIwcHg7Cn0KCi5yZW1pbmRlci1mb3JtLWl0ZW0gewogIG1hcmdpbi1ib3R0b206IDE2cHg7CgogIC5yZW1pbmRlci1sYWJlbCB7CiAgICBkaXNwbGF5OiBibG9jazsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGNvbG9yOiAjMzMzOwogICAgbWFyZ2luLWJvdHRvbTogOHB4OwogICAgZm9udC13ZWlnaHQ6IDUwMDsKICB9CgogIC5yZW1pbmRlci1zZWxlY3QgewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDM2cHg7CiAgICBwYWRkaW5nOiAwIDEycHg7CiAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgZm9udC1zaXplOiAxNHB4OwogICAgY29sb3I6ICMzMzM7CiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICAgIG91dGxpbmU6IG5vbmU7CiAgICBjdXJzb3I6IHBvaW50ZXI7CgogICAgJjpmb2N1cyB7CiAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOwogICAgfQogIH0KCiAgLnJlbWluZGVyLWlucHV0IHsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAzNnB4OwogICAgcGFkZGluZzogMCAxMnB4OwogICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGNvbG9yOiAjMzMzOwogICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICBvdXRsaW5lOiBub25lOwoKICAgICY6Zm9jdXMgewogICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgIH0KCiAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgIGNvbG9yOiAjYmZiZmJmOwogICAgfQogIH0KCiAgLnJlbWluZGVyLWNoYW5nZS1zZWN0aW9uIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBnYXA6IDEycHg7CgogICAgLnJlbWluZGVyLXNlbGVjdC1zbWFsbCB7CiAgICAgIGZsZXg6IDE7CiAgICAgIGhlaWdodDogMzZweDsKICAgICAgcGFkZGluZzogMCAxMnB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY29sb3I6ICMzMzM7CiAgICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgICBvdXRsaW5lOiBub25lOwogICAgICBjdXJzb3I6IHBvaW50ZXI7CgogICAgICAmOmZvY3VzIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOwogICAgICB9CiAgICB9CiAgfQoKICAucmVtaW5kZXItdGhyZXNob2xkLXNlY3Rpb24gewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDEycHg7CgogICAgLnJlbWluZGVyLW51bWJlci1pbnB1dCB7CiAgICAgIHdpZHRoOiAxMjBweDsKICAgICAgaGVpZ2h0OiAzNnB4OwogICAgICBwYWRkaW5nOiAwIDEycHg7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogIzMzMzsKICAgICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICAgIG91dGxpbmU6IG5vbmU7CgogICAgICAmOmZvY3VzIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOwogICAgICB9CiAgICB9CgogICAgLnJlbWluZGVyLXVuaXQgewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGNvbG9yOiAjNjY2OwogICAgfQoKICAgIC5yZW1pbmRlci1jaGVja2JveC1zZWN0aW9uIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgZ2FwOiA2cHg7CiAgICAgIG1hcmdpbi1sZWZ0OiBhdXRvOwoKICAgICAgLnJlbWluZGVyLWNoZWNrYm94IHsKICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB9CgogICAgICAucmVtaW5kZXItY2hlY2tib3gtbGFiZWwgewogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzMzMzsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgbWFyZ2luOiAwOwogICAgICB9CiAgICB9CiAgfQoKICAucmVtaW5kZXItbWV0aG9kLXNlY3Rpb24gewogICAgZGlzcGxheTogZmxleDsKICAgIGdhcDogMjBweDsKCiAgICAucmVtaW5kZXItcmFkaW8taXRlbSB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGdhcDogNnB4OwoKICAgICAgLnJlbWluZGVyLXJhZGlvIHsKICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB9CgogICAgICAucmVtaW5kZXItcmFkaW8tbGFiZWwgewogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzMzMzsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgbWFyZ2luOiAwOwogICAgICB9CiAgICB9CiAgfQp9CgoucmVtaW5kZXItZGVzY3JpcHRpb24gewogIGZvbnQtc2l6ZTogMTNweDsKICBjb2xvcjogIzk5OTsKICBtYXJnaW46IDE2cHggMDsKICBwYWRkaW5nOiAxMnB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCi5yZW1pbmRlci1wb3B1cC1mb290ZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICBnYXA6IDEycHg7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMGYwOwogIGJhY2tncm91bmQ6ICNmYWZhZmE7Cn0KCi5yZW1pbmRlci1jYW5jZWwtYnRuIHsKICBoZWlnaHQ6IDM2cHg7CiAgcGFkZGluZzogMCAxNnB4OwogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7CgogICY6aG92ZXIgewogICAgY29sb3I6ICMxODkwZmY7CiAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgfQp9CgoucmVtaW5kZXItY29uZmlybS1idG4gewogIGhlaWdodDogMzZweDsKICBwYWRkaW5nOiAwIDE2cHg7CiAgYmFja2dyb3VuZDogIzE4OTBmZjsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGNvbG9yOiB3aGl0ZTsKICBmb250LXNpemU6IDE0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4zczsKCiAgJjpob3ZlciB7CiAgICBiYWNrZ3JvdW5kOiAjNDBhOWZmOwogIH0KCiAgJjphY3RpdmUgewogICAgYmFja2dyb3VuZDogIzA5NmRkOTsKICB9Cn0KCi8vIOS4iuS8oENTVuW8ueeql+agt+W8jwoudXBsb2FkLXBvcHVwLW92ZXJsYXkgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpOwogIHotaW5kZXg6IDk5OTk7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9CgoudXBsb2FkLXBvcHVwIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgd2lkdGg6IDQ4MHB4OwogIG1heC13aWR0aDogOTB2dzsKICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLnVwbG9hZC1wb3B1cC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogIGJhY2tncm91bmQ6ICNmYWZhZmE7Cn0KCi51cGxvYWQtcG9wdXAtdGl0bGUgewogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogNTAwOwogIGNvbG9yOiAjMzMzOwp9CgoudXBsb2FkLXBvcHVwLWNsb3NlIHsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlcjogbm9uZTsKICBmb250LXNpemU6IDIwcHg7CiAgY29sb3I6ICM5OTk7CiAgY3Vyc29yOiBwb2ludGVyOwogIHBhZGRpbmc6IDA7CiAgd2lkdGg6IDI0cHg7CiAgaGVpZ2h0OiAyNHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKCiAgJjpob3ZlciB7CiAgICBjb2xvcjogIzY2NjsKICB9Cn0KCi51cGxvYWQtcG9wdXAtY29udGVudCB7CiAgcGFkZGluZzogMjBweDsKfQoKLnVwbG9hZC1mb3JtLWl0ZW0gewogIG1hcmdpbi1ib3R0b206IDE2cHg7CgogIC51cGxvYWQtbGFiZWwgewogICAgZGlzcGxheTogYmxvY2s7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBjb2xvcjogIzMzMzsKICAgIG1hcmdpbi1ib3R0b206IDhweDsKICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgfQoKICAudXBsb2FkLWlucHV0IHsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAzNnB4OwogICAgcGFkZGluZzogMCAxMnB4OwogICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGNvbG9yOiAjMzMzOwogICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICBvdXRsaW5lOiBub25lOwoKICAgICY6Zm9jdXMgewogICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgIH0KCiAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgIGNvbG9yOiAjYmZiZmJmOwogICAgfQogIH0KCiAgLnVwbG9hZC10ZXh0YXJlYSB7CiAgICB3aWR0aDogMTAwJTsKICAgIHBhZGRpbmc6IDhweCAxMnB4OwogICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGNvbG9yOiAjMzMzOwogICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICBvdXRsaW5lOiBub25lOwogICAgcmVzaXplOiB2ZXJ0aWNhbDsKICAgIG1pbi1oZWlnaHQ6IDgwcHg7CgogICAgJjpmb2N1cyB7CiAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOwogICAgfQoKICAgICY6OnBsYWNlaG9sZGVyIHsKICAgICAgY29sb3I6ICNiZmJmYmY7CiAgICB9CiAgfQoKICAudXBsb2FkLWZpbGUtc2VjdGlvbiB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogMTJweDsKCiAgICAudXBsb2FkLWZpbGUtaW5wdXQgewogICAgICBkaXNwbGF5OiBub25lOwogICAgfQoKICAgIC51cGxvYWQtZmlsZS1idXR0b24gewogICAgICBoZWlnaHQ6IDM2cHg7CiAgICAgIHBhZGRpbmc6IDAgMTZweDsKICAgICAgYmFja2dyb3VuZDogIzE4OTBmZjsKICAgICAgYm9yZGVyOiBub25lOwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGNvbG9yOiB3aGl0ZTsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4zczsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjNDBhOWZmOwogICAgICB9CgogICAgICAmOmFjdGl2ZSB7CiAgICAgICAgYmFja2dyb3VuZDogIzA5NmRkOTsKICAgICAgfQogICAgfQoKICAgIC51cGxvYWQtZmlsZS1uYW1lIHsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogIzMzMzsKICAgICAgZmxleDogMTsKICAgIH0KCiAgICAudXBsb2FkLWZpbGUtcGxhY2Vob2xkZXIgewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGNvbG9yOiAjYmZiZmJmOwogICAgICBmbGV4OiAxOwogICAgfQogIH0KfQoKLnVwbG9hZC10aXBzIHsKICBtYXJnaW4tdG9wOiAyMHB4OwogIHBhZGRpbmc6IDEycHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjMTg5MGZmOwoKICAudXBsb2FkLXRpcHMtdGl0bGUgewogICAgZm9udC1zaXplOiAxNHB4OwogICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgIGNvbG9yOiAjMzMzOwogICAgbWFyZ2luLWJvdHRvbTogOHB4OwogIH0KCiAgLnVwbG9hZC10aXBzLWNvbnRlbnQgewogICAgZm9udC1zaXplOiAxM3B4OwogICAgY29sb3I6ICM2NjY7CiAgICBsaW5lLWhlaWdodDogMS42OwogIH0KfQoKLnVwbG9hZC1wb3B1cC1mb290ZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKICBnYXA6IDEycHg7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMGYwOwogIGJhY2tncm91bmQ6ICNmYWZhZmE7Cn0KCi51cGxvYWQtY2FuY2VsLWJ0biB7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDAgMTZweDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzOwoKICAmOmhvdmVyIHsKICAgIGNvbG9yOiAjMTg5MGZmOwogICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogIH0KfQoKLnVwbG9hZC1jb25maXJtLWJ0biB7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDAgMTZweDsKICBiYWNrZ3JvdW5kOiAjMTg5MGZmOwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMTRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjNzOwoKICAmOmhvdmVyIHsKICAgIGJhY2tncm91bmQ6ICM0MGE5ZmY7CiAgfQoKICAmOmFjdGl2ZSB7CiAgICBiYWNrZ3JvdW5kOiAjMDk2ZGQ5OwogIH0KfQoKLy8g6K6+572u5by556qX5qC35byPCi5zZXR0aW5ncy1wb3B1cCB7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4OwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgcGFkZGluZzogOHB4OwogIG92ZXJmbG93OiBoaWRkZW47CgogIC5zZXR0aW5ncy1wb3B1cC1jb250ZW50IHsKICAgIC5jaGFydC10eXBlcy1ncmlkIHsKICAgICAgZGlzcGxheTogZ3JpZDsKICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNCwgMWZyKTsKICAgICAgZ2FwOiA4cHg7CgogICAgICAuY2hhcnQtdHlwZS1pdGVtIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICBwYWRkaW5nOiA4cHggNHB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgbWluLWhlaWdodDogNjBweDsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjBmOGZmOwogICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOwogICAgICAgIH0KCiAgICAgICAgJjphY3RpdmUgewogICAgICAgICAgYmFja2dyb3VuZDogI2U2ZjdmZjsKICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsKICAgICAgICB9CgogICAgICAgIC5jaGFydC1pY29uIHsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7CgogICAgICAgICAgc3ZnIHsKICAgICAgICAgICAgd2lkdGg6IDIwcHg7CiAgICAgICAgICAgIGhlaWdodDogMjBweDsKICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuY2hhcnQtbGFiZWwgewogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgY29sb3I6ICMyNjI2MjY7CiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICBsaW5lLWhlaWdodDogMS4yOwogICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICAgICAgICB9CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgLmNoYXJ0LWljb24gc3ZnIHsKICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpOwogICAgICAgICAgfQoKICAgICAgICAgIC5jaGFydC1sYWJlbCB7CiAgICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQoKLy8g5pCc57Si5Y6G5Y+y5by556qX5qC35byPCi5zZWFyY2gtaGlzdG9yeS1wb3B1cCB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogMTAwJTsKICBsZWZ0OiAwOwogIHJpZ2h0OiAwOwogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyOiAxcHggc29saWQgI2U4ZThlODsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOwogIHotaW5kZXg6IDEwMDA7CiAgbWF4LWhlaWdodDogNDAwcHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKCiAgLnNlYXJjaC1oaXN0b3J5LXBvcHVwLXRpdGxlIHsKICAgIHBhZGRpbmc6IDEycHggMTZweCA4cHg7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBmb250LXdlaWdodDogNTAwOwogICAgY29sb3I6ICMyNjI2MjY7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKCiAgICAmLmhvdCB7CiAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMGYwOwogICAgICBib3JkZXItYm90dG9tOiBub25lOwogICAgfQogIH0KCiAgLnNlYXJjaC1oaXN0b3J5LXBvcHVwLWxpc3QgewogICAgcGFkZGluZzogOHB4IDA7CgogICAgLnNlYXJjaC1oaXN0b3J5LXBvcHVwLWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBwYWRkaW5nOiA4cHggMTZweDsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuMnM7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY29sb3I6ICM1OTU5NTk7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1OwogICAgICB9CgogICAgICAuaGlzdG9yeS1pY29uLAogICAgICAuaG90LWljb24gewogICAgICAgIHdpZHRoOiAxNnB4OwogICAgICAgIGhlaWdodDogMTZweDsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgICAgICBmbGV4LXNocmluazogMDsKICAgICAgfQoKICAgICAgLmhpc3RvcnktaWNvbiB7CiAgICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNVFlpSUdobGFXZG9kRDBpTVRZaUlIWnBaWGRDYjNnOUlqQWdNQ0F4TmlBeE5pSWdabWxzYkQwaWJtOXVaU0lnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JajRLUEhCaGRHZ2daRDBpVFRnZ01VTXhNUzQ0TmpZZ01TQXhOU0EwTGpFek5DQXhOU0E0UXpFMUlERXhMamcyTmlBeE1TNDROallnTVRVZ09DQXhOVU0wTGpFek5DQXhOU0F4SURFeExqZzJOaUF4SURoRE1TQTBMakV6TkNBMExqRXpOQ0F4SURnZ01Wb2lJSE4wY205clpUMGlJelU1TlRrMU9TSWdjM1J5YjJ0bExYZHBaSFJvUFNJeExqVWlMejRLUEhCaGRHZ2daRDBpVFRnZ05GWTRUREV3TGpVZ01UQXVOU0lnYzNSeWIydGxQU0lqTlRrMU9UVTVJaUJ6ZEhKdmEyVXRkMmxrZEdnOUlqRXVOU0lnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lpQnpkSEp2YTJVdGJHbHVaV3B2YVc0OUluSnZkVzVrSWk4K0Nqd3ZjM1puUGdvPScpIG5vLXJlcGVhdCBjZW50ZXI7CiAgICAgICAgYmFja2dyb3VuZC1zaXplOiBjb250YWluOwogICAgICB9CgogICAgICAuaG90LWljb24gewogICAgICAgIGJhY2tncm91bmQ6IHVybCgnZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlCM2FXUjBhRDBpTVRZaUlHaGxhV2RvZEQwaU1UWWlJSFpwWlhkQ2IzZzlJakFnTUNBeE5pQXhOaUlnWm1sc2JEMGlibTl1WlNJZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWo0S1BIQmhkR2dnWkQwaVRUZ2dNa3d4TUNBMlNERTBUREV4SURsTU1USWdNVE5NT0NBeE1VdzBJREV6VERVZ09Vd3lJRFpJTmt3NElESmFJaUJtYVd4c1BTSWpSa1kwUkRSR0lpOCtDand2YzNablBnbz0nKSBuby1yZXBlYXQgY2VudGVyOwogICAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjsKICAgICAgfQoKICAgICAgJi5ob3QgewogICAgICAgIGNvbG9yOiAjZmY0ZDRmOwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyrDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/datasearch", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- 顶部搜索栏 -->\n    <div class=\"search-container\">\n      <div class=\"search-form\">\n        <div class=\"search-input-wrapper\">\n          <input\n            type=\"text\"\n            v-model=\"searchForm.keyword\"\n            class=\"search-input\"\n            placeholder=\"搜索 门店营业额 前十 门店 营业额\"\n            @focus=\"handleSearchFocus\"\n            @keyup.enter=\"handleSearch\"\n            ref=\"searchInput\"\n          />\n          <!-- 搜索历史弹窗 -->\n          <div v-if=\"showSearchPopup\" class=\"search-history-popup\" ref=\"searchHistoryPopup\">\n            <div class=\"search-history-popup-title\">搜索历史</div>\n            <div class=\"search-history-popup-list\">\n              <div class=\"search-history-popup-item\" v-for=\"(item, index) in historyList\" :key=\"'history-' + index\" @click=\"setSearchKeyword(item)\">\n                <i class=\"history-icon\"></i>{{ item }}\n              </div>\n            </div>\n            <div class=\"search-history-popup-title hot\">热门搜索</div>\n            <div class=\"search-history-popup-list\">\n              <div class=\"search-history-popup-item hot\" v-for=\"(item, index) in recommendList\" :key=\"'recommend-' + index\" @click=\"setSearchKeyword(item)\">\n                <i class=\"hot-icon\"></i>{{ item }}\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"search-buttons\">\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleSearch\">\n            <i class=\"search-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handlePrevious\">\n            <i class=\"left-arrow-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleNext\">\n            <i class=\"right-arrow-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleFilter\" ref=\"filterButton\">\n            <i class=\"filter-icon\"></i>\n          </button>\n        </div>\n\n        <!-- 筛选弹窗 -->\n        <div v-if=\"showFilterPopup\" class=\"filter-popup\" :style=\"filterPopupStyle\">\n          <div class=\"popup-header\">\n            <span>数据</span>\n            <button class=\"popup-close\" @click=\"closeFilterPopup\">×</button>\n          </div>\n          <div class=\"popup-search\">\n            <input type=\"text\" class=\"search-input\" placeholder=\"搜索\" v-model=\"filterSearchQuery\">\n            <i class=\"search-icon\"></i>\n          </div>\n          <div class=\"popup-tabs\">\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '维度' }\" @click=\"activeTab = '维度'\">维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '时间维度' }\" @click=\"activeTab = '时间维度'\">时间维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '指标' }\" @click=\"activeTab = '指标'\">指标</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '分析' }\" @click=\"activeTab = '分析'\">分析</div>\n          </div>\n          <div class=\"popup-content\">\n            <div v-if=\"activeTab === '维度'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('门店')\">\n                <span>门店</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('品牌')\">\n                <span>品牌</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('等')\">\n                <span>等</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('综合分析')\">\n                <span>综合分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('门店营业额')\">\n                <span>门店营业额</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('数据分析')\">\n                <span>数据分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n            </div>\n            <div v-if=\"activeTab === '时间维度'\" class=\"tab-content\">\n              <div class=\"filter-item\">\n                <span>日期</span>\n                <i class=\"arrow-icon down\">v</i>\n              </div>\n              <div class=\"time-units-row\">\n                <span class=\"time-unit\" @click=\"selectFilter('日')\">日</span>\n                <span class=\"time-unit\" @click=\"selectFilter('周')\">周</span>\n                <span class=\"time-unit\" @click=\"selectFilter('月')\">月</span>\n                <span class=\"time-unit\" @click=\"selectFilter('季')\">季</span>\n                <span class=\"time-unit\" @click=\"selectFilter('年')\">年</span>\n              </div>\n              <div class=\"time-item\">当日</div>\n              <div class=\"time-item\">数天</div>\n              <div class=\"time-item\">数十天</div>\n              <div class=\"time-item\">数月</div>\n              <div class=\"time-item\">2月1日至16日</div>\n              <div class=\"time-item\">2月1日至今</div>\n            </div>\n            <div v-if=\"activeTab === '指标'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进店顾客')\">进店顾客</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客单')\">客单</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('利润')\">利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('销售额')\">销售额</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进货数量')\">进货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('退货数量')\">退货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('总价值')\">总价值</div>\n              <div class=\"filter-item\" @click=\"selectFilter('公司利润率')\">公司利润率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客户数量')\">客户数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('今日利润')\">今日利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('全店成本率')\">全店成本率</div>\n            </div>\n            <div v-if=\"activeTab === '分析'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('增长')\">增长</div>\n              <div class=\"filter-item\" @click=\"selectFilter('开店')\">开店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n              <div class=\"filter-item\" @click=\"selectFilter('成交率')\">成交率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 顶部：门店营业额前十的 + 智能助手 -->\n    <div class=\"top-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton1\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"storeRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <div class=\"header-actions\">\n            <button class=\"send-btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <i class=\"panel-close\">×</i>\n          </div>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"chat-messages\">\n            <div v-for=\"(message, index) in chatMessages\" :key=\"index\"\n                 :class=\"['message-item', message.type === 'user' ? 'user-message' : 'assistant-message']\">\n              <div v-if=\"message.type === 'assistant'\" class=\"message-avatar\">\n                <div class=\"avatar-circle\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z\" fill=\"#1890ff\"/>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"message-content\">\n                <div class=\"message-text\" v-html=\"message.text.replace(/\\n/g, '<br>')\"></div>\n                <div class=\"message-time\">{{ message.time }}</div>\n              </div>\n            </div>\n            <div v-if=\"chatMessages.length === 1\" v-for=\"suggestion in suggestions\" :key=\"suggestion.text\"\n                 class=\"suggestion-item\" @click=\"handleSuggestionClick(suggestion)\">\n              <div class=\"suggestion-icon\">{{ suggestion.icon }}</div>\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <div class=\"input-wrapper\">\n              <input type=\"text\" v-model=\"chatInput\" @keyup.enter=\"sendChatMessage\"\n                     placeholder=\"请输入您的问题...\" class=\"chat-input\">\n              <button class=\"input-send-btn\" @click=\"sendChatMessage\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"#1890ff\"/>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 中间：营业额同比 单独一行 -->\n    <div class=\"middle-section\">\n      <div class=\"value-card\">\n        <div class=\"value-header\">\n          <div class=\"value-title\">\n            <i class=\"chart-icon\"></i>\n            <span>营业额同比</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"value-meta\">\n            <span class=\"value-date\">2024-01-01 至 12-31</span>\n            <span class=\"value-type\">月报</span>\n          </div>\n          <div class=\"value-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton2\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n          </div>\n        </div>\n        <div class=\"value-content\">\n          <div class=\"value-main\">\n            <span class=\"value-label\">营业额(总) / 元</span>\n            <div class=\"value-number\">165.32<span class=\"value-unit\">亿</span></div>\n            <div class=\"value-change\">\n              <span class=\"change-text\">同比上期</span>\n              <span class=\"change-value positive\">+4.73%(+7.43亿)</span>\n              <i class=\"change-arrow up\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部：品牌门店营业额前十的 + 智能助手 -->\n    <div class=\"bottom-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>品牌门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton3\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"cloudRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <div class=\"header-actions\">\n            <button class=\"send-btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <i class=\"panel-close\">×</i>\n          </div>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"chat-messages\">\n            <div v-for=\"(message, index) in chatMessages\" :key=\"'bottom-' + index\"\n                 :class=\"['message-item', message.type === 'user' ? 'user-message' : 'assistant-message']\">\n              <div v-if=\"message.type === 'assistant'\" class=\"message-avatar\">\n                <div class=\"avatar-circle\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z\" fill=\"#1890ff\"/>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"message-content\">\n                <div class=\"message-text\" v-html=\"message.text.replace(/\\n/g, '<br>')\"></div>\n                <div class=\"message-time\">{{ message.time }}</div>\n              </div>\n            </div>\n            <div v-if=\"chatMessages.length === 1\" v-for=\"suggestion in suggestions\" :key=\"'bottom-' + suggestion.text\"\n                 class=\"suggestion-item\" @click=\"handleSuggestionClick(suggestion)\">\n              <div class=\"suggestion-icon\">{{ suggestion.icon }}</div>\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <div class=\"input-wrapper\">\n              <input type=\"text\" v-model=\"chatInput\" @keyup.enter=\"sendChatMessage\"\n                     placeholder=\"请输入您的问题...\" class=\"chat-input\">\n              <button class=\"input-send-btn\" @click=\"sendChatMessage\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"#1890ff\"/>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 更多操作弹窗 -->\n    <div v-if=\"showMorePopup\" class=\"more-popup\" :style=\"morePopupStyle\" @click.stop>\n      <div class=\"more-popup-content\">\n        <div class=\"more-action-item\" @click.stop=\"handleCardReminder\">卡片提醒</div>\n        <div class=\"more-action-item\" @click.stop=\"handleShareCard\">分享卡片</div>\n        <div class=\"more-action-item\" @click=\"handleSaveCard\">保存卡片</div>\n        <div class=\"more-action-item\" @click.stop=\"handleUploadCSV\">上传CSV</div>\n        <div class=\"more-action-item\" @click=\"handleDownloadPNG\">下载PNG</div>\n      </div>\n    </div>\n\n    <!-- 分享卡片弹窗 -->\n    <div v-if=\"showSharePopup\" class=\"share-popup-overlay\" @click=\"closeSharePopup\">\n      <div class=\"share-popup\" @click.stop>\n        <div class=\"share-popup-header\">\n          <span class=\"share-popup-title\">分享链接</span>\n          <button class=\"share-popup-close\" @click=\"closeSharePopup\">×</button>\n        </div>\n        <div class=\"share-popup-content\">\n          <div class=\"share-description\">\n            分享分析结果，让更多的人看到你的洞察\n          </div>\n          <div class=\"share-option\">\n            <div class=\"share-option-label\">\n              <span>代码嵌入功能</span>\n            </div>\n            <div class=\"share-toggle\">\n              <input type=\"checkbox\" id=\"embedToggle\" v-model=\"embedEnabled\" class=\"toggle-input\">\n              <label for=\"embedToggle\" class=\"toggle-label\"></label>\n            </div>\n          </div>\n          <div class=\"share-link-section\">\n            <input\n              type=\"text\"\n              class=\"share-link-input\"\n              :value=\"shareLink\"\n              readonly\n              placeholder=\"https://dwz.cn/jzwMdMh\"\n            >\n            <button class=\"copy-link-btn\" @click=\"copyShareLink\">复制链接</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 卡片提醒弹窗 -->\n    <div v-if=\"showReminderPopup\" class=\"reminder-popup-overlay\" @click=\"closeReminderPopup\">\n      <div class=\"reminder-popup\" @click.stop>\n        <div class=\"reminder-popup-header\">\n          <span class=\"reminder-popup-title\">卡片提醒设置</span>\n          <button class=\"reminder-popup-close\" @click=\"closeReminderPopup\">×</button>\n        </div>\n        <div class=\"reminder-popup-content\">\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒卡片</label>\n            <select class=\"reminder-select\" v-model=\"reminderForm.cardName\">\n              <option value=\"\">请选择卡片</option>\n              <option value=\"门店营业额前十\">门店营业额前十</option>\n              <option value=\"云营业额前十\">云营业额前十</option>\n            </select>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒邮箱地址</label>\n            <input\n              type=\"email\"\n              class=\"reminder-input\"\n              v-model=\"reminderForm.email\"\n              placeholder=\"请输入邮箱地址\"\n            >\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">数据变化</label>\n            <div class=\"reminder-change-section\">\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.changeType\">\n                <option value=\"同比增减幅\">同比增减幅/元</option>\n                <option value=\"环比增减幅\">环比增减幅/元</option>\n              </select>\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.timePeriod\">\n                <option value=\"天数\">天数(天)</option>\n                <option value=\"周数\">周数(周)</option>\n                <option value=\"月数\">月数(月)</option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <div class=\"reminder-threshold-section\">\n              <input\n                type=\"number\"\n                class=\"reminder-number-input\"\n                v-model=\"reminderForm.threshold\"\n                placeholder=\"0\"\n              >\n              <span class=\"reminder-unit\">元</span>\n              <div class=\"reminder-checkbox-section\">\n                <input\n                  type=\"checkbox\"\n                  id=\"contentChange\"\n                  v-model=\"reminderForm.contentChange\"\n                  class=\"reminder-checkbox\"\n                >\n                <label for=\"contentChange\" class=\"reminder-checkbox-label\">内容变化提醒</label>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"reminder-description\">\n            当选择指标比上月数据变化超过设定阈值时，发送邮件提醒\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒方式</label>\n            <div class=\"reminder-method-section\">\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"emailMethod\"\n                  value=\"email\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"emailMethod\" class=\"reminder-radio-label\">邮件提醒</label>\n              </div>\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"smsMethod\"\n                  value=\"sms\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"smsMethod\" class=\"reminder-radio-label\">短信提醒</label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"reminder-popup-footer\">\n          <button class=\"reminder-cancel-btn\" @click=\"closeReminderPopup\">取消</button>\n          <button class=\"reminder-confirm-btn\" @click=\"confirmReminder\">确定，设置提醒人</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 上传CSV弹窗 -->\n    <div v-if=\"showUploadPopup\" class=\"upload-popup-overlay\" @click=\"closeUploadPopup\">\n      <div class=\"upload-popup\" @click.stop>\n        <div class=\"upload-popup-header\">\n          <span class=\"upload-popup-title\">加入报告</span>\n          <button class=\"upload-popup-close\" @click=\"closeUploadPopup\">×</button>\n        </div>\n        <div class=\"upload-popup-content\">\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">报告名称</label>\n            <input\n              type=\"text\"\n              class=\"upload-input\"\n              v-model=\"uploadForm.reportName\"\n              placeholder=\"请输入报告名称\"\n            >\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">描述信息</label>\n            <textarea\n              class=\"upload-textarea\"\n              v-model=\"uploadForm.description\"\n              placeholder=\"请输入描述信息\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">上传文件</label>\n            <div class=\"upload-file-section\">\n              <input\n                type=\"file\"\n                accept=\".csv\"\n                @change=\"handleFileSelect\"\n                class=\"upload-file-input\"\n                id=\"csvFileInput\"\n              >\n              <label for=\"csvFileInput\" class=\"upload-file-button\">\n                选择文件\n              </label>\n              <span class=\"upload-file-name\" v-if=\"uploadForm.file\">\n                {{ uploadForm.file.name }}\n              </span>\n              <span class=\"upload-file-placeholder\" v-else>\n                请选择CSV文件\n              </span>\n            </div>\n          </div>\n\n          <div class=\"upload-tips\">\n            <div class=\"upload-tips-title\">上传说明：</div>\n            <div class=\"upload-tips-content\">\n              • 支持CSV格式文件<br>\n              • 文件大小不超过10MB<br>\n              • 请确保数据格式正确\n            </div>\n          </div>\n        </div>\n\n        <div class=\"upload-popup-footer\">\n          <button class=\"upload-cancel-btn\" @click=\"closeUploadPopup\">取消</button>\n          <button class=\"upload-confirm-btn\" @click=\"confirmUpload\">确定</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 设置弹窗 -->\n    <div v-if=\"showSettingsPopup\" class=\"settings-popup\" :style=\"settingsPopupStyle\" @click.stop>\n      <div class=\"settings-popup-content\">\n        <div class=\"chart-types-grid\">\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('bar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"12\" width=\"4\" height=\"9\" fill=\"#1890ff\"/>\n                <rect x=\"10\" y=\"8\" width=\"4\" height=\"13\" fill=\"#1890ff\"/>\n                <rect x=\"17\" y=\"4\" width=\"4\" height=\"17\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">柱状图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('line')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <circle cx=\"3\" cy=\"17\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"11\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"15\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"7\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">折线图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('pie')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 2V12L20.5 7.5C19.5 4.5 16 2 12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M12 12L20.5 16.5C19.5 19.5 16 22 12 22C7 22 3 18 3 12C3 7 7 3 12 3V12Z\" fill=\"#52c41a\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">饼图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('area')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7V21H3V17Z\" fill=\"#1890ff\" opacity=\"0.3\"/>\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">面积图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('scatter')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"5\" cy=\"18\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"16\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"17\" cy=\"8\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"14\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">散点图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('radar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <polygon points=\"12,2 20,8 20,16 12,22 4,16 4,8\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"none\"/>\n                <polygon points=\"12,6 16,9 16,15 12,18 8,15 8,9\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"#1890ff\" opacity=\"0.3\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">雷达图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('gauge')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <path d=\"M12 12L16 8\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">仪表盘</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('funnel')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M6 4H18L16 8H8L6 4Z\" fill=\"#1890ff\"/>\n                <path d=\"M8 8H16L14 12H10L8 8Z\" fill=\"#52c41a\"/>\n                <path d=\"M10 12H14L13 16H11L10 12Z\" fill=\"#faad14\"/>\n                <path d=\"M11 16H13V20H11V16Z\" fill=\"#f5222d\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">漏斗图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('heatmap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"3\" width=\"4\" height=\"4\" fill=\"#1890ff\"/>\n                <rect x=\"8\" y=\"3\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"13\" y=\"3\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"18\" y=\"3\" width=\"3\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"3\" y=\"8\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"8\" y=\"8\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"13\" y=\"8\" width=\"4\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"18\" y=\"8\" width=\"3\" height=\"4\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">热力图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('treemap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"2\" y=\"2\" width=\"10\" height=\"8\" fill=\"#1890ff\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"2\" width=\"9\" height=\"5\" fill=\"#52c41a\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"8\" width=\"9\" height=\"3\" fill=\"#faad14\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"2\" y=\"11\" width=\"6\" height=\"11\" fill=\"#f5222d\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"9\" y=\"11\" width=\"13\" height=\"11\" fill=\"#722ed1\" stroke=\"#fff\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">矩形树图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sunburst')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"none\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"6\" fill=\"none\" stroke=\"#52c41a\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#faad14\"/>\n                <path d=\"M12 2L14 6L12 6L10 6L12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M22 12L18 14L18 12L18 10L22 12Z\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">旭日图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sankey')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 6C2 6 8 6 12 10C16 14 22 14 22 14\" stroke=\"#1890ff\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 12C2 12 8 12 12 12C16 12 22 12 22 12\" stroke=\"#52c41a\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 18C2 18 8 18 12 14C16 10 22 10 22 10\" stroke=\"#faad14\" stroke-width=\"3\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">桑基图</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts';\nimport { getHistoryList } from '@/api/history'\nimport { getTopQuestions } from '@/api/top_questions'\nimport { addHistoryQuery } from '@/api/history_add'\n\nexport default {\n  name: \"DataSearch\",\n  data() {\n    return {\n      // 搜索表单数据\n      searchForm: {\n        keyword: '门店 营业额 前十 门店 营业额',\n        dataType: 'store',\n        store: 'all',\n        time: '2024'\n      },\n      // 筛选弹窗相关\n      showFilterPopup: false,\n      filterPopupStyle: {},\n      filterSearchQuery: '',\n      activeTab: '维度',\n      // 搜索历史弹窗相关\n      showSearchPopup: false,\n      historyList: [],\n      recommendList: [],\n      // 更多操作弹窗\n      showMorePopup: false,\n      morePopupStyle: {},\n      // 分享弹窗相关\n      showSharePopup: false,\n      embedEnabled: true,\n      shareLink: 'https://dwz.cn/jzwMdMh',\n\n      // 卡片提醒弹窗\n      showReminderPopup: false,\n      reminderForm: {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      },\n      // 上传CSV弹窗\n      showUploadPopup: false,\n      uploadForm: {\n        reportName: '',\n        description: '',\n        file: null\n      },\n      // 图表选择弹窗\n      showChartSelector: false,\n      selectedChartType: 'bar',\n      // 设置弹窗\n      showSettingsPopup: false,\n      settingsPopupStyle: {},\n      // 图表实例\n      storeRevenueChart: null,\n      cloudRevenueChart: null,\n      // 刷新间隔\n      refreshInterval: '0',\n      // 智能助手相关\n      chatMessages: [\n        {\n          type: 'assistant',\n          text: '根据当前数据表现？',\n          time: '刚刚'\n        }\n      ],\n      chatInput: '',\n      suggestions: [\n        { icon: '💡', text: '深圳门店营业额最高，有什么成功经验可以分享？' },\n        { icon: '📊', text: '如何提升其他门店的营业额？' },\n        { icon: '🎯', text: '引用数据分析' }\n      ],\n      // 数据加载状态\n      isLoading: false,\n      // 搜索历史\n      searchHistory: [\n        '门店 营业额 前十',\n        '品牌 销售额 排行',\n        '区域 业绩 对比'\n      ],\n      // 门店营业额数据\n      storeRevenueData: {\n        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],\n        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],\n        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],\n        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]\n      },\n      // 云营业额数据\n      cloudRevenueData: {\n        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],\n        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],\n        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],\n        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]\n      }\n    };\n  },\n  watch: {\n    '$route.query.keyword'(newKeyword) {\n      if (newKeyword) {\n        this.searchForm.keyword = newKeyword;\n        // 可以在这里触发新的搜索\n        this.handleSearch();\n      }\n    }\n  },\n  mounted() {\n    // 获取路由参数中的关键词\n    if (this.$route.query.keyword) {\n      this.searchForm.keyword = this.$route.query.keyword;\n    }\n\n    // 获取搜索历史和热门搜索\n    this.fetchSearchData();\n\n    this.initCharts();\n    // 添加点击外部关闭弹窗的事件监听\n    document.addEventListener('click', this.handleClickOutside);\n    // 添加键盘快捷键\n    document.addEventListener('keydown', this.handleKeydown);\n    // 启动实时数据更新\n    this.startRealTimeUpdate();\n    // 显示快捷键提示\n    this.showKeyboardShortcuts();\n  },\n  beforeDestroy() {\n    if (this.storeRevenueChart) {\n      this.storeRevenueChart.dispose();\n    }\n    if (this.cloudRevenueChart) {\n      this.cloudRevenueChart.dispose();\n    }\n    // 移除事件监听\n    document.removeEventListener('click', this.handleClickOutside);\n    document.removeEventListener('keydown', this.handleKeydown);\n  },\n  methods: {\n    /** 初始化图表 */\n    initCharts() {\n      this.$nextTick(() => {\n        this.initStoreRevenueChart();\n        this.initCloudRevenueChart();\n      });\n    },\n\n    /** 初始化门店营业额图表 */\n    initStoreRevenueChart() {\n      this.storeRevenueChart = echarts.init(this.$refs.storeRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.storeRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                if (value >= 10000) {\n                  return (value / 10000).toFixed(1) + '万';\n                }\n                return value;\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.storeRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.storeRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.storeRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.storeRevenueChart.setOption(option);\n\n      // 添加点击事件\n      this.storeRevenueChart.on('click', (params) => {\n        this.handleChartClick(params);\n      });\n    },\n\n    /** 初始化云营业额图表 */\n    initCloudRevenueChart() {\n      this.cloudRevenueChart = echarts.init(this.$refs.cloudRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.cloudRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                return (value / 10000).toFixed(0) + '万';\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.cloudRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.cloudRevenueChart.setOption(option);\n\n      // 添加点击事件\n      this.cloudRevenueChart.on('click', (params) => {\n        this.handleChartClick(params);\n      });\n    },\n\n    // 搜索栏图标按钮方法\n    handleClose() {\n      console.log('关闭搜索');\n      this.$message.info('关闭搜索');\n    },\n\n    handleSearch() {\n      this.showSearchPopup = false;\n      this.handleSearchEnhanced();\n    },\n\n    // 搜索历史相关方法\n    async fetchSearchData() {\n      // 先设置默认数据，确保有内容显示\n      this.historyList = ['门店 营业额 前十', '品牌 销售额 排行', '区域 业绩 对比'];\n      this.recommendList = ['门店营业额前十', '今日汇率与昨日汇率', '上海房产价格走势', '产品销售量统计'];\n\n      try {\n        // 获取搜索历史\n        const historyRes = await getHistoryList({ pageNum: 1, pageSize: 10 });\n        const historyData = (historyRes.items || historyRes.rows || []).map(item => item.content || item.question || '').filter(Boolean);\n        if (historyData.length > 0) {\n          this.historyList = historyData;\n        }\n\n        // 获取热门搜索\n        const topRes = await getTopQuestions({ pageNum: 1, pageSize: 8 });\n        const recommendData = (topRes.items || topRes.rows || []).map(item => item.content || item.question || '').filter(Boolean);\n        if (recommendData.length > 0) {\n          this.recommendList = recommendData;\n        }\n      } catch (error) {\n        console.error('获取搜索数据失败:', error);\n        // 保持默认数据\n      }\n    },\n\n    handleSearchFocus() {\n      console.log('搜索框获得焦点，显示搜索历史弹窗');\n      this.showSearchPopup = true;\n    },\n\n    setSearchKeyword(keyword) {\n      this.searchForm.keyword = keyword;\n      this.showSearchPopup = false;\n      this.handleSearch();\n    },\n\n    handlePrevious() {\n      console.log('上一页');\n      this.$message.info('上一页功能');\n    },\n\n    handleNext() {\n      console.log('下一页');\n      this.$message.info('下一页功能');\n    },\n\n    handleFilter() {\n      console.log('筛选功能');\n      this.showFilterPopup = !this.showFilterPopup;\n\n      if (this.showFilterPopup) {\n        this.$nextTick(() => {\n          this.setFilterPopupPosition();\n        });\n      }\n    },\n\n    // 设置弹窗位置\n    setFilterPopupPosition() {\n      const filterButton = this.$refs.filterButton;\n      if (filterButton) {\n        const rect = filterButton.getBoundingClientRect();\n        // 计算弹窗宽度（280px）和按钮位置，让弹窗右边缘与按钮右边缘对齐\n        const popupWidth = 280;\n        const leftPosition = rect.right - popupWidth;\n\n        this.filterPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 8 + 'px', // 增加一点间距\n          left: Math.max(10, leftPosition) + 'px', // 确保不会超出屏幕左边界\n          zIndex: 1000\n        };\n      }\n    },\n\n    // 关闭筛选弹窗\n    closeFilterPopup() {\n      this.showFilterPopup = false;\n    },\n\n    // 选择筛选项\n    selectFilter(item) {\n      console.log('选择筛选项:', item);\n      this.$message.success(`已选择: ${item}`);\n      this.closeFilterPopup();\n    },\n\n    // 点击外部关闭弹窗\n    handleClickOutside(event) {\n      // 处理搜索历史弹窗\n      if (this.showSearchPopup) {\n        const searchInput = this.$refs.searchInput;\n        const searchPopup = this.$refs.searchHistoryPopup;\n\n        // 如果点击的不是搜索框也不是弹窗内部，则关闭弹窗\n        if (!searchInput?.contains(event.target) && !searchPopup?.contains(event.target)) {\n          this.showSearchPopup = false;\n        }\n      }\n\n      // 处理筛选弹窗\n      if (this.showFilterPopup) {\n        const filterButton = this.$refs.filterButton;\n        const popup = event.target.closest('.filter-popup');\n\n        // 如果点击的不是筛选按钮也不是弹窗内部，则关闭弹窗\n        if (!filterButton?.contains(event.target) && !popup) {\n          this.closeFilterPopup();\n        }\n      }\n\n      // 处理更多操作弹窗\n      if (this.showMorePopup) {\n        const moreButtons = [\n          this.$refs.moreButton1,\n          this.$refs.moreButton2,\n          this.$refs.moreButton3\n        ];\n        const morePopup = event.target.closest('.more-popup');\n\n        // 检查是否点击了任何更多按钮\n        const clickedMoreButton = moreButtons.some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是更多按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedMoreButton && !morePopup) {\n          this.closeMorePopup();\n        }\n      }\n\n      // 处理分享弹窗\n      if (this.showSharePopup) {\n        const sharePopup = event.target.closest('.share-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!sharePopup) {\n          this.closeSharePopup();\n        }\n      }\n\n      // 处理卡片提醒弹窗\n      if (this.showReminderPopup) {\n        const reminderPopup = event.target.closest('.reminder-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!reminderPopup) {\n          this.closeReminderPopup();\n        }\n      }\n\n      // 处理上传CSV弹窗\n      if (this.showUploadPopup) {\n        const uploadPopup = event.target.closest('.upload-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!uploadPopup) {\n          this.closeUploadPopup();\n        }\n      }\n\n      // 处理图表选择器弹窗\n      if (this.showChartSelector) {\n        const chartSelector = event.target.closest('.chart-selector');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!chartSelector) {\n          this.closeChartSelector();\n        }\n      }\n\n      // 处理设置弹窗\n      if (this.showSettingsPopup) {\n        const settingsPopup = event.target.closest('.settings-popup');\n        const settingsButtons = document.querySelectorAll('.action-icon.settings');\n\n        // 检查是否点击了任何设置按钮\n        const clickedSettingsButton = Array.from(settingsButtons).some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是设置按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedSettingsButton && !settingsPopup) {\n          this.closeSettingsPopup();\n        }\n      }\n    },\n\n    // 更多操作相关方法\n    handleMoreClick(event) {\n      this.showMorePopup = true;\n\n      this.$nextTick(() => {\n        this.setMorePopupPosition(event.target);\n      });\n    },\n\n    // 设置更多弹窗位置\n    setMorePopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.morePopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 60 + 'px', // 向左偏移一些，让弹窗居中对齐按钮\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeMorePopup() {\n      this.showMorePopup = false;\n    },\n\n    handleCardReminder() {\n      this.showReminderPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleShareCard() {\n      this.showSharePopup = true;\n      this.closeMorePopup();\n      this.$message.success('分享弹窗已打开');\n    },\n\n    handleSaveCard() {\n      this.$message.loading('正在保存卡片...', 2);\n      setTimeout(() => {\n        this.$message.success('卡片已保存到我的收藏');\n      }, 2000);\n      this.closeMorePopup();\n    },\n\n    handleUploadCSV() {\n      this.showUploadPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleDownloadPNG() {\n      this.exportData('PNG');\n      this.closeMorePopup();\n    },\n\n    // 图表按钮处理方法\n    handleRefresh() {\n      console.log('刷新数据');\n      this.$message.loading('正在刷新数据...', 1);\n      this.refreshChartData();\n    },\n\n    handleDownload() {\n      this.exportData('Excel');\n    },\n\n    handleSettings(event) {\n      console.log('设置');\n      this.showSettingsPopup = true;\n\n      this.$nextTick(() => {\n        this.setSettingsPopupPosition(event.target);\n      });\n    },\n\n    // 分享弹窗相关方法\n    closeSharePopup() {\n      this.showSharePopup = false;\n    },\n\n    copyShareLink() {\n      // 复制链接到剪贴板\n      navigator.clipboard.writeText(this.shareLink).then(() => {\n        this.$message.success('链接已复制到剪贴板');\n      }).catch(() => {\n        // 降级方案\n        const textArea = document.createElement('textarea');\n        textArea.value = this.shareLink;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        this.$message.success('链接已复制到剪贴板');\n      });\n    },\n\n    // 卡片提醒弹窗方法\n    closeReminderPopup() {\n      this.showReminderPopup = false;\n      // 重置表单\n      this.reminderForm = {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      };\n    },\n\n    confirmReminder() {\n      // 验证表单\n      if (!this.reminderForm.cardName) {\n        this.$message.warning('请选择提醒卡片');\n        return;\n      }\n      if (!this.reminderForm.email) {\n        this.$message.warning('请输入邮箱地址');\n        return;\n      }\n\n      // 这里可以添加邮箱格式验证\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(this.reminderForm.email)) {\n        this.$message.warning('请输入正确的邮箱格式');\n        return;\n      }\n\n      console.log('设置卡片提醒:', this.reminderForm);\n      this.$message.success('卡片提醒设置成功！');\n      this.closeReminderPopup();\n    },\n\n    // 上传CSV弹窗方法\n    closeUploadPopup() {\n      this.showUploadPopup = false;\n      // 重置表单\n      this.uploadForm = {\n        reportName: '',\n        description: '',\n        file: null\n      };\n    },\n\n    handleFileSelect(event) {\n      const file = event.target.files[0];\n      if (file) {\n        // 检查文件类型\n        if (!file.name.toLowerCase().endsWith('.csv')) {\n          this.$message.warning('请选择CSV格式的文件');\n          event.target.value = '';\n          return;\n        }\n        this.uploadForm.file = file;\n      }\n    },\n\n    confirmUpload() {\n      // 验证表单\n      if (!this.uploadForm.reportName.trim()) {\n        this.$message.warning('请输入报告名称');\n        return;\n      }\n      if (!this.uploadForm.file) {\n        this.$message.warning('请选择要上传的CSV文件');\n        return;\n      }\n\n      console.log('上传CSV:', this.uploadForm);\n      this.$message.success('CSV文件上传成功！');\n      this.closeUploadPopup();\n    },\n\n    // 图表选择器相关方法\n    openChartSelector() {\n      this.showChartSelector = true;\n    },\n\n    closeChartSelector() {\n      this.showChartSelector = false;\n    },\n\n    selectChartType(chartType) {\n      this.selectedChartType = chartType;\n      console.log('选择图表类型:', chartType);\n      this.closeChartSelector();\n    },\n\n    // 设置弹窗相关方法\n    setSettingsPopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.settingsPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 100 + 'px',\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeSettingsPopup() {\n      this.showSettingsPopup = false;\n    },\n\n    selectChartIcon(chartType) {\n      console.log('选择图表类型:', chartType);\n      this.$message.success(`已选择图表类型: ${chartType}`);\n      this.closeSettingsPopup();\n    },\n\n    // 智能助手相关方法\n    sendChatMessage() {\n      if (!this.chatInput.trim()) return;\n\n      // 添加用户消息\n      this.chatMessages.push({\n        type: 'user',\n        text: this.chatInput,\n        time: this.getCurrentTime()\n      });\n\n      const userMessage = this.chatInput;\n      this.chatInput = '';\n\n      // 模拟AI回复\n      setTimeout(() => {\n        this.generateAIResponse(userMessage);\n      }, 1000);\n    },\n\n    generateAIResponse(userMessage) {\n      let response = '';\n\n      if (userMessage.includes('深圳') || userMessage.includes('成功经验')) {\n        response = '深圳门店表现优异，营业额达到2.134万元，主要成功因素包括：\\n\\n1. 地理位置优势 - 位于核心商圈\\n2. 客户群体消费能力强\\n3. 产品组合策略精准\\n4. 服务质量持续优化';\n      } else if (userMessage.includes('提升') || userMessage.includes('营业额')) {\n        response = '基于深圳门店的成功经验，建议其他门店：\\n\\n• 学习深圳门店的运营模式\\n• 根据当地市场调整产品结构\\n• 加强员工培训提升服务水平\\n• 优化店面布局和客户体验\\n• 制定针对性的营销策略';\n      } else if (userMessage.includes('数据分析') || userMessage.includes('分析')) {\n        response = '根据当前数据分析显示：\\n\\n• 华南地区（深圳、广州）表现最佳\\n• 华东地区存在下降趋势，需要关注\\n• 整体营业额同比增长4.73%\\n• 建议重点关注排名靠后的门店';\n      } else {\n        response = '感谢您的提问！我正在分析相关数据，为您提供专业的建议。您可以询问关于门店营业额、运营策略或数据分析的问题。';\n      }\n\n      this.chatMessages.push({\n        type: 'assistant',\n        text: response,\n        time: this.getCurrentTime()\n      });\n    },\n\n    handleSuggestionClick(suggestion) {\n      this.chatInput = suggestion.text;\n      this.sendChatMessage();\n    },\n\n    getCurrentTime() {\n      const now = new Date();\n      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;\n    },\n\n    // 搜索功能增强\n    handleSearchEnhanced() {\n      this.isLoading = true;\n      this.$message.loading('正在搜索数据...', 2);\n\n      // 模拟搜索延迟\n      setTimeout(() => {\n        this.isLoading = false;\n        this.$message.success('搜索完成！');\n\n        // 添加到搜索历史\n        if (this.searchForm.keyword && !this.searchHistory.includes(this.searchForm.keyword)) {\n          this.searchHistory.unshift(this.searchForm.keyword);\n          if (this.searchHistory.length > 5) {\n            this.searchHistory.pop();\n          }\n        }\n\n        // 刷新图表数据\n        this.refreshChartData();\n      }, 2000);\n    },\n\n    // 刷新图表数据\n    refreshChartData() {\n      // 模拟数据变化\n      this.storeRevenueData.revenue = this.storeRevenueData.revenue.map(val =>\n        val + Math.floor(Math.random() * 2000 - 1000)\n      );\n      this.storeRevenueData.profit = this.storeRevenueData.profit.map(val =>\n        val + Math.floor(Math.random() * 1500 - 750)\n      );\n      this.storeRevenueData.growthRate = this.storeRevenueData.growthRate.map(val =>\n        +(val + Math.random() * 2 - 1).toFixed(2)\n      );\n\n      // 重新渲染图表\n      this.initCharts();\n    },\n\n    // 数据导出功能\n    exportData(format) {\n      this.$message.loading(`正在导出${format}格式数据...`, 2);\n\n      setTimeout(() => {\n        this.$message.success(`${format}数据导出成功！`);\n\n        // 模拟下载\n        const data = {\n          门店营业额: this.storeRevenueData,\n          品牌营业额: this.cloudRevenueData,\n          导出时间: new Date().toLocaleString()\n        };\n\n        console.log('导出数据:', data);\n      }, 2000);\n    },\n\n    // 实时数据更新\n    startRealTimeUpdate() {\n      if (this.refreshInterval !== '0') {\n        const interval = parseInt(this.refreshInterval) * 1000;\n\n        setInterval(() => {\n          this.refreshChartData();\n          this.$message.info('数据已自动更新');\n        }, interval);\n\n        this.$message.success(`已开启${this.refreshInterval}秒自动刷新`);\n      }\n    },\n\n    // 键盘快捷键处理\n    handleKeydown(event) {\n      // Ctrl + Enter 执行搜索\n      if (event.ctrlKey && event.key === 'Enter') {\n        event.preventDefault();\n        this.handleSearch();\n      }\n      // F5 刷新数据\n      if (event.key === 'F5') {\n        event.preventDefault();\n        this.handleRefresh();\n      }\n      // Ctrl + S 保存卡片\n      if (event.ctrlKey && event.key === 's') {\n        event.preventDefault();\n        this.handleSaveCard();\n      }\n      // Ctrl + D 下载数据\n      if (event.ctrlKey && event.key === 'd') {\n        event.preventDefault();\n        this.handleDownload();\n      }\n      // Escape 关闭所有弹窗\n      if (event.key === 'Escape') {\n        this.closeAllPopups();\n      }\n    },\n\n    // 关闭所有弹窗\n    closeAllPopups() {\n      this.showFilterPopup = false;\n      this.showMorePopup = false;\n      this.showSharePopup = false;\n      this.showReminderPopup = false;\n      this.showUploadPopup = false;\n      this.showChartSelector = false;\n      this.showSettingsPopup = false;\n    },\n\n    // 图表交互增强\n    handleChartClick(params) {\n      console.log('图表点击事件:', params);\n      this.$message.info(`点击了: ${params.name} - ${params.value}`);\n    },\n\n    // 数据筛选功能\n    filterDataByCategory(category) {\n      this.$message.loading('正在筛选数据...', 1);\n\n      setTimeout(() => {\n        // 模拟数据筛选\n        if (category === '华南地区') {\n          this.storeRevenueData.categories = ['深圳', '广州'];\n          this.storeRevenueData.revenue = [21340, 16200];\n          this.storeRevenueData.profit = [22410, 18940];\n          this.storeRevenueData.growthRate = [11.39, 9.04];\n        } else if (category === '华东地区') {\n          this.storeRevenueData.categories = ['上海', '杭州', '南京'];\n          this.storeRevenueData.revenue = [8100, 7610, 6200];\n          this.storeRevenueData.profit = [12400, 7600, 6420];\n          this.storeRevenueData.growthRate = [7.60, 5.37, 5.04];\n        }\n\n        this.initCharts();\n        this.$message.success(`已筛选${category}数据`);\n      }, 1000);\n    },\n\n    // 重置数据\n    resetData() {\n      this.storeRevenueData = {\n        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],\n        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],\n        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],\n        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]\n      };\n\n      this.cloudRevenueData = {\n        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],\n        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],\n        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],\n        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]\n      };\n\n      this.initCharts();\n      this.$message.success('数据已重置');\n    },\n\n    // 显示快捷键提示\n    showKeyboardShortcuts() {\n      setTimeout(() => {\n        this.$message.info('💡 快捷键提示：Ctrl+Enter搜索，F5刷新，Ctrl+S保存，Ctrl+D下载，ESC关闭弹窗', 5);\n      }, 3000);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n// 搜索栏样式\n.search-container {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 4px;\n  padding: 12px 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n\n  .search-form {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .search-input-wrapper {\n      flex: 1;\n      position: relative; // 添加相对定位，使弹窗能正确定位\n\n      .search-input {\n        width: 100%;\n        height: 32px;\n        padding: 4px 12px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        font-size: 14px;\n        color: #333;\n        background: #fff;\n        outline: none;\n        transition: border-color 0.3s;\n\n        &:focus {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        &::placeholder {\n          color: #999;\n        }\n      }\n    }\n\n    .search-buttons {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .btn-icon {\n        width: 32px;\n        height: 32px;\n        padding: 6px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        background: #fff;\n        cursor: pointer;\n        transition: all 0.3s;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n\n        &:hover {\n          background: #f5f5f5;\n          border-color: #40a9ff;\n        }\n\n        .close-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA0TDQgMTJMMTIgMTJMMTIgNFoiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;\n          background-size: contain;\n        }\n\n        .search-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTJDMy42ODYyOSAxMiAxIDkuMzEzNzEgMSA2QzEgMi42ODYyOSAzLjY4NjI5IDAgNyAwQzEwLjMxMzcgMCAxMyAyLjY4NjI5IDEzIDZDMTMgOS4zMTM3MSAxMC4zMTM3IDEyIDcgMTJaTTcgMTFDOS43NjE0MiAxMSAxMiA4Ljc2MTQyIDEyIDZDMTIgMy4yMzg1OCA5Ljc2MTQyIDEgNyAxQzQuMjM4NTggMSAyIDMuMjM4NTggMiA2QzIgOC43NjE0MiA0LjIzODU4IDExIDcgMTFaIiBmaWxsPSIjNjY2Ii8+CjxwYXRoIGQ9Ik0xMSAxMUwxNSAxNSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n          background-size: contain;\n        }\n\n        .left-arrow-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDEyTDYgOEwxMCA0IiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;\n          background-size: contain;\n        }\n\n        .right-arrow-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgNEwxMCA4TDYgMTIiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;\n          background-size: contain;\n        }\n\n        .filter-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgM0gxNEwxMCA3VjEzTDYgMTFWN0wyIDNaIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;\n          background-size: contain;\n        }\n      }\n    }\n  }\n}\n\n// 筛选弹窗样式\n.filter-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  width: 280px;\n  max-height: 450px;\n  overflow: hidden;\n\n  .popup-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 12px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n    border-radius: 6px 6px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .popup-close {\n      background: none;\n      border: none;\n      font-size: 16px;\n      color: #8c8c8c;\n      cursor: pointer;\n      padding: 0;\n      width: 20px;\n      height: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 2px;\n\n      &:hover {\n        background: #f5f5f5;\n        color: #595959;\n      }\n    }\n  }\n\n  .popup-search {\n    padding: 12px;\n    border-bottom: 1px solid #f0f0f0;\n    position: relative;\n\n    .search-input {\n      width: 100%;\n      padding: 6px 12px 6px 32px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      outline: none;\n      background: #ffffff;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 20px;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 14px;\n      height: 14px;\n      background: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"%23999\" stroke-width=\"2\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/></svg>') no-repeat center;\n      background-size: contain;\n    }\n  }\n\n  .popup-tabs {\n    display: flex;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n\n    .tab-item {\n      flex: 1;\n      padding: 8px 12px;\n      text-align: center;\n      font-size: 13px;\n      color: #595959;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 2px solid transparent;\n\n      &:hover {\n        color: #1890ff;\n        background: #f5f5f5;\n      }\n\n      &.active {\n        color: #1890ff;\n        background: #ffffff;\n        border-bottom-color: #1890ff;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .popup-content {\n    padding: 0;\n    max-height: 300px;\n    overflow-y: auto;\n\n    .tab-content {\n      .filter-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n\n        &:active {\n          background: #e6f7ff;\n          color: #1890ff;\n        }\n\n        .arrow-icon {\n          font-size: 12px;\n          color: #8c8c8c;\n          font-style: normal;\n        }\n      }\n\n      .time-units-row {\n        padding: 10px 16px;\n        border-bottom: 1px solid #f5f5f5;\n        display: flex;\n        gap: 16px;\n\n        .time-unit {\n          font-size: 14px;\n          color: #262626;\n          cursor: pointer;\n          transition: all 0.2s;\n          padding: 4px 8px;\n          border-radius: 4px;\n\n          &:hover {\n            background: #f0f8ff;\n            color: #1890ff;\n          }\n        }\n      }\n\n      .time-item {\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n      }\n    }\n  }\n}\n\n.search-form {\n  .form-row {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    flex-wrap: wrap;\n  }\n\n  .form-group {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .form-label {\n      font-size: 13px;\n      color: #595959;\n      font-weight: 400;\n      white-space: nowrap;\n      margin: 0;\n    }\n\n    .form-input {\n      height: 28px;\n      padding: 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff;\n      outline: none;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &.keyword-input {\n        width: 200px;\n      }\n\n      &.time-input {\n        width: 60px;\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n        font-size: 13px;\n      }\n    }\n\n    .form-select {\n      height: 28px;\n      padding: 0 20px 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\") no-repeat right 6px center/12px 12px;\n      outline: none;\n      appearance: none;\n      cursor: pointer;\n      min-width: 80px;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n    }\n  }\n\n  .form-buttons {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-left: auto;\n\n    .btn {\n      height: 28px;\n      padding: 0 12px;\n      border-radius: 4px;\n      font-size: 13px;\n      font-weight: 400;\n      border: 1px solid;\n      cursor: pointer;\n      outline: none;\n      transition: all 0.2s;\n      white-space: nowrap;\n\n      &.btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n        color: #ffffff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n\n      &.btn-default {\n        background: #ffffff;\n        border-color: #d9d9d9;\n        color: #595959;\n\n        &:hover {\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n      }\n\n      &.btn-success {\n        background: #52c41a;\n        border-color: #52c41a;\n        color: #ffffff;\n\n        &:hover {\n          background: #73d13d;\n          border-color: #73d13d;\n        }\n      }\n    }\n  }\n}\n\n// 顶部区域：门店营业额前十的 + 智能助手\n.top-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 中间区域：营业额同比 单独一行\n.middle-section {\n  width: 100%;\n}\n\n// 底部区域：品牌门店营业额前十的 + 智能助手\n.bottom-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 主图表区域\n.main-chart {\n  flex: 2.5; // 图表占据更多空间，比例约为 2.5:1\n  min-width: 0;\n}\n\n// 智能助手面板\n.assistant-panel {\n  width: 320px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  flex-shrink: 0;\n  display: flex;\n  flex-direction: column;\n  height: 400px; // 减少智能助手面板的高度\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    flex-shrink: 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .send-btn {\n        width: 28px;\n        height: 28px;\n        border-radius: 50%;\n        background: #1890ff;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.2s;\n\n        &:hover {\n          background: #40a9ff;\n          transform: scale(1.05);\n        }\n\n        &:active {\n          background: #096dd9;\n          transform: scale(0.95);\n        }\n      }\n\n      .panel-close {\n        cursor: pointer;\n        font-size: 16px;\n        color: #8c8c8c;\n\n        &:hover {\n          color: #262626;\n        }\n      }\n    }\n  }\n\n  .panel-content {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    overflow: hidden;\n\n    .chat-messages {\n      flex: 1;\n      padding: 16px;\n      overflow-y: auto;\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n\n      .message-item {\n        display: flex;\n        align-items: flex-start;\n        gap: 8px;\n\n        &.user-message {\n          flex-direction: row-reverse;\n\n          .message-content {\n            background: #1890ff;\n            color: white;\n            border-radius: 12px 12px 4px 12px;\n            max-width: 70%;\n          }\n        }\n\n        &.assistant-message {\n          .message-avatar {\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            background: #f0f8ff;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n\n            .avatar-circle {\n              width: 24px;\n              height: 24px;\n              border-radius: 50%;\n              background: #e6f7ff;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n            }\n          }\n\n          .message-content {\n            background: #f5f5f5;\n            color: #262626;\n            border-radius: 12px 12px 12px 4px;\n            max-width: 70%;\n          }\n        }\n\n        .message-content {\n          padding: 8px 12px;\n\n          .message-text {\n            font-size: 13px;\n            line-height: 1.4;\n            margin-bottom: 4px;\n          }\n\n          .message-time {\n            font-size: 11px;\n            opacity: 0.7;\n          }\n        }\n      }\n\n      .suggestion-item {\n        display: flex;\n        align-items: center;\n        padding: 8px 12px;\n        margin: 4px 0;\n        background: #f8f9fa;\n        border-radius: 6px;\n        cursor: pointer;\n        transition: background-color 0.2s;\n\n        &:hover {\n          background: #e9ecef;\n        }\n\n        .suggestion-icon {\n          margin-right: 8px;\n          font-size: 14px;\n        }\n\n        .suggestion-text {\n          font-size: 13px;\n          color: #666;\n        }\n      }\n    }\n\n    .input-area {\n      padding: 12px 16px;\n      border-top: 1px solid #f0f0f0;\n      background: #fafbfc;\n      flex-shrink: 0;\n\n      .input-wrapper {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        background: white;\n        border: 1px solid #d9d9d9;\n        border-radius: 20px;\n        padding: 6px 12px;\n\n        &:focus-within {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        .chat-input {\n          flex: 1;\n          border: none;\n          outline: none;\n          font-size: 13px;\n          padding: 4px 0;\n          background: transparent;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n        }\n\n        .input-send-btn {\n          width: 24px;\n          height: 24px;\n          border: none;\n          background: transparent;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n          transition: all 0.2s;\n\n          &:hover {\n            background: #f0f8ff;\n          }\n\n          &:active {\n            background: #e6f7ff;\n          }\n        }\n      }\n    }\n  }\n}\n\n.chart-card, .value-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.chart-header, .value-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.chart-title, .value-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n\n  .chart-icon {\n    width: 16px;\n    height: 16px;\n    background: #1890ff;\n    border-radius: 2px;\n  }\n\n  .help-icon {\n    width: 16px;\n    height: 16px;\n    background: #d9d9d9;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    color: #ffffff;\n    cursor: pointer;\n  }\n}\n\n.chart-meta, .value-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 12px;\n  color: #8c8c8c;\n\n  .chart-date, .value-date {\n    color: #595959;\n  }\n\n  .chart-type, .value-type {\n    background: #f0f0f0;\n    padding: 2px 6px;\n    border-radius: 2px;\n  }\n\n  .chart-source {\n    color: #1890ff;\n  }\n}\n\n.chart-actions, .value-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  .action-icon {\n    width: 16px;\n    height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    background-size: 12px 12px;\n    background-position: center;\n    background-repeat: no-repeat;\n    transition: all 0.2s;\n\n    &.refresh {\n      background-color: #52c41a;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #73d13d;\n      }\n    }\n\n    &.download {\n      background-color: #1890ff;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #40a9ff;\n      }\n    }\n\n    &.more {\n      background-color: #8c8c8c;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #a6a6a6;\n      }\n    }\n\n    &.settings {\n      background-color: #722ed1;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3crect x='3' y='12' width='4' height='9'/%3e%3crect x='10' y='8' width='4' height='13'/%3e%3crect x='17' y='4' width='4' height='17'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #9254de;\n      }\n    }\n\n    &.close {\n      background-color: #ff4d4f;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #ff7875;\n      }\n    }\n  }\n\n  .chart-status {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-left: 8px;\n  }\n}\n\n.chart-content {\n  padding: 20px;\n}\n\n.chart-legend {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 16px;\n  font-size: 12px;\n\n  .legend-item {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .legend-color {\n      width: 12px;\n      height: 12px;\n      border-radius: 2px;\n\n      &.blue {\n        background: #5B8FF9;\n      }\n\n      &.yellow {\n        background: #FFD666;\n      }\n\n      &.line {\n        background: #FF6B6B;\n        border-radius: 50%;\n        width: 8px;\n        height: 8px;\n      }\n    }\n  }\n}\n\n.chart-wrapper {\n  width: 100%;\n  height: 300px;\n}\n\n.chart {\n  width: 100%;\n  height: 100%;\n}\n\n.value-content {\n  padding: 20px;\n}\n\n.value-main {\n  .value-label {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .value-number {\n    font-size: 36px;\n    font-weight: bold;\n    color: #262626;\n    line-height: 1;\n    margin-bottom: 12px;\n\n    .value-unit {\n      font-size: 18px;\n      color: #8c8c8c;\n      margin-left: 4px;\n    }\n  }\n\n  .value-change {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    font-size: 12px;\n\n    .change-text {\n      color: #8c8c8c;\n    }\n\n    .change-value {\n      &.positive {\n        color: #52c41a;\n      }\n\n      &.negative {\n        color: #ff4d4f;\n      }\n    }\n\n    .change-arrow {\n      width: 0;\n      height: 0;\n\n      &.up {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-bottom: 6px solid #52c41a;\n      }\n\n      &.down {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-top: 6px solid #ff4d4f;\n      }\n    }\n  }\n}\n\n.control-panel {\n  position: fixed;\n  right: 20px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 280px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    border-radius: 8px 8px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .panel-close {\n      cursor: pointer;\n      font-size: 16px;\n      color: #8c8c8c;\n    }\n  }\n\n  .panel-content {\n    padding: 20px;\n\n    .panel-section {\n      h4 {\n        margin: 0 0 12px 0;\n        font-size: 14px;\n        color: #262626;\n      }\n\n      .setting-item {\n        margin-bottom: 16px;\n\n        label {\n          display: block;\n          margin-bottom: 6px;\n          font-size: 12px;\n          color: #8c8c8c;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .search-container {\n    .search-form {\n      .form-row {\n        gap: 12px;\n      }\n\n      .form-group {\n        .form-input.keyword-input {\n          width: 160px;\n        }\n      }\n\n      .form-buttons {\n        margin-left: 0;\n        margin-top: 8px;\n        width: 100%;\n        justify-content: flex-start;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1; // 智能助手面板在移动端显示在图表上方\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-container {\n    padding: 10px;\n    gap: 15px;\n  }\n\n  .search-container {\n    .search-form {\n      .form-row {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 12px;\n      }\n\n      .form-group {\n        width: 100%;\n\n        .form-input {\n          flex: 1;\n          min-width: 120px;\n\n          &.keyword-input {\n            width: 100%;\n          }\n        }\n\n        .form-select {\n          flex: 1;\n          min-width: 120px;\n        }\n      }\n\n      .form-buttons {\n        width: 100%;\n        justify-content: center;\n        margin-top: 12px;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1;\n\n    .panel-content {\n      padding: 15px;\n\n      .assistant-item {\n        padding: 10px 0;\n\n        .assistant-icon {\n          width: 28px;\n          height: 28px;\n          font-size: 16px;\n        }\n\n        .assistant-text {\n          font-size: 13px;\n        }\n      }\n    }\n  }\n\n  .chart-header, .value-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n    padding: 12px 16px;\n  }\n\n  .chart-meta, .value-meta {\n    order: 1;\n  }\n\n  .chart-actions, .value-actions {\n    order: 2;\n    align-self: flex-end;\n  }\n\n  .chart-content {\n    padding: 16px;\n  }\n\n  .value-content {\n    padding: 16px;\n  }\n\n  .chart-wrapper {\n    height: 250px;\n  }\n\n  .value-number {\n    font-size: 28px !important;\n\n    .value-unit {\n      font-size: 14px !important;\n    }\n  }\n}\n\n// 更多操作弹窗样式\n.more-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  min-width: 160px;\n  overflow: hidden;\n  position: fixed;\n  z-index: 2000;\n\n  .more-popup-content {\n    .more-action-item {\n      padding: 12px 16px;\n      font-size: 14px;\n      color: #262626;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 1px solid #f5f5f5;\n      line-height: 1.4;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover {\n        background: #f0f8ff;\n        color: #1890ff;\n      }\n\n      &:active {\n        background: #e6f7ff;\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n// 分享弹窗样式\n.share-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-popup {\n  background: white;\n  border-radius: 8px;\n  width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.share-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.share-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.share-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.share-popup-content {\n  padding: 20px;\n}\n\n.share-description {\n  color: #666;\n  font-size: 14px;\n  margin-bottom: 20px;\n  line-height: 1.5;\n}\n\n.share-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.share-option-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.share-toggle {\n  position: relative;\n}\n\n.toggle-input {\n  display: none;\n}\n\n.toggle-label {\n  display: block;\n  width: 44px;\n  height: 24px;\n  background: #ddd;\n  border-radius: 12px;\n  cursor: pointer;\n  position: relative;\n  transition: background 0.3s;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 2px;\n    left: 2px;\n    width: 20px;\n    height: 20px;\n    background: white;\n    border-radius: 50%;\n    transition: transform 0.3s;\n  }\n}\n\n.toggle-input:checked + .toggle-label {\n  background: #1890ff;\n\n  &::after {\n    transform: translateX(20px);\n  }\n}\n\n.share-link-section {\n  display: flex;\n  gap: 8px;\n}\n\n.share-link-input {\n  flex: 1;\n  height: 36px;\n  padding: 0 12px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n  background: #f5f5f5;\n  outline: none;\n}\n\n.copy-link-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 卡片提醒弹窗样式\n.reminder-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 10000 !important;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.reminder-popup {\n  background: white;\n  border-radius: 8px;\n  width: 520px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  z-index: 10001 !important;\n  position: relative;\n}\n\n.reminder-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.reminder-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.reminder-popup-content {\n  padding: 20px;\n}\n\n.reminder-form-item {\n  margin-bottom: 16px;\n\n  .reminder-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .reminder-select {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    cursor: pointer;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n  }\n\n  .reminder-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .reminder-change-section {\n    display: flex;\n    gap: 12px;\n\n    .reminder-select-small {\n      flex: 1;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n      cursor: pointer;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n  }\n\n  .reminder-threshold-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .reminder-number-input {\n      width: 120px;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n\n    .reminder-unit {\n      font-size: 14px;\n      color: #666;\n    }\n\n    .reminder-checkbox-section {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      margin-left: auto;\n\n      .reminder-checkbox {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-checkbox-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n\n  .reminder-method-section {\n    display: flex;\n    gap: 20px;\n\n    .reminder-radio-item {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n\n      .reminder-radio {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-radio-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n}\n\n.reminder-description {\n  font-size: 13px;\n  color: #999;\n  margin: 16px 0;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  line-height: 1.5;\n}\n\n.reminder-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.reminder-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 上传CSV弹窗样式\n.upload-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-popup {\n  background: white;\n  border-radius: 8px;\n  width: 480px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.upload-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.upload-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.upload-popup-content {\n  padding: 20px;\n}\n\n.upload-form-item {\n  margin-bottom: 16px;\n\n  .upload-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .upload-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-textarea {\n    width: 100%;\n    padding: 8px 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    resize: vertical;\n    min-height: 80px;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-file-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .upload-file-input {\n      display: none;\n    }\n\n    .upload-file-button {\n      height: 36px;\n      padding: 0 16px;\n      background: #1890ff;\n      border: none;\n      border-radius: 4px;\n      color: white;\n      font-size: 14px;\n      cursor: pointer;\n      transition: background 0.3s;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:hover {\n        background: #40a9ff;\n      }\n\n      &:active {\n        background: #096dd9;\n      }\n    }\n\n    .upload-file-name {\n      font-size: 14px;\n      color: #333;\n      flex: 1;\n    }\n\n    .upload-file-placeholder {\n      font-size: 14px;\n      color: #bfbfbf;\n      flex: 1;\n    }\n  }\n}\n\n.upload-tips {\n  margin-top: 20px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n\n  .upload-tips-title {\n    font-size: 14px;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 8px;\n  }\n\n  .upload-tips-content {\n    font-size: 13px;\n    color: #666;\n    line-height: 1.6;\n  }\n}\n\n.upload-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.upload-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 设置弹窗样式\n.settings-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  padding: 8px;\n  overflow: hidden;\n\n  .settings-popup-content {\n    .chart-types-grid {\n      display: grid;\n      grid-template-columns: repeat(4, 1fr);\n      gap: 8px;\n\n      .chart-type-item {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 8px 4px;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-radius: 4px;\n        min-height: 60px;\n\n        &:hover {\n          background: #f0f8ff;\n          transform: translateY(-2px);\n        }\n\n        &:active {\n          background: #e6f7ff;\n          transform: translateY(0);\n        }\n\n        .chart-icon {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 4px;\n\n          svg {\n            width: 20px;\n            height: 20px;\n            transition: all 0.2s;\n          }\n        }\n\n        .chart-label {\n          font-size: 12px;\n          color: #262626;\n          text-align: center;\n          line-height: 1.2;\n          white-space: nowrap;\n        }\n\n        &:hover {\n          .chart-icon svg {\n            transform: scale(1.1);\n          }\n\n          .chart-label {\n            color: #1890ff;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 搜索历史弹窗样式\n.search-history-popup {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  max-height: 400px;\n  overflow-y: auto;\n\n  .search-history-popup-title {\n    padding: 12px 16px 8px;\n    font-size: 14px;\n    font-weight: 500;\n    color: #262626;\n    border-bottom: 1px solid #f0f0f0;\n\n    &.hot {\n      border-top: 1px solid #f0f0f0;\n      border-bottom: none;\n    }\n  }\n\n  .search-history-popup-list {\n    padding: 8px 0;\n\n    .search-history-popup-item {\n      display: flex;\n      align-items: center;\n      padding: 8px 16px;\n      cursor: pointer;\n      transition: background 0.2s;\n      font-size: 14px;\n      color: #595959;\n\n      &:hover {\n        background: #f5f5f5;\n      }\n\n      .history-icon,\n      .hot-icon {\n        width: 16px;\n        height: 16px;\n        margin-right: 8px;\n        flex-shrink: 0;\n      }\n\n      .history-icon {\n        background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMUMxMS44NjYgMSAxNSA0LjEzNCAxNSA4QzE1IDExLjg2NiAxMS44NjYgMTUgOCAxNUM0LjEzNCAxNSAxIDExLjg2NiAxIDhDMSA0LjEzNCA0LjEzNCAxIDggMVoiIHN0cm9rZT0iIzU5NTk1OSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggZD0iTTggNFY4TDEwLjUgMTAuNSIgc3Ryb2tlPSIjNTk1OTU5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n        background-size: contain;\n      }\n\n      .hot-icon {\n        background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkwxMCA2SDE0TDExIDlMMTIgMTNMOCAxMUw0IDEzTDUgOUwyIDZINkw4IDJaIiBmaWxsPSIjRkY0RDRGIi8+Cjwvc3ZnPgo=') no-repeat center;\n        background-size: contain;\n      }\n\n      &.hot {\n        color: #ff4d4f;\n      }\n    }\n  }\n}\n</style>\n"]}]}