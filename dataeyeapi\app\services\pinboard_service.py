#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from app.dao.pinboard_dao import PinboardDao
from app.models.pinboard_models import (
    Pinboard, PinboardTag, PinboardTagRelation, AccountProject,
    PinboardStar, PinboardShare, UserNotificationSettings,
    PinboardUpdate, PinboardReminder, PinboardReminderLog, PinboardDeleteRecord
)
from app.schemas.pinboard_schemas import (
    PinboardCreateRequest, PinboardItem, TagItem,
    CollaborateReportItem, SharedReportItem, FollowedReportItem,
    ReminderItem, ReminderCreateRequest, TrashReportItem
)
from utils.log_util import logger
from datetime import datetime, timedelta
import uuid

class PinboardService:
    """Pinboard服务层"""

    @staticmethod
    async def get_user_pinboards(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        project_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取用户的Pinboard列表
        """
        try:
            # 调用DAO层获取数据
            result = await PinboardDao.get_pinboards_with_pagination(
                user_id=user_id,
                page=page,
                size=size,
                search=search,
                project_id=project_id
            )

            # 转换数据格式
            items = []
            for pinboard_data in result['items']:
                pinboard = pinboard_data['pinboard']
                tags = pinboard_data.get('tags', [])
                owner_info = pinboard_data.get('owner', {})

                # 判断是否为新建（3天内创建）
                is_new = (datetime.now() - pinboard.created_at).days <= 3

                # 格式化更新时间
                update_time = PinboardService._format_time(pinboard.updated_at)

                item = PinboardItem(
                    uid=pinboard.uid,
                    name=pinboard.name,
                    description=pinboard.description,
                    owner_id=pinboard.owner_id,
                    owner_name=owner_info.get('user_name', '未知用户'),
                    owner_avatar=owner_info.get('avatar', ''),
                    last_editor=pinboard.update_by or owner_info.get('user_name', ''),
                    last_editor_avatar='',
                    is_template=bool(pinboard.is_template),
                    is_shared=bool(pinboard.is_shared),
                    share_type=pinboard.share_type,
                    is_new=is_new,
                    tags=[{'id': tag.id, 'name': tag.name, 'color': tag.color} for tag in tags],
                    created_at=pinboard.created_at,
                    updated_at=pinboard.updated_at,
                    update_time=update_time
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size
            }

        except Exception as e:
            logger.error(f"获取用户Pinboard列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_my_pinboards(
        user_id: int,
        page: int = 1,
        size: int = 20,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取我的Pinboard列表（只包含用户创建的）
        """
        try:
            # 调用DAO层获取用户创建的Pinboard
            result = await PinboardDao.get_my_pinboards_with_pagination(
                user_id=user_id,
                page=page,
                size=size,
                search=search
            )

            # 转换数据格式
            items = []
            for pinboard_data in result['items']:
                pinboard = pinboard_data['pinboard']
                tags = pinboard_data.get('tags', [])
                owner_info = pinboard_data.get('owner', {})

                # 判断是否为新建（3天内创建）
                is_new = (datetime.now() - pinboard.created_at).days <= 3

                # 格式化更新时间
                update_time = PinboardService._format_time(pinboard.updated_at)

                item = PinboardItem(
                    uid=pinboard.uid,
                    name=pinboard.name,
                    description=pinboard.description,
                    owner_id=pinboard.owner_id,
                    owner_name=owner_info.get('user_name', '未知用户'),
                    owner_avatar=owner_info.get('avatar', ''),
                    last_editor=pinboard.update_by or owner_info.get('user_name', ''),
                    last_editor_avatar='',
                    is_template=bool(pinboard.is_template),
                    is_shared=bool(pinboard.is_shared),
                    share_type=pinboard.share_type,
                    is_new=is_new,
                    tags=[{'id': tag.id, 'name': tag.name, 'color': tag.color} for tag in tags],
                    created_at=pinboard.created_at,
                    updated_at=pinboard.updated_at,
                    update_time=update_time
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size
            }

        except Exception as e:
            logger.error(f"获取我的Pinboard列表失败: {str(e)}")
            raise e

    @staticmethod
    async def create_pinboard(
        request: PinboardCreateRequest,
        user_id: int,
        username: str
    ) -> PinboardItem:
        """
        创建Pinboard
        """
        try:
            # 生成唯一ID
            pinboard_uid = f"pb-{uuid.uuid4().hex[:8]}-{uuid.uuid4().hex[:8]}"

            # 调用DAO层创建
            pinboard = await PinboardDao.create_pinboard(
                uid=pinboard_uid,
                project_id=request.project_id,
                name=request.name,
                description=request.description,
                owner_id=user_id,
                is_template=request.is_template,
                create_by=username,
                update_by=username
            )

            # 处理标签关联
            if request.tags:
                await PinboardDao.add_pinboard_tags(pinboard_uid, request.tags, request.project_id)

            # 返回创建的Pinboard信息
            return PinboardItem(
                uid=pinboard.uid,
                name=pinboard.name,
                description=pinboard.description,
                owner_id=pinboard.owner_id,
                owner_name=username,
                owner_avatar='',
                last_editor=username,
                last_editor_avatar='',
                is_template=bool(pinboard.is_template),
                is_shared=bool(pinboard.is_shared),
                share_type=pinboard.share_type,
                is_new=True,
                tags=[],
                created_at=pinboard.created_at,
                updated_at=pinboard.updated_at,
                update_time=PinboardService._format_time(pinboard.updated_at)
            )

        except Exception as e:
            logger.error(f"创建Pinboard失败: {str(e)}")
            raise e

    @staticmethod
    async def get_tags(
        user_id: int,
        project_id: Optional[int] = None
    ) -> List[TagItem]:
        """
        获取标签列表
        """
        try:
            # 调用DAO层获取标签
            tags = await PinboardDao.get_tags_with_count(user_id, project_id)

            # 转换数据格式
            result = []
            for tag_data in tags:
                tag = tag_data['tag']
                count = tag_data['count']

                item = TagItem(
                    id=tag.id,
                    name=tag.name,
                    color=tag.color,
                    report_count=count,
                    modify_type="手动"
                )
                result.append(item)

            return result

        except Exception as e:
            logger.error(f"获取标签列表失败: {str(e)}")
            raise e

    @staticmethod
    async def delete_pinboard(pinboard_uid: str, user_id: int):
        """
        删除Pinboard
        """
        try:
            # 调用DAO层删除
            await PinboardDao.delete_pinboard(pinboard_uid, user_id)

        except Exception as e:
            logger.error(f"删除Pinboard失败: {str(e)}")
            raise e

    @staticmethod
    def _format_time(dt: datetime) -> str:
        """
        格式化时间显示
        """
        now = datetime.now()
        diff = now - dt

        if diff.days == 0:
            if diff.seconds < 3600:  # 1小时内
                minutes = diff.seconds // 60
                return f"{minutes}分钟前" if minutes > 0 else "刚刚"
            else:  # 今天
                hours = diff.seconds // 3600
                return f"{hours}小时前"
        elif diff.days == 1:
            return f"昨天 {dt.strftime('%H:%M:%S')}"
        elif diff.days < 7:
            return f"{diff.days}天前"
        else:
            return dt.strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    async def get_collaborate_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        tag: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取协作报告列表
        """
        try:
            # 调用DAO层获取协作报告（共享给用户的报告）
            result = await PinboardDao.get_collaborate_reports(
                user_id=user_id,
                page=page,
                size=size,
                search=search,
                tag=tag
            )

            # 转换数据格式
            items = []
            for report_data in result['items']:
                pinboard = report_data['pinboard']
                owner_info = report_data.get('owner', {})
                tags = report_data.get('tags', [])

                item = CollaborateReportItem(
                    uid=pinboard.uid,
                    title=pinboard.name,
                    description=pinboard.description,
                    author=owner_info.get('user_name', '未知用户'),
                    avatar=owner_info.get('avatar', ''),
                    last_update=PinboardService._format_time(pinboard.updated_at),
                    tags=[tag.name for tag in tags],
                    collaborators=report_data.get('collaborators_count', 0),
                    is_new=(datetime.now() - pinboard.created_at).days <= 3
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size,
                'summary': {
                    'total_reports': result['total'],
                    'new_reports': len([item for item in items if item.is_new]),
                    'categories': ['全部', '数据分析', '业务报告', '监控面板']
                }
            }

        except Exception as e:
            logger.error(f"获取协作报告列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_shared_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        filter_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取共享报告列表
        """
        try:
            # 调用DAO层获取共享报告
            result = await PinboardDao.get_shared_reports(
                user_id=user_id,
                page=page,
                size=size,
                search=search,
                filter_type=filter_type
            )

            # 转换数据格式
            items = []
            for report_data in result['items']:
                pinboard = report_data['pinboard']
                share_info = report_data.get('share_info', {})

                item = SharedReportItem(
                    uid=pinboard.uid,
                    title=pinboard.name,
                    description=pinboard.description,
                    share_url=share_info.get('share_url', ''),
                    access_password=share_info.get('access_password', ''),
                    expire_time=share_info.get('expire_time', ''),
                    view_count=share_info.get('view_count', 0),
                    share_time=PinboardService._format_time(share_info.get('created_at', datetime.now())),
                    permissions=share_info.get('permissions', [])
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size,
                'summary': {
                    'total_shares': result['total'],
                    'active_shares': len([item for item in items if not item.expire_time or item.expire_time > datetime.now().isoformat()]),
                    'total_views': sum(item.view_count for item in items)
                }
            }

        except Exception as e:
            logger.error(f"获取共享报告列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_followed_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取关注报告列表
        """
        try:
            # 调用DAO层获取关注报告
            result = await PinboardDao.get_followed_reports(
                user_id=user_id,
                page=page,
                size=size,
                search=search,
                category=category
            )

            # 转换数据格式
            items = []
            for report_data in result['items']:
                pinboard = report_data['pinboard']
                owner_info = report_data.get('owner', {})
                star_info = report_data.get('star_info', {})

                item = FollowedReportItem(
                    uid=pinboard.uid,
                    title=pinboard.name,
                    description=pinboard.description,
                    author=owner_info.get('user_name', '未知用户'),
                    avatar=owner_info.get('avatar', ''),
                    follow_time=PinboardService._format_time(star_info.get('created_at', datetime.now())),
                    last_update=PinboardService._format_time(pinboard.updated_at),
                    category=report_data.get('category', '数据分析'),
                    has_update=report_data.get('has_update', False)
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size,
                'summary': {
                    'total_followed': result['total'],
                    'updated_reports': len([item for item in items if item.has_update]),
                    'categories': ['全部', '数据分析', '业务报告', '监控面板']
                }
            }

        except Exception as e:
            logger.error(f"获取关注报告列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_reminders(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取定时提醒列表
        """
        try:
            # 调用DAO层获取定时提醒
            result = await PinboardDao.get_reminders(
                user_id=user_id,
                page=page,
                size=size,
                search=search,
                status=status
            )

            # 转换数据格式
            items = []
            for reminder_data in result['items']:
                reminder = reminder_data['reminder']
                pinboard = reminder_data.get('pinboard', {})

                import json

                # 解析JSON字段
                try:
                    recipients = json.loads(reminder.recipients) if reminder.recipients else []
                except (json.JSONDecodeError, TypeError):
                    recipients = []

                try:
                    push_methods = json.loads(reminder.push_methods) if reminder.push_methods else []
                except (json.JSONDecodeError, TypeError):
                    push_methods = []

                item = ReminderItem(
                    id=reminder.id,
                    name=reminder.name,
                    description=reminder.description,
                    pinboard_uid=reminder.pinboard_uid,
                    report_title=pinboard.get('name', '未知报告'),
                    frequency=reminder.frequency,
                    execute_time=reminder.execute_time or '',
                    next_run=PinboardService._format_time(reminder.next_run_time) if reminder.next_run_time else '',
                    recipients=recipients,
                    push_methods=push_methods,
                    enabled=bool(reminder.enabled),
                    create_time=PinboardService._format_time(reminder.created_at)
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size,
                'summary': {
                    'total_reminders': result['total'],
                    'active_reminders': len([item for item in items if item.enabled]),
                    'frequencies': ['daily', 'weekly', 'monthly', 'custom']
                }
            }

        except Exception as e:
            logger.error(f"获取定时提醒列表失败: {str(e)}")
            raise e

    @staticmethod
    async def create_reminder(
        request: ReminderCreateRequest,
        user_id: int,
        create_by: str
    ) -> Dict[str, Any]:
        """
        创建定时提醒
        """
        try:
            # 调用DAO层创建提醒
            reminder = await PinboardDao.create_reminder(
                name=request.name,
                description=request.description,
                pinboard_uid=request.pinboard_uid,
                frequency=request.frequency,
                cron_expression=request.cron_expression,
                execute_time=request.execute_time,
                recipients=request.recipients,
                push_methods=request.push_methods,
                expire_date=request.expire_date,
                create_by=create_by
            )

            return {
                'id': reminder.id,
                'name': reminder.name,
                'message': '定时提醒创建成功'
            }

        except Exception as e:
            logger.error(f"创建定时提醒失败: {str(e)}")
            raise e

    @staticmethod
    async def get_trash_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        sort_by: Optional[str] = "delete_time"
    ) -> Dict[str, Any]:
        """
        获取回收站报告列表
        """
        try:
            # 调用DAO层获取回收站报告
            result = await PinboardDao.get_trash_reports(
                user_id=user_id,
                page=page,
                size=size,
                search=search,
                sort_by=sort_by
            )

            # 转换数据格式
            items = []
            for report_data in result['items']:
                pinboard = report_data['pinboard']
                delete_record = report_data.get('delete_record', {})

                # 计算剩余天数（30天后永久删除）
                delete_time = delete_record.get('delete_time', datetime.now())
                expire_time = delete_time + timedelta(days=30)
                remaining_days = max(0, (expire_time - datetime.now()).days)

                item = TrashReportItem(
                    uid=pinboard.uid,
                    title=pinboard.name,
                    description=pinboard.description,
                    original_path=delete_record.get('original_path', ''),
                    delete_time=PinboardService._format_time(delete_time),
                    delete_by=delete_record.get('delete_by_name', '未知用户'),
                    size=delete_record.get('file_size', '未知'),
                    remaining_days=remaining_days
                )
                items.append(item)

            return {
                'items': items,
                'total': result['total'],
                'page': page,
                'size': size,
                'summary': {
                    'total_deleted': result['total'],
                    'expiring_soon': len([item for item in items if item.remaining_days <= 7]),
                    'total_size': '计算中...'
                }
            }

        except Exception as e:
            logger.error(f"获取回收站报告列表失败: {str(e)}")
            raise e

    @staticmethod
    async def restore_report(
        pinboard_uid: str,
        user_id: int,
        restore_by: str
    ) -> Dict[str, Any]:
        """
        恢复报告
        """
        try:
            # 调用DAO层恢复报告
            result = await PinboardDao.restore_report(
                pinboard_uid=pinboard_uid,
                user_id=user_id,
                restore_by=restore_by
            )

            return {
                'uid': pinboard_uid,
                'message': '报告恢复成功',
                'restore_path': result.get('restore_path', '')
            }

        except Exception as e:
            logger.error(f"恢复报告失败: {str(e)}")
            raise e
