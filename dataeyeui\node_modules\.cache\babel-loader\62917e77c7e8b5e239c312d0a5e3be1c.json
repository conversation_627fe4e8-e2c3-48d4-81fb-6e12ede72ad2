{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\api\\alert.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\api\\alert.js", "mtime": 1749628834168}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkQWxlcnQgPSBhZGRBbGVydDsKZXhwb3J0cy5nZXRBbGVydEJ5SWQgPSBnZXRBbGVydEJ5SWQ7CmV4cG9ydHMuZ2V0QWxlcnRMaXN0ID0gZ2V0QWxlcnRMaXN0OwpleHBvcnRzLnJlbW92ZUFsZXJ0ID0gcmVtb3ZlQWxlcnQ7CmV4cG9ydHMudXBkYXRlQWxlcnQgPSB1cGRhdGVBbGVydDsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOaMh+agh+WRiuitpuebuOWFs0FQSQpmdW5jdGlvbiBnZXRBbGVydExpc3QocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvYXBpL2FsZXJ0cycsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQpmdW5jdGlvbiBnZXRBbGVydEJ5SWQoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9hcGkvYWxlcnRzLyIuY29uY2F0KGlkKSwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQpmdW5jdGlvbiBhZGRBbGVydChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvYXBpL2FsZXJ0cycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpmdW5jdGlvbiB1cGRhdGVBbGVydChpZCwgZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL2FwaS9hbGVydHMvIi5jb25jYXQoaWQpLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpmdW5jdGlvbiByZW1vdmVBbGVydChpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL2FwaS9hbGVydHMvIi5jb25jYXQoaWQpLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getAlertList", "params", "request", "url", "method", "getAlertById", "id", "concat", "add<PERSON><PERSON><PERSON>", "data", "updateAlert", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/jgst/dataeyeui/src/api/alert.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 指标告警相关API\r\nexport function getAlertList(params) {\r\n  return request({\r\n    url: '/api/alerts',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\nexport function getAlertById(id) {\r\n  return request({\r\n    url: `/api/alerts/${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function addAlert(data) {\r\n  return request({\r\n    url: '/api/alerts',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function updateAlert(id, data) {\r\n  return request({\r\n    url: `/api/alerts/${id}`,\r\n    method: 'put',\r\n    data\r\n  })\r\n}\r\n\r\nexport function removeAlert(id) {\r\n  return request({\r\n    url: `/api/alerts/${id}`,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AAEO,SAASI,YAAYA,CAACC,EAAE,EAAE;EAC/B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,iBAAAI,MAAA,CAAiBD,EAAE,CAAE;IACxBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASI,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,WAAWA,CAACJ,EAAE,EAAEG,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,iBAAAI,MAAA,CAAiBD,EAAE,CAAE;IACxBF,MAAM,EAAE,KAAK;IACbK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEO,SAASE,WAAWA,CAACL,EAAE,EAAE;EAC9B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,iBAAAI,MAAA,CAAiBD,EAAE,CAAE;IACxBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}