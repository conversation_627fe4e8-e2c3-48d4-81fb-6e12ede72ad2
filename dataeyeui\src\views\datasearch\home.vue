<template>
  <div class="datasearch-home-bg">
    <div class="datasearch-home-main">
      <div class="datasearch-home-header">
        <div class="datasearch-home-face">
          <svg width="80" height="80" viewBox="0 0 80 80"><circle cx="40" cy="40" r="40" fill="#f4f6fa"/><circle cx="40" cy="40" r="36" fill="#fff"/><ellipse cx="30" cy="36" rx="4" ry="6" fill="#d8dde3"/><ellipse cx="50" cy="36" rx="4" ry="6" fill="#d8dde3"/><path d="M30 56c3.5 2.5 19.5 2.5 22 0" stroke="#d8dde3" stroke-width="2" stroke-linecap="round" fill="none"/></svg>
        </div>
        <div class="datasearch-home-tip">
          开始探索数据吧！<span class="datasearch-home-tip-link" @click="goToSearch">在这里</span>可以精通数据搜索~
        </div>
      </div>
      <div class="datasearch-home-searchbar">
        <input
          v-model="keyword"
          class="datasearch-home-input"
          type="text"
          placeholder="搜索 门店营业额 前十 门店 营业额"
          @focus="showPopup = true"
          @keyup.enter="goSearch"
          ref="input"
        />
        <button class="datasearch-home-search-btn" @click="goSearch" type="button">
          <i class="search-icon"></i>
        </button>
        <div v-if="showPopup" class="search-history-popup" ref="searchPopup">
          <div class="search-history-popup-title">搜索历史</div>
          <div class="search-history-popup-list">
            <div class="search-history-popup-item" v-for="item in historyList" :key="item" @click="setKeyword(item)">
              <i class="history-icon"></i>{{ item }}
            </div>
          </div>
          <div class="search-history-popup-title hot">热门搜索</div>
          <div class="search-history-popup-list">
            <div class="search-history-popup-item hot" v-for="item in recommendList" :key="item" @click="setKeyword(item)">
              <i class="hot-icon"></i>{{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="datasearch-home-recommend-row">
        <span class="datasearch-home-recommend-item" v-for="item in recommendList" :key="item" @click="setKeyword(item)">{{ item }}</span>
      </div>
      <div class="datasearch-home-history-row">
        <span class="datasearch-home-history-item" v-for="item in historyList" :key="item" @click="setKeyword(item)">
          <i class="history-icon"></i>{{ item }}
        </span>
      </div>
      <div class="datasearch-home-recommend-title">推荐搜索</div>
      <div class="datasearch-home-history-title">历史搜索</div>
    </div>
    <div class="datasearch-home-version">**********.15.26</div>
  </div>
</template>

<script>
import { getHistoryList } from '@/api/history'
import { getTopQuestions } from '@/api/top_questions'
import { addHistoryQuery } from '@/api/history_add'

export default {
  name: 'DataSearchHome',
  data() {
    return {
      keyword: '',
      showPopup: false,
      recommendList: [],
      historyList: []
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside)
    // 加载历史搜索
    getHistoryList({ pageNum: 1, pageSize: 8 }).then(res => {
      if (res && res.items) {
        this.historyList = res.items.map(item => item.question || item.content || item.identity || '')
      }
    })
    // 加载热门搜索
    getTopQuestions({ pageNum: 1, pageSize: 8 }).then(res => {
      if (res && res.items) {
        this.recommendList = res.items.map(item => item.question || item.content || item.identity || '')
      }
    })
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  watch: {
    '$route.query.keyword'(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.keyword = newVal;
        // 这里可以加你需要的刷新逻辑，比如重新请求数据
      }
    }
  },
  methods: {
    goSearch() {
      this.showPopup = false;
      // 写入历史搜索
      if (this.keyword && this.keyword.trim()) {
        addHistoryQuery({
          project_id: 1, // 可根据实际项目动态传递
          user_id: 1,    // 可根据实际用户动态传递
          identity: this.keyword,
          tokens: this.keyword
        })
      }
      this.$router.push({ name: 'DataSearchIndex', query: { keyword: this.keyword } });
    },
    setKeyword(val) {
      this.keyword = val;
      this.goSearch();
    },
    goToSearch() {
      this.$refs.input && this.$refs.input.focus();
      this.showPopup = true;
    },
    handleClickOutside(e) {
      if (!this.$refs.input || this.$refs.input.contains(e.target)) {
        return;
      }
      const popup = this.$refs.searchPopup;
      if (popup && popup.contains(e.target)) {
        return;
      }
      this.showPopup = false;
    }
  }
}
</script>

<style scoped>
.datasearch-home-bg {
  min-height: 100vh;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}
.datasearch-home-main {
  width: 100vw;
  max-width: 1200px;
  margin-top: 40px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.datasearch-home-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 18px;
}
.datasearch-home-face {
  margin-bottom: 10px;
}
.datasearch-home-tip {
  color: #b2b2b2;
  font-size: 18px;
  margin-bottom: 10px;
  margin-top: 10px;
}
.datasearch-home-tip-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
  margin: 0 2px;
}
.datasearch-home-searchbar {
  display: flex;
  align-items: center;
  width: 95vw;
  max-width: 900px;
  background: #fff;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 10px 30px;
  margin-bottom: 30px;
  margin-top: 10px;
  position: relative; /* 关键：让弹窗定位于搜索栏下方 */
}
.datasearch-home-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 20px;
  background: transparent;
  height: 40px;
}
.datasearch-home-search-btn {
  background: #1890ff;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 10px;
}
.search-icon {
  width: 20px;
  height: 20px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23fff" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M21.71 20.29l-3.4-3.39A8.93 8.93 0 0019 11a9 9 0 10-9 9 8.93 8.93 0 005.9-1.69l3.39 3.4a1 1 0 001.42-1.42zM4 11a7 7 0 117 7 7 7 0 01-7-7z"/></svg>') no-repeat center center;
  background-size: contain;
  display: inline-block;
}
.datasearch-home-recommend-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 95vw;
  max-width: 1200px;
  margin-top: 30px;
  margin-bottom: 10px;
  gap: 8px;
}
.datasearch-home-recommend-item {
  background: transparent;
  color: #3b6ed6;
  font-size: 16px;
  margin-right: 24px;
  cursor: pointer;
  padding: 0 0 2px 0;
  border-bottom: 1px dashed #dbe3f4;
  transition: color 0.2s;
}
.datasearch-home-recommend-item:hover {
  color: #1890ff;
  border-bottom: 1px solid #1890ff;
}
.datasearch-home-recommend-title {
  color: #b2b2b2;
  font-size: 15px;
  margin-top: 8px;
  margin-bottom: 0;
  width: 95vw;
  max-width: 1200px;
  text-align: left;
  letter-spacing: 1px;
}
.datasearch-home-history-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 95vw;
  max-width: 1200px;
  margin-top: 18px;
  gap: 8px;
}
.datasearch-home-history-item {
  background: #f4f6fa;
  border-radius: 16px;
  padding: 4px 16px;
  color: #666;
  cursor: pointer;
  font-size: 15px;
  display: flex;
  align-items: center;
  transition: background 0.2s;
  border: 1px solid #e5e8ef;
}
.datasearch-home-history-item:hover {
  background: #e6f7ff;
}
.datasearch-home-history-title {
  color: #b2b2b2;
  font-size: 15px;
  margin-top: 8px;
  width: 95vw;
  max-width: 1200px;
  text-align: left;
  letter-spacing: 1px;
}
.history-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23999" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13 3a9 9 0 1 0 8.94 10.06 1 1 0 0 0-2-.12A7 7 0 1 1 5 12H7.59l-2.3 2.29A1 1 0 0 1 4 13V8a1 1 0 0 1 1-1h5a1 1 0 0 1 0 2H6.41A9 9 0 0 0 13 3zm-1 5a1 1 0 0 1 2 0v4.59l3.29 3.3a1 1 0 0 1-1.42 1.41l-3.59-3.59A1 1 0 0 1 12 13V8z"/></svg>') no-repeat center center;
  background-size: contain;
  display: inline-block;
}
.datasearch-home-version {
  color: #b2b2b2;
  font-size: 13px;
  position: absolute;
  right: 40px;
  bottom: 16px;
  user-select: none;
}
.search-history-popup {
  position: absolute;
  top: 100%; /* 紧贴搜索栏底部 */
  left: 0;
  width: 420px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 16px 0 8px 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  margin-top: 8px; /* 与搜索栏有间距 */
}
.search-history-popup-title {
  font-size: 15px;
  color: #b2b2b2;
  font-weight: 500;
  padding: 0 24px 6px 24px;
}
.search-history-popup-title.hot {
  margin-top: 12px;
}
.search-history-popup-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0 24px 8px 24px;
}
.search-history-popup-item {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #333;
  padding: 6px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s;
}
.search-history-popup-item:hover {
  background: #f4f6fa;
}
.search-history-popup-item.hot .hot-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background: url('data:image/svg+xml;utf8,<svg fill="%233b6ed6" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>') no-repeat center center;
  background-size: contain;
  display: inline-block;
}
.search-history-popup-item .history-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background: url('data:image/svg+xml;utf8,<svg fill="%23999" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13 3a9 9 0 1 0 8.94 10.06 1 1 0 0 0-2-.12A7 7 0 1 1 5 12H7.59l-2.3 2.29A1 1 0 0 1 4 13V8a1 1 0 0 1 1-1h5a1 1 0 0 1 0 2H6.41A9 9 0 0 0 13 3zm-1 5a1 1 0 0 1 2 0v4.59l3.29 3.3a1 1 0 0 1-1.42 1.41l-3.59-3.59A1 1 0 0 1 12 13V8z"/></svg>') no-repeat center center;
  background-size: contain;
  display: inline-block;
}
</style>