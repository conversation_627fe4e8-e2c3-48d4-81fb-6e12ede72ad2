<template>
  <div class="app-container">
    <div class="page-header">
      <h2 class="page-title">共享报告</h2>
      <p class="page-description">管理您分享给他人的报告</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" icon="el-icon-share" @click="shareNewReport">分享报告</el-button>
        <el-button icon="el-icon-refresh" @click="refreshList">刷新</el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索共享报告..."
          prefix-icon="el-icon-search"
          style="width: 300px;"
          clearable
        />
        <el-select v-model="filterStatus" placeholder="状态筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="公开" value="public" />
          <el-option label="私有链接" value="private" />
          <el-option label="已过期" value="expired" />
        </el-select>
      </div>
    </div>

    <!-- 共享报告列表 -->
    <div class="shared-list">
      <el-table :data="filteredReports" style="width: 100%">
        <el-table-column width="60">
          <template slot-scope="scope">
            <div class="report-icon">
              <i class="el-icon-document"></i>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="报告名称" min-width="200">
          <template slot-scope="scope">
            <div class="report-info">
              <div class="report-title">{{ scope.row.title }}</div>
              <div class="report-description">{{ scope.row.description }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="shareType" label="分享类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getShareTypeTag(scope.row.shareType)" size="small">
              {{ getShareTypeText(scope.row.shareType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="viewCount" label="访问次数" width="100" />
        
        <el-table-column prop="shareTime" label="分享时间" width="150" />
        
        <el-table-column prop="expireTime" label="过期时间" width="150">
          <template slot-scope="scope">
            <span :class="{ 'expired': isExpired(scope.row.expireTime) }">
              {{ scope.row.expireTime || '永不过期' }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getStatusTag(scope.row)" 
              size="small"
            >
              {{ getStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="copyLink(scope.row)">复制链接</el-button>
            <el-button type="text" size="small" @click="editShare(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="viewStats(scope.row)">统计</el-button>
            <el-button type="text" size="small" @click="stopShare(scope.row)">停止分享</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 分享设置对话框 -->
    <el-dialog title="分享设置" :visible.sync="shareDialogVisible" width="600px">
      <el-form :model="shareForm" label-width="100px">
        <el-form-item label="选择报告">
          <el-select v-model="shareForm.reportId" placeholder="选择要分享的报告" style="width: 100%;">
            <el-option 
              v-for="report in myReports" 
              :key="report.id" 
              :label="report.title" 
              :value="report.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分享类型">
          <el-radio-group v-model="shareForm.shareType">
            <el-radio label="public">公开分享</el-radio>
            <el-radio label="private">私有链接</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="访问权限">
          <el-checkbox-group v-model="shareForm.permissions">
            <el-checkbox label="view">查看</el-checkbox>
            <el-checkbox label="download">下载</el-checkbox>
            <el-checkbox label="comment">评论</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="过期时间">
          <el-radio-group v-model="shareForm.expireType">
            <el-radio label="never">永不过期</el-radio>
            <el-radio label="custom">自定义</el-radio>
          </el-radio-group>
          <el-date-picker
            v-if="shareForm.expireType === 'custom'"
            v-model="shareForm.expireTime"
            type="datetime"
            placeholder="选择过期时间"
            style="margin-top: 10px; width: 100%;"
          />
        </el-form-item>
        
        <el-form-item label="访问密码">
          <el-input 
            v-model="shareForm.password" 
            placeholder="可选，设置访问密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="shareDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmShare">确认分享</el-button>
      </div>
    </el-dialog>

    <!-- 统计对话框 -->
    <el-dialog title="访问统计" :visible.sync="statsDialogVisible" width="800px">
      <div class="stats-content">
        <div class="stats-summary">
          <div class="stat-item">
            <div class="stat-number">{{ currentStats.totalViews }}</div>
            <div class="stat-label">总访问量</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ currentStats.uniqueVisitors }}</div>
            <div class="stat-label">独立访客</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ currentStats.todayViews }}</div>
            <div class="stat-label">今日访问</div>
          </div>
        </div>
        
        <div class="stats-chart">
          <h4>访问趋势</h4>
          <div class="chart-placeholder">
            <p>访问趋势图表（此处可集成图表组件）</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ReportShared',
  data() {
    return {
      searchKeyword: '',
      filterStatus: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      shareDialogVisible: false,
      statsDialogVisible: false,
      shareForm: {
        reportId: '',
        shareType: 'private',
        permissions: ['view'],
        expireType: 'never',
        expireTime: null,
        password: ''
      },
      currentStats: {
        totalViews: 0,
        uniqueVisitors: 0,
        todayViews: 0
      },
      reports: [
        {
          id: 1,
          title: '销售数据分析报告',
          description: '2024年第一季度销售数据分析',
          shareType: 'public',
          viewCount: 156,
          shareTime: '2024-01-10',
          expireTime: null,
          status: 'active'
        },
        {
          id: 2,
          title: '用户行为分析',
          description: '用户行为数据统计与分析',
          shareType: 'private',
          viewCount: 89,
          shareTime: '2024-01-08',
          expireTime: '2024-02-08',
          status: 'active'
        },
        {
          id: 3,
          title: '财务月报',
          description: '12月份财务数据汇总',
          shareType: 'private',
          viewCount: 23,
          shareTime: '2024-01-05',
          expireTime: '2024-01-20',
          status: 'expired'
        }
      ],
      myReports: [
        { id: 1, title: '销售数据分析报告' },
        { id: 4, title: '产品销售报告' },
        { id: 5, title: '客户满意度调查' }
      ]
    }
  },
  computed: {
    filteredReports() {
      let filtered = this.reports
      
      // 状态筛选
      if (this.filterStatus) {
        if (this.filterStatus === 'expired') {
          filtered = filtered.filter(report => this.isExpired(report.expireTime))
        } else {
          filtered = filtered.filter(report => report.shareType === this.filterStatus)
        }
      }
      
      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(report => 
          report.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          report.description.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      
      this.total = filtered.length
      return filtered
    }
  },
  methods: {
    getShareTypeTag(type) {
      return type === 'public' ? 'success' : 'warning'
    },
    getShareTypeText(type) {
      return type === 'public' ? '公开' : '私有链接'
    },
    getStatusTag(report) {
      if (this.isExpired(report.expireTime)) return 'danger'
      return 'success'
    },
    getStatusText(report) {
      if (this.isExpired(report.expireTime)) return '已过期'
      return '正常'
    },
    isExpired(expireTime) {
      if (!expireTime) return false
      return new Date(expireTime) < new Date()
    },
    shareNewReport() {
      this.shareDialogVisible = true
    },
    refreshList() {
      this.$message.success('列表已刷新')
    },
    copyLink(report) {
      // 模拟复制链接
      const link = `https://example.com/share/${report.id}`
      navigator.clipboard.writeText(link).then(() => {
        this.$message.success('链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    editShare(report) {
      this.shareForm = {
        reportId: report.id,
        shareType: report.shareType,
        permissions: ['view'],
        expireType: report.expireTime ? 'custom' : 'never',
        expireTime: report.expireTime ? new Date(report.expireTime) : null,
        password: ''
      }
      this.shareDialogVisible = true
    },
    viewStats(report) {
      this.currentStats = {
        totalViews: report.viewCount,
        uniqueVisitors: Math.floor(report.viewCount * 0.7),
        todayViews: Math.floor(Math.random() * 20)
      }
      this.statsDialogVisible = true
    },
    stopShare(report) {
      this.$confirm(`确定要停止分享"${report.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reports.findIndex(r => r.id === report.id)
        if (index > -1) {
          this.reports.splice(index, 1)
          this.$message.success('已停止分享')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    confirmShare() {
      if (!this.shareForm.reportId) {
        this.$message.warning('请选择要分享的报告')
        return
      }
      
      // 这里应该调用API创建分享
      this.$message.success('分享设置已保存')
      this.shareDialogVisible = false
    },
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  
  .page-title {
    font-size: 24px;
    color: #303133;
    margin: 0 0 8px 0;
  }
  
  .page-description {
    color: #909399;
    margin: 0;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .left-actions {
    display: flex;
    gap: 10px;
  }
  
  .right-actions {
    display: flex;
    align-items: center;
  }
}

.shared-list {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .report-icon {
    width: 32px;
    height: 32px;
    background: #F56C6C;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
  }
  
  .report-info {
    .report-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .report-description {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }
  
  .expired {
    color: #f56c6c;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stats-content {
  .stats-summary {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
  
  .stats-chart {
    h4 {
      margin-bottom: 16px;
      color: #303133;
    }
    
    .chart-placeholder {
      height: 200px;
      background: #f5f7fa;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;
    }
  }
}
</style>
