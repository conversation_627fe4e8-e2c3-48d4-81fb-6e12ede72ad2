{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\layout\\components\\InnerLink\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\layout\\components\\InnerLink\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgc3JjOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogIi8iCiAgICB9LAogICAgaWZyYW1lSWQ6IHsKICAgICAgdHlwZTogU3RyaW5nCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCAtIDk0LjUgKyAicHg7IgogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgY29uc3QgaWZyYW1lSWQgPSAoIiMiICsgdGhpcy5pZnJhbWVJZCkucmVwbGFjZSgvXC8vZywgIlxcLyIpOwogICAgY29uc3QgaWZyYW1lID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihpZnJhbWVJZCk7CiAgICAvLyBpZnJhbWXpobXpnaJsb2FkaW5n5o6n5Yi2CiAgICBpZiAoaWZyYW1lLmF0dGFjaEV2ZW50KSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGlmcmFtZS5hdHRhY2hFdmVudCgib25sb2FkIiwgZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9IGVsc2UgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBpZnJhbWUub25sb2FkID0gZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/InnerLink", "sourcesContent": ["<template>\n  <div :style=\"'height:' + height\" v-loading=\"loading\" element-loading-text=\"正在加载页面，请稍候！\">\n    <iframe\n      :id=\"iframeId\"\n      style=\"width: 100%; height: 100%\"\n      :src=\"src\"\n      frameborder=\"no\"\n    ></iframe>\n  </div>\n</template>\n\n<script>\nexport default {\n  props: {\n    src: {\n      type: String,\n      default: \"/\"\n    },\n    iframeId: {\n      type: String\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      height: document.documentElement.clientHeight - 94.5 + \"px;\"\n    };\n  },\n  mounted() {\n    var _this = this;\n    const iframeId = (\"#\" + this.iframeId).replace(/\\//g, \"\\\\/\");\n    const iframe = document.querySelector(iframeId);\n    // iframe页面loading控制\n    if (iframe.attachEvent) {\n      this.loading = true;\n      iframe.attachEvent(\"onload\", function () {\n        _this.loading = false;\n      });\n    } else {\n      this.loading = true;\n      iframe.onload = function () {\n        _this.loading = false;\n      };\n    }\n  }\n};\n</script>\n"]}]}