{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\ImagePreview\\index.vue?vue&type=style&index=0&id=4b9dcf40&lang=scss&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\ImagePreview\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZWwtaW1hZ2UgewogIGJvcmRlci1yYWRpdXM6IDVweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWJlZWY1OwogIGJveC1zaGFkb3c6IDAgMCA1cHggMXB4ICNjY2M7CiAgOjp2LWRlZXAgLmVsLWltYWdlX19pbm5lciB7CiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICY6aG92ZXIgewogICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMik7CiAgICB9CiAgfQogIDo6di1kZWVwIC5pbWFnZS1zbG90IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMTAwJTsKICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgZm9udC1zaXplOiAzMHB4OwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImagePreview", "sourcesContent": ["<template>\n  <el-image\n    :src=\"`${realSrc}`\"\n    fit=\"cover\"\n    :style=\"`width:${realWidth};height:${realHeight};`\"\n    :preview-src-list=\"realSrcList\"\n  >\n    <div slot=\"error\" class=\"image-slot\">\n      <i class=\"el-icon-picture-outline\"></i>\n    </div>\n  </el-image>\n</template>\n\n<script>\nimport { isExternal } from \"@/utils/validate\";\n\nexport default {\n  name: \"ImagePreview\",\n  props: {\n    src: {\n      type: String,\n      default: \"\"\n    },\n    width: {\n      type: [Number, String],\n      default: \"\"\n    },\n    height: {\n      type: [Number, String],\n      default: \"\"\n    }\n  },\n  computed: {\n    realSrc() {\n      if (!this.src) {\n        return;\n      }\n      let real_src = this.src.split(\",\")[0];\n      if (isExternal(real_src)) {\n        return real_src;\n      }\n      return process.env.VUE_APP_BASE_API + real_src;\n    },\n    realSrcList() {\n      if (!this.src) {\n        return;\n      }\n      let real_src_list = this.src.split(\",\");\n      let srcList = [];\n      real_src_list.forEach(item => {\n        if (isExternal(item)) {\n          return srcList.push(item);\n        }\n        return srcList.push(process.env.VUE_APP_BASE_API + item);\n      });\n      return srcList;\n    },\n    realWidth() {\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`;\n    },\n    realHeight() {\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`;\n    }\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.el-image {\n  border-radius: 5px;\n  background-color: #ebeef5;\n  box-shadow: 0 0 5px 1px #ccc;\n  ::v-deep .el-image__inner {\n    transition: all 0.3s;\n    cursor: pointer;\n    &:hover {\n      transform: scale(1.2);\n    }\n  }\n  ::v-deep .image-slot {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n    color: #909399;\n    font-size: 30px;\n  }\n}\n</style>\n"]}]}