{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\test-api.vue?vue&type=style&index=0&id=63c090f9&scoped=true&lang=css", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\test-api.vue", "mtime": 1749638102000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi50ZXN0LWFwaSB7CiAgcGFkZGluZzogMjBweDsKfQoKLnRlc3QtY2FyZCB7CiAgbWF4LXdpZHRoOiA4MDBweDsKICBtYXJnaW46IDAgYXV0bzsKfQoKLnJlc3VsdC1hcmVhLCAuZXJyb3ItYXJlYSB7CiAgbWFyZ2luLXRvcDogMjBweDsKICBwYWRkaW5nOiAxNXB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKLnJlc3VsdC1hcmVhIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOwogIGJvcmRlcjogMXB4IHNvbGlkICM2N2MyM2E7Cn0KCi5lcnJvci1hcmVhIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmVmMGYwOwogIGJvcmRlcjogMXB4IHNvbGlkICNmNTZjNmM7Cn0KCnByZSB7CiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOwogIHdvcmQtd3JhcDogYnJlYWstd29yZDsKICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlOwogIGZvbnQtc2l6ZTogMTJweDsKfQo="}, {"version": 3, "sources": ["test-api.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "test-api.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"test-api\">\n    <h2>API连接测试</h2>\n    \n    <el-card class=\"test-card\">\n      <div slot=\"header\">\n        <span>测试后端连接</span>\n      </div>\n      \n      <el-button @click=\"testConnection\" type=\"primary\" :loading=\"loading\">\n        测试连接\n      </el-button>\n      \n      <div v-if=\"result\" class=\"result-area\">\n        <h4>测试结果：</h4>\n        <pre>{{ result }}</pre>\n      </div>\n      \n      <div v-if=\"error\" class=\"error-area\">\n        <h4>错误信息：</h4>\n        <pre>{{ error }}</pre>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios'\n\nexport default {\n  name: 'TestApi',\n  data() {\n    return {\n      loading: false,\n      result: null,\n      error: null\n    }\n  },\n  methods: {\n    async testConnection() {\n      this.loading = true\n      this.result = null\n      this.error = null\n      \n      try {\n        // 直接测试后端接口，不通过前端代理\n        const response = await axios.get('http://localhost:9099/pinboard/test', {\n          timeout: 5000,\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        })\n        \n        this.result = JSON.stringify(response.data, null, 2)\n        console.log('API测试成功:', response.data)\n        \n      } catch (error) {\n        this.error = {\n          message: error.message,\n          code: error.code,\n          response: error.response ? {\n            status: error.response.status,\n            statusText: error.response.statusText,\n            data: error.response.data\n          } : null\n        }\n        console.error('API测试失败:', error)\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-api {\n  padding: 20px;\n}\n\n.test-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.result-area, .error-area {\n  margin-top: 20px;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.result-area {\n  background-color: #f0f9ff;\n  border: 1px solid #67c23a;\n}\n\n.error-area {\n  background-color: #fef0f0;\n  border: 1px solid #f56c6c;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n</style>\n"]}]}