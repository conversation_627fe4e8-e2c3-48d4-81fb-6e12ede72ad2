import request from '@/utils/request'

// 关注问题相关API
export function getQuestionStarList(params) {
  return request({
    url: '/api/question_stars',
    method: 'get',
    params
  })
}

export function addQuestionStar(data) {
  return request({
    url: '/api/question_stars',
    method: 'post',
    data
  })
}

export function removeQuestionStar(id) {
  return request({
    url: `/api/question_stars/${id}`,
    method: 'delete'
  })
}
