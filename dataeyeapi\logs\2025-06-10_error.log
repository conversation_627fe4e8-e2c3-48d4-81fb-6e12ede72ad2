2025-06-10 08:34:23.823 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 08:34:23.824 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 08:34:30.276 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-10 08:34:30.276 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-10 08:34:30.278 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-10 08:34:31.256 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-10 08:34:33.568 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-10 08:34:33.568 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-10 08:35:09.825 | d3d7982e9bce410a8074a194caf8dd73 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为cf9aed82-9a2b-4899-a7e0-06ec39a13aea的会话获取图片验证码成功
2025-06-10 08:35:13.601 | 30c30850f1534554a928683b4d4371cb | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-10 08:35:13.930 | 41dd4a979a20483d86c1b51556883bfc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-10 08:35:14.541 | 243a681f609d45d2acbd05254579839c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-10 09:23:56.418 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:23:56.418 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:25:21.428 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:25:21.428 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:28:49.570 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:28:49.570 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:34:13.318 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:34:13.319 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:39:04.776 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:39:04.777 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:42:52.001 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:42:52.002 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:47:21.697 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:47:21.698 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:49:38.627 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:49:38.627 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:52:08.574 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:52:08.574 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:58:50.963 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 09:58:50.963 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 09:58:51.713 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-10 09:58:51.713 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-10 09:58:51.716 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-10 09:58:52.252 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-10 09:58:52.835 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-10 09:58:52.835 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-10 09:59:00.106 | 3444f496264b4cbc8992fba6a309802c | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为85c2e118-0cef-4bde-a28c-4b3ca6376bbd的会话获取图片验证码成功
2025-06-10 09:59:03.020 | 8d09cd91fd3a4a37816f9ef0b8fdcfd6 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-10 09:59:03.227 | 3fdc0bd4552a4a4a93d3540170e97a09 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-10 09:59:03.714 | 913121c7ae0947e7b9b0568386734b91 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-10 17:50:08.730 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 17:50:08.730 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 17:50:09.569 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-10 17:50:09.569 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-10 17:50:09.571 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-10 17:50:10.141 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-10 17:50:10.734 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-10 17:50:10.735 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-10 17:51:03.032 | 491ea213bed34db99214f9200d1d17e9 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-10 17:51:03.233 | 9e4b20e5af13452aae604929fc76833b | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-10 17:51:03.347 | 83625fe3900f4d62a36f5d6ebd0a67cb | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d1dfe593-e270-468a-948e-7c0f3f65dbf9的会话获取图片验证码成功
2025-06-10 17:51:07.616 | 0c0a49108216487eb83ecf309a7bf7b0 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-10 17:51:08.422 | 17b1dba1730643df8904edc03a81a0ac | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-10 17:51:08.872 | 46a3a7926b3e4b9e9add6f2e1303e5fa | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-10 18:10:24.847 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-10 18:10:24.848 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-10 18:10:25.971 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-10 18:10:25.972 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-10 18:10:25.974 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-10 18:10:26.813 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-10 18:10:27.592 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-10 18:10:27.592 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-10 18:10:35.592 | 31a011dc7f714298b00a8695e9c8c0f9 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为1dfd7ac2-5344-4509-9e8f-00196271d6f0的会话获取图片验证码成功
2025-06-10 18:10:38.084 | 81ca264213fd40d895645bff434f4b82 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-10 18:10:38.397 | 2feaad2d7660479294cf8de4633fe847 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-10 18:10:38.968 | c7e858ee95804e83a8216e96bb7fa959 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
