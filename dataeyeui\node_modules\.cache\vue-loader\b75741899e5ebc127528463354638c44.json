{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\system\\user\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\system\\user\\profile\\userAvatar.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userAvatar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userAvatar.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <div>\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\"  @close=\"closeDialog\">\n      <el-row>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <vue-cropper\n            ref=\"cropper\"\n            :img=\"options.img\"\n            :info=\"true\"\n            :autoCrop=\"options.autoCrop\"\n            :autoCropWidth=\"options.autoCropWidth\"\n            :autoCropHeight=\"options.autoCropHeight\"\n            :fixedBox=\"options.fixedBox\"\n            :outputType=\"options.outputType\"\n            @realTime=\"realTime\"\n            v-if=\"visible\"\n          />\n        </el-col>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <div class=\"avatar-upload-preview\">\n            <img :src=\"previews.url\" :style=\"previews.img\" />\n          </div>\n        </el-col>\n      </el-row>\n      <br />\n      <el-row>\n        <el-col :lg=\"2\" :sm=\"3\" :xs=\"3\">\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\n            <el-button size=\"small\">\n              选择\n              <i class=\"el-icon-upload el-icon--right\"></i>\n            </el-button>\n          </el-upload>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 2}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 2, offset: 6}\" :sm=\"2\" :xs=\"2\">\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\n        </el-col>\n      </el-row>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport store from \"@/store\";\nimport { VueCropper } from \"vue-cropper\";\nimport { uploadAvatar } from \"@/api/system/user\";\nimport { debounce } from '@/utils'\n\nexport default {\n  components: { VueCropper },\n  data() {\n    return {\n      // 是否显示弹出层\n      open: false,\n      // 是否显示cropper\n      visible: false,\n      // 弹出层标题\n      title: \"修改头像\",\n      options: {\n        img: store.getters.avatar,  //裁剪图片的地址\n        autoCrop: true,             // 是否默认生成截图框\n        autoCropWidth: 200,         // 默认生成截图框宽度\n        autoCropHeight: 200,        // 默认生成截图框高度\n        fixedBox: true,             // 固定截图框大小 不允许改变\n        outputType:\"png\",           // 默认生成截图为PNG格式\n        filename: 'avatar'          // 文件名称\n      },\n      previews: {},\n      resizeHandler: null\n    };\n  },\n  methods: {\n    // 编辑头像\n    editCropper() {\n      this.open = true;\n    },\n    // 打开弹出层结束时的回调\n    modalOpened() {\n      this.visible = true;\n      if (!this.resizeHandler) {\n        this.resizeHandler = debounce(() => {\n          this.refresh()\n        }, 100)\n      }\n      window.addEventListener(\"resize\", this.resizeHandler)\n    },\n    // 刷新组件\n    refresh() {\n      this.$refs.cropper.refresh();\n    },\n    // 覆盖默认的上传行为\n    requestUpload() {\n    },\n    // 向左旋转\n    rotateLeft() {\n      this.$refs.cropper.rotateLeft();\n    },\n    // 向右旋转\n    rotateRight() {\n      this.$refs.cropper.rotateRight();\n    },\n    // 图片缩放\n    changeScale(num) {\n      num = num || 1;\n      this.$refs.cropper.changeScale(num);\n    },\n    // 上传预处理\n    beforeUpload(file) {\n      if (file.type.indexOf(\"image/\") == -1) {\n        this.$modal.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\");\n      } else {\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          this.options.img = reader.result;\n          this.options.filename = file.name;\n        };\n      }\n    },\n    // 上传图片\n    uploadImg() {\n      this.$refs.cropper.getCropBlob(data => {\n        let formData = new FormData();\n        formData.append(\"avatarfile\", data, this.options.filename);\n        uploadAvatar(formData).then(response => {\n          this.open = false;\n          this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl;\n          store.commit('SET_AVATAR', this.options.img);\n          this.$modal.msgSuccess(\"修改成功\");\n          this.visible = false;\n        });\n      });\n    },\n    // 实时预览\n    realTime(data) {\n      this.previews = data;\n    },\n    // 关闭窗口\n    closeDialog() {\n      this.options.img = store.getters.avatar\n      this.visible = false;\n      window.removeEventListener(\"resize\", this.resizeHandler)\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n.user-info-head {\n  position: relative;\n  display: inline-block;\n  height: 120px;\n}\n\n.user-info-head:hover:after {\n  content: '+';\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  color: #eee;\n  background: rgba(0, 0, 0, 0.5);\n  font-size: 24px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  cursor: pointer;\n  line-height: 110px;\n  border-radius: 50%;\n}\n</style>\n"]}]}