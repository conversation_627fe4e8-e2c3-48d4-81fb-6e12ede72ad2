{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\register.vue", "mtime": 1747038938000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "name", "data", "_this", "equalToPassword", "rule", "value", "callback", "registerForm", "password", "Error", "codeUrl", "username", "confirmPassword", "code", "uuid", "registerRules", "required", "trigger", "message", "min", "max", "pattern", "validator", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created", "getCode", "methods", "_this2", "getCodeImg", "then", "res", "undefined", "img", "handleRegister", "_this3", "$refs", "validate", "valid", "register", "$alert", "dangerouslyUseHTMLString", "type", "$router", "push", "catch"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\n  <div class=\"register\">\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n      <h3 class=\"title\">金刚数瞳</h3>\n      <el-form-item prop=\"username\">\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"registerForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"confirmPassword\">\n        <el-input\n          v-model=\"registerForm.confirmPassword\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"确认密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"registerForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"register-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleRegister\"\n        >\n          <span v-if=\"!loading\">注 册</span>\n          <span v-else>注 册 中...</span>\n        </el-button>\n        <div style=\"float: right;\">\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\";\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    };\n  },\n  created() {\n    this.getCode();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\n          this.registerForm.uuid = res.uuid;\n        }\n      });\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username;\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/login\");\n            }).catch(() => {});\n          }).catch(() => {\n            this.loading = false;\n            if (this.captchaEnabled) {\n              this.getCode();\n            }\n          })\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.register-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.register-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.register-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.register-code-img {\n  height: 38px;\n}\n</style>\n"], "mappings": ";;;;;;;;AAqEA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,YAAA,CAAAC,QAAA,KAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACA;MACAI,OAAA;MACAH,YAAA;QACAI,QAAA;QACAH,QAAA;QACAI,eAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,aAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAD,OAAA;QAAA,EACA;QACAT,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAD,OAAA;QAAA,GACA;UAAAI,OAAA;UAAAH,OAAA;UAAAD,OAAA;QAAA,EACA;QACAL,eAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAM,SAAA,EAAAnB,eAAA;UAAAc,OAAA;QAAA,EACA;QACAJ,IAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAK,OAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAJ,cAAA,GAAAO,GAAA,CAAAP,cAAA,KAAAQ,SAAA,UAAAD,GAAA,CAAAP,cAAA;QACA,IAAAI,MAAA,CAAAJ,cAAA;UACAI,MAAA,CAAAlB,OAAA,8BAAAqB,GAAA,CAAAE,GAAA;UACAL,MAAA,CAAArB,YAAA,CAAAO,IAAA,GAAAiB,GAAA,CAAAjB,IAAA;QACA;MACA;IACA;IACAoB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA7B,YAAA,CAAA8B,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAZ,OAAA;UACA,IAAAgB,eAAA,EAAAJ,MAAA,CAAA5B,YAAA,EAAAuB,IAAA,WAAAC,GAAA;YACA,IAAApB,QAAA,GAAAwB,MAAA,CAAA5B,YAAA,CAAAI,QAAA;YACAwB,MAAA,CAAAK,MAAA,iCAAA7B,QAAA;cACA8B,wBAAA;cACAC,IAAA;YACA,GAAAZ,IAAA;cACAK,MAAA,CAAAQ,OAAA,CAAAC,IAAA;YACA,GAAAC,KAAA;UACA,GAAAA,KAAA;YACAV,MAAA,CAAAZ,OAAA;YACA,IAAAY,MAAA,CAAAX,cAAA;cACAW,MAAA,CAAAT,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}