{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\tool\\gen\\genInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\tool\\gen\\genInfoForm.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CgpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogeyBUcmVlc2VsZWN0IH0sCiAgcHJvcHM6IHsKICAgIGluZm86IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgdGFibGVzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgbWVudXM6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IFtdCiAgICB9LAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHN1YkNvbHVtbnM6IFtdLAogICAgICBydWxlczogewogICAgICAgIHRwbENhdGVnb3J5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup55Sf5oiQ5qih5p2/IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHBhY2thZ2VOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl55Sf5oiQ5YyF6Lev5b6EIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIG1vZHVsZU5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXnlJ/miJDmqKHlnZflkI0iLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgYnVzaW5lc3NOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl55Sf5oiQ5Lia5Yqh5ZCNIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGZ1bmN0aW9uTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeeUn+aIkOWKn+iDveWQjSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgfQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICAnaW5mby5zdWJUYWJsZU5hbWUnOiBmdW5jdGlvbih2YWwpIHsKICAgICAgdGhpcy5zZXRTdWJUYWJsZUNvbHVtbnModmFsKTsKICAgIH0sCiAgICAnaW5mby50cGxXZWJUeXBlJzogZnVuY3Rpb24odmFsKSB7CiAgICAgIGlmICh2YWwgPT09ICcnKSB7CiAgICAgICAgdGhpcy5pbmZvLnRwbFdlYlR5cGUgPSAiZWxlbWVudC11aSI7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDovazmjaLoj5zljZXmlbDmja7nu5PmnoQgKi8KICAgIG5vcm1hbGl6ZXIobm9kZSkgewogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsKICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbjsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIGlkOiBub2RlLm1lbnVJZCwKICAgICAgICBsYWJlbDogbm9kZS5tZW51TmFtZSwKICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbgogICAgICB9OwogICAgfSwKICAgIC8qKiDpgInmi6nlrZDooajlkI3op6blj5EgKi8KICAgIHN1YlNlbGVjdENoYW5nZSh2YWx1ZSkgewogICAgICB0aGlzLmluZm8uc3ViVGFibGVGa05hbWUgPSAnJzsKICAgIH0sCiAgICAvKiog6YCJ5oup55Sf5oiQ5qih5p2/6Kem5Y+RICovCiAgICB0cGxTZWxlY3RDaGFuZ2UodmFsdWUpIHsKICAgICAgaWYodmFsdWUgIT09ICdzdWInKSB7CiAgICAgICAgdGhpcy5pbmZvLnN1YlRhYmxlTmFtZSA9ICcnOwogICAgICAgIHRoaXMuaW5mby5zdWJUYWJsZUZrTmFtZSA9ICcnOwogICAgICB9CiAgICB9LAogICAgLyoqIOiuvue9ruWFs+iBlOWklumUriAqLwogICAgc2V0U3ViVGFibGVDb2x1bW5zKHZhbHVlKSB7CiAgICAgIGZvciAodmFyIGl0ZW0gaW4gdGhpcy50YWJsZXMpIHsKICAgICAgICBjb25zdCBuYW1lID0gdGhpcy50YWJsZXNbaXRlbV0udGFibGVOYW1lOwogICAgICAgIGlmICh2YWx1ZSA9PT0gbmFtZSkgewogICAgICAgICAgdGhpcy5zdWJDb2x1bW5zID0gdGhpcy50YWJsZXNbaXRlbV0uY29sdW1uczsKICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["genInfoForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "genInfoForm.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\n  <el-form ref=\"genInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\n    <el-row>\n      <el-col :span=\"12\">\n        <el-form-item prop=\"tplCategory\">\n          <span slot=\"label\">生成模板</span>\n          <el-select v-model=\"info.tplCategory\" @change=\"tplSelectChange\">\n            <el-option label=\"单表（增删改查）\" value=\"crud\" />\n            <el-option label=\"树表（增删改查）\" value=\"tree\" />\n            <el-option label=\"主子表（增删改查）\" value=\"sub\" />\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item prop=\"tplWebType\">\n          <span slot=\"label\">前端类型</span>\n          <el-select v-model=\"info.tplWebType\">\n            <el-option label=\"Vue2 Element UI 模版\" value=\"element-ui\" />\n            <el-option label=\"Vue3 Element Plus 模版\" value=\"element-plus\" />\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item prop=\"packageName\">\n          <span slot=\"label\">\n            生成包路径\n            <el-tooltip content=\"生成在哪个java包下，例如 com.ruoyi.system\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.packageName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"moduleName\">\n          <span slot=\"label\">\n            生成模块名\n            <el-tooltip content=\"可理解为子系统名，例如 system\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.moduleName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"businessName\">\n          <span slot=\"label\">\n            生成业务名\n            <el-tooltip content=\"可理解为功能英文名，例如 user\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.businessName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"functionName\">\n          <span slot=\"label\">\n            生成功能名\n            <el-tooltip content=\"用作类描述，例如 用户\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.functionName\" />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item prop=\"genType\">\n          <span slot=\"label\">\n            生成代码方式\n            <el-tooltip content=\"默认为zip压缩包下载，也可以自定义生成路径\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-radio v-model=\"info.genType\" label=\"0\">zip压缩包</el-radio>\n          <el-radio v-model=\"info.genType\" label=\"1\">自定义路径</el-radio>\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            上级菜单\n            <el-tooltip content=\"分配到指定菜单下，例如 系统管理\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <treeselect\n            :append-to-body=\"true\"\n            v-model=\"info.parentMenuId\"\n            :options=\"menus\"\n            :normalizer=\"normalizer\"\n            :show-count=\"true\"\n            placeholder=\"请选择系统菜单\"\n          />\n        </el-form-item>\n      </el-col>\n\n      <el-col :span=\"24\" v-if=\"info.genType == '1'\">\n        <el-form-item prop=\"genPath\">\n          <span slot=\"label\">\n            自定义路径\n            <el-tooltip content=\"填写磁盘绝对路径，若不填写，则生成到当前Web项目下\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-input v-model=\"info.genPath\">\n            <el-dropdown slot=\"append\">\n              <el-button type=\"primary\">\n                最近路径快速选择\n                <i class=\"el-icon-arrow-down el-icon--right\"></i>\n              </el-button>\n              <el-dropdown-menu slot=\"dropdown\">\n                <el-dropdown-item @click.native=\"info.genPath = '/'\">恢复默认的生成基础路径</el-dropdown-item>\n              </el-dropdown-menu>\n            </el-dropdown>\n          </el-input>\n        </el-form-item>\n      </el-col>\n    </el-row>\n\n    <el-row v-show=\"info.tplCategory == 'tree'\">\n      <h4 class=\"form-header\">其他信息</h4>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            树编码字段\n            <el-tooltip content=\"树显示的编码字段名， 如：dept_id\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.treeCode\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in info.columns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            树父编码字段\n            <el-tooltip content=\"树显示的父编码字段名， 如：parent_Id\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.treeParentCode\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in info.columns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            树名称字段\n            <el-tooltip content=\"树节点的显示名称字段名， 如：dept_name\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.treeName\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in info.columns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n    </el-row>\n    <el-row v-show=\"info.tplCategory == 'sub'\">\n      <h4 class=\"form-header\">关联信息</h4>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            关联子表的表名\n            <el-tooltip content=\"关联子表的表名， 如：sys_user\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.subTableName\" placeholder=\"请选择\" @change=\"subSelectChange\">\n            <el-option\n              v-for=\"(table, index) in tables\"\n              :key=\"index\"\n              :label=\"table.tableName + '：' + table.tableComment\"\n              :value=\"table.tableName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item>\n          <span slot=\"label\">\n            子表关联的外键名\n            <el-tooltip content=\"子表关联的外键名， 如：user_id\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n          </span>\n          <el-select v-model=\"info.subTableFkName\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"(column, index) in subColumns\"\n              :key=\"index\"\n              :label=\"column.columnName + '：' + column.columnComment\"\n              :value=\"column.columnName\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-col>\n    </el-row>\n  </el-form>\n</template>\n\n<script>\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\n\nexport default {\n  components: { Treeselect },\n  props: {\n    info: {\n      type: Object,\n      default: null\n    },\n    tables: {\n      type: Array,\n      default: null\n    },\n    menus: {\n      type: Array,\n      default: []\n    },\n  },\n  data() {\n    return {\n      subColumns: [],\n      rules: {\n        tplCategory: [\n          { required: true, message: \"请选择生成模板\", trigger: \"blur\" }\n        ],\n        packageName: [\n          { required: true, message: \"请输入生成包路径\", trigger: \"blur\" }\n        ],\n        moduleName: [\n          { required: true, message: \"请输入生成模块名\", trigger: \"blur\" }\n        ],\n        businessName: [\n          { required: true, message: \"请输入生成业务名\", trigger: \"blur\" }\n        ],\n        functionName: [\n          { required: true, message: \"请输入生成功能名\", trigger: \"blur\" }\n        ],\n      }\n    };\n  },\n  watch: {\n    'info.subTableName': function(val) {\n      this.setSubTableColumns(val);\n    },\n    'info.tplWebType': function(val) {\n      if (val === '') {\n        this.info.tplWebType = \"element-ui\";\n      }\n    }\n  },\n  methods: {\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.menuId,\n        label: node.menuName,\n        children: node.children\n      };\n    },\n    /** 选择子表名触发 */\n    subSelectChange(value) {\n      this.info.subTableFkName = '';\n    },\n    /** 选择生成模板触发 */\n    tplSelectChange(value) {\n      if(value !== 'sub') {\n        this.info.subTableName = '';\n        this.info.subTableFkName = '';\n      }\n    },\n    /** 设置关联外键 */\n    setSubTableColumns(value) {\n      for (var item in this.tables) {\n        const name = this.tables[item].tableName;\n        if (value === name) {\n          this.subColumns = this.tables[item].columns;\n          break;\n        }\n      }\n    }\n  }\n};\n</script>\n"]}]}