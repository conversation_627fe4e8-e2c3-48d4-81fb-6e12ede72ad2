{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\register.vue", "mtime": 1747038938000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoucmVnaXN0ZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBoZWlnaHQ6IDEwMCU7CiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCIuLi9hc3NldHMvaW1hZ2VzL2xvZ2luLWJhY2tncm91bmQuanBnIik7CiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjsKfQoudGl0bGUgewogIG1hcmdpbjogMHB4IGF1dG8gMzBweCBhdXRvOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogIzcwNzA3MDsKfQoKLnJlZ2lzdGVyLWZvcm0gewogIGJvcmRlci1yYWRpdXM6IDZweDsKICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogIHdpZHRoOiA0MDBweDsKICBwYWRkaW5nOiAyNXB4IDI1cHggNXB4IDI1cHg7CiAgLmVsLWlucHV0IHsKICAgIGhlaWdodDogMzhweDsKICAgIGlucHV0IHsKICAgICAgaGVpZ2h0OiAzOHB4OwogICAgfQogIH0KICAuaW5wdXQtaWNvbiB7CiAgICBoZWlnaHQ6IDM5cHg7CiAgICB3aWR0aDogMTRweDsKICAgIG1hcmdpbi1sZWZ0OiAycHg7CiAgfQp9Ci5yZWdpc3Rlci10aXAgewogIGZvbnQtc2l6ZTogMTNweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgY29sb3I6ICNiZmJmYmY7Cn0KLnJlZ2lzdGVyLWNvZGUgewogIHdpZHRoOiAzMyU7CiAgaGVpZ2h0OiAzOHB4OwogIGZsb2F0OiByaWdodDsKICBpbWcgewogICAgY3Vyc29yOiBwb2ludGVyOwogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKICB9Cn0KLmVsLXJlZ2lzdGVyLWZvb3RlciB7CiAgaGVpZ2h0OiA0MHB4OwogIGxpbmUtaGVpZ2h0OiA0MHB4OwogIHBvc2l0aW9uOiBmaXhlZDsKICBib3R0b206IDA7CiAgd2lkdGg6IDEwMCU7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGNvbG9yOiAjZmZmOwogIGZvbnQtZmFtaWx5OiBBcmlhbDsKICBmb250LXNpemU6IDEycHg7CiAgbGV0dGVyLXNwYWNpbmc6IDFweDsKfQoucmVnaXN0ZXItY29kZS1pbWcgewogIGhlaWdodDogMzhweDsKfQo="}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"register\">\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n      <h3 class=\"title\">金刚数瞳</h3>\n      <el-form-item prop=\"username\">\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"registerForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"confirmPassword\">\n        <el-input\n          v-model=\"registerForm.confirmPassword\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"确认密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"registerForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"register-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleRegister\"\n        >\n          <span v-if=\"!loading\">注 册</span>\n          <span v-else>注 册 中...</span>\n        </el-button>\n        <div style=\"float: right;\">\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\";\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    };\n  },\n  created() {\n    this.getCode();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\n          this.registerForm.uuid = res.uuid;\n        }\n      });\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username;\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/login\");\n            }).catch(() => {});\n          }).catch(() => {\n            this.loading = false;\n            if (this.captchaEnabled) {\n              this.getCode();\n            }\n          })\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.register-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.register-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.register-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.register-code-img {\n  height: 38px;\n}\n</style>\n"]}]}