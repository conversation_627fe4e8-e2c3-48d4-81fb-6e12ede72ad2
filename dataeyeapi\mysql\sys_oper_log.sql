INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (100, '菜单管理', 1, 'module_admin.controller.menu_controller.add_system_menu()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"parentId\": 0, \"menuName\": \"公司管理\", \"icon\": \"build\", \"menuType\": \"M\", \"orderNum\": 10, \"isFrame\": 1, \"isCache\": 0, \"visible\": \"0\", \"status\": \"0\", \"path\": \"/system/company\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-06-05T16:04:24.840809\"}', 0, '', '2025-06-05 16:04:25', 12);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (101, '菜单管理', 1, 'module_admin.controller.menu_controller.add_system_menu()', 'POST', 1, 'admin', '研发部门', '/system/menu', '127.0.0.1', '内网IP', '{\"parentId\": 2000, \"menuName\": \"公司列表\", \"menuType\": \"C\", \"orderNum\": 11, \"isFrame\": 1, \"isCache\": 0, \"visible\": \"0\", \"status\": \"0\", \"path\": \"company\", \"component\": \"system/company/index\", \"perms\": \"system:company:list\"}', '{\"code\": 200, \"msg\": \"新增成功\", \"success\": true, \"time\": \"2025-06-05T16:05:41.642098\"}', 0, '', '2025-06-05 16:05:42', 12);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (102, '个人信息', 2, 'module_admin.controller.user_controller.change_system_user_profile_info()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"userName\": \"admin\"}', '{\"code\": 500, \"msg\": \"module_admin.entity.vo.user_vo.EditUserModel() got multiple values for keyword argument \'userName\'\", \"success\": false, \"time\": \"2025-06-11T11:14:01.557038\"}', 1, 'module_admin.entity.vo.user_vo.EditUserModel() got multiple values for keyword argument \'userName\'', '2025-06-11 11:14:01', 23);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (103, '个人信息', 2, 'module_admin.controller.user_controller.change_system_user_profile_info()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"userName\": \"admin\"}', '{\"code\": 500, \"msg\": \"module_admin.entity.vo.user_vo.EditUserModel() got multiple values for keyword argument \'userName\'\", \"success\": false, \"time\": \"2025-06-11T11:15:09.803977\"}', 1, 'module_admin.entity.vo.user_vo.EditUserModel() got multiple values for keyword argument \'userName\'', '2025-06-11 11:15:10', 1);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (104, '个人信息', 2, 'module_admin.controller.user_controller.change_system_user_profile_info()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"userName\": \"admin\"}', '{\"code\": 500, \"msg\": \"module_admin.entity.vo.user_vo.EditUserModel() got multiple values for keyword argument \'userName\'\", \"success\": false, \"time\": \"2025-06-11T11:15:41.952036\"}', 1, 'module_admin.entity.vo.user_vo.EditUserModel() got multiple values for keyword argument \'userName\'', '2025-06-11 11:15:42', 1);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (105, '个人信息', 2, 'module_admin.controller.user_controller.change_system_user_profile_info()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"nickName\": \"超级管理员\", \"phonenumber\": \"***********\", \"email\": \"<EMAIL>\", \"sex\": \"0\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-06-11T11:18:05.132941\"}', 0, '', '2025-06-11 11:18:05', 47);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (106, '个人信息', 2, 'module_admin.controller.user_controller.change_system_user_profile_info()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"nickName\": \"超级管理员\", \"phonenumber\": \"***********\", \"email\": \"<EMAIL>\", \"sex\": \"0\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-06-11T11:18:21.773824\"}', 0, '', '2025-06-11 11:18:21', 41);
INSERT INTO `sys_oper_log`(`oper_id`, `title`, `business_type`, `method`, `request_method`, `operator_type`, `oper_name`, `dept_name`, `oper_url`, `oper_ip`, `oper_location`, `oper_param`, `json_result`, `status`, `error_msg`, `oper_time`, `cost_time`) VALUES (107, '个人信息', 2, 'module_admin.controller.user_controller.change_system_user_profile_info()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile', '127.0.0.1', '内网IP', '{\"nickName\": \"超级管理员\", \"phonenumber\": \"***********\", \"email\": \"<EMAIL>\", \"sex\": \"0\"}', '{\"code\": 200, \"msg\": \"更新成功\", \"success\": true, \"time\": \"2025-06-11T11:25:08.904232\"}', 0, '', '2025-06-11 11:25:08', 41);
