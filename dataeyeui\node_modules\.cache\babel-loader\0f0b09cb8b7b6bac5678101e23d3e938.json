{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\api\\question.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\api\\question.js", "mtime": 1749628015261}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkUXVlc3Rpb25TdGFyID0gYWRkUXVlc3Rpb25TdGFyOwpleHBvcnRzLmdldFF1ZXN0aW9uU3Rhckxpc3QgPSBnZXRRdWVzdGlvblN0YXJMaXN0OwpleHBvcnRzLnJlbW92ZVF1ZXN0aW9uU3RhciA9IHJlbW92ZVF1ZXN0aW9uU3RhcjsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOWFs+azqOmXrumimOebuOWFs0FQSQpmdW5jdGlvbiBnZXRRdWVzdGlvblN0YXJMaXN0KHBhcmFtcykgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2FwaS9xdWVzdGlvbl9zdGFycycsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQpmdW5jdGlvbiBhZGRRdWVzdGlvblN0YXIoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2FwaS9xdWVzdGlvbl9zdGFycycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpmdW5jdGlvbiByZW1vdmVRdWVzdGlvblN0YXIoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9hcGkvcXVlc3Rpb25fc3RhcnMvIi5jb25jYXQoaWQpLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getQuestionStarList", "params", "request", "url", "method", "addQuestionStar", "data", "removeQuestionStar", "id", "concat"], "sources": ["D:/jgst/dataeyeui/src/api/question.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 关注问题相关API\r\nexport function getQuestionStarList(params) {\r\n  return request({\r\n    url: '/api/question_stars',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\nexport function addQuestionStar(data) {\r\n  return request({\r\n    url: '/api/question_stars',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function removeQuestionStar(id) {\r\n  return request({\r\n    url: `/api/question_stars/${id}`,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AAEO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,kBAAkBA,CAACC,EAAE,EAAE;EACrC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,yBAAAM,MAAA,CAAyBD,EAAE,CAAE;IAChCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}