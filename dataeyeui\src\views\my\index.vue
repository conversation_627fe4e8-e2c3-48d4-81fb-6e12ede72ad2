<template>
  <div class="app-container">
    <div class="my-container">
      <!-- 左侧菜单 -->
      <div class="left-menu">
        <div class="menu-item" :class="{ active: activeMenu === 'address' }" @click="activeMenu = 'address'">
          <i class="el-icon-data-analysis"></i>
          <span>指标告警({{ menuCounts.address }})</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'f2code' }" @click="activeMenu = 'f2code'">
          <i class="el-icon-document"></i>
          <span>卡片提醒({{ menuCounts.f2code }})</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'questions' }" @click="activeMenu = 'questions'">
          <i class="el-icon-question"></i>
          <span>关注的问题({{ menuCounts.questions }})</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'edit' }" @click="activeMenu = 'edit'">
          <i class="el-icon-star-off"></i>
          <span>保存的卡片({{ menuCounts.edit }})</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'tags' }" @click="activeMenu = 'tags'">
          <i class="el-icon-time"></i>
          <span>搜索历史({{ menuCounts.tags }})</span>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="content-area">
        <el-skeleton v-if="loading" rows="6" animated />
        <el-alert v-if="error" :title="error" type="error" show-icon style="margin-bottom:16px" />

        <!-- 指标告警内容 -->
        <template v-if="activeMenu === 'address' && !loading && !error">
          <div class="saved-cards-header">
            <div class="title">指标告警</div>
          </div>
          <el-table :data="alertList" style="width:100%" border v-if="alertList.length">
            <el-table-column prop="name" label="告警名称" min-width="180" />
            <el-table-column prop="level" label="级别" width="100" />
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small">查看</el-button>
                <el-button type="text" size="small" @click="removeAlertRow(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-content"><div class="empty-text">暂无数据</div></div>
        </template>

        <!-- 卡片提醒内容 -->
        <template v-if="activeMenu === 'f2code' && !loading && !error">
          <div class="saved-cards-header">
            <div class="title">卡片记录</div>
          </div>
          <el-table :data="filteredCardShowList" style="width:100%" border v-if="filteredCardShowList.length">
            <el-table-column prop="name" label="监控卡片" min-width="180" />
            <el-table-column prop="createTime" label="触发条件" width="180" />
            <el-table-column prop="updateTime" label="计算频率" width="180" />
            <el-table-column prop="creator" label="提醒接受人" width="120" />
            <el-table-column prop="lastViewTime" label="最近触发时间" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small">查看</el-button>
                <el-button type="text" size="small" @click="removeCardRow(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-content"><div class="empty-text">暂无数据</div></div>
        </template>

        <!-- 关注的问题内容 -->
        <template v-if="activeMenu === 'questions' && !loading && !error">
          <div class="saved-cards-header">
            <div class="title">关注的问题</div>
            <div class="search-box">
              <el-input placeholder="在关注的问题中查找" prefix-icon="el-icon-search" v-model="questionSearchKeyword" clearable />
            </div>
          </div>
          <el-table :data="filteredQuestionList" style="width:100%" border v-if="filteredQuestionList.length">
            <el-table-column prop="question" label="问题" min-width="200" />
            <el-table-column prop="starTime" label="关注时间" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small">查看</el-button>
                <el-button type="text" size="small" @click="removeQuestionRow(scope.row)">取消关注</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-content"><div class="empty-text">暂无数据</div></div>
        </template>

        <!-- 保存的卡片内容 -->
        <template v-if="activeMenu === 'edit' && !loading && !error">
          <div class="saved-cards-header">
            <div class="title">保存的卡片</div>
            <div class="search-box">
              <el-input placeholder="在保存的卡片中查找" prefix-icon="el-icon-search" v-model="cardSearchKeyword" clearable />
            </div>
          </div>
          <el-table :data="filteredCardList" style="width:100%" border v-if="filteredCardList.length">
            <el-table-column prop="name" label="卡片名称" min-width="180" />
            <el-table-column prop="createTime" label="保存时间" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small">查看</el-button>
                <el-button type="text" size="small" @click="removeCardRow(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-content"><div class="empty-text">暂未保存任何卡片</div></div>
        </template>

        <!-- 搜索历史内容 -->
        <template v-if="activeMenu === 'tags' && !loading && !error">
          <div class="search-history-header-new">
            <div class="search-history-title">搜索历史 <span class="desc">保留最近1个月的记录</span></div>
            <el-input class="search-history-input" placeholder="在搜索历史中检索" prefix-icon="el-icon-search" v-model="searchKeyword" clearable />
          </div>
          <el-table :data="filteredHistoryList" class="search-history-table" style="width:100%" border v-if="filteredHistoryList.length">
            <el-table-column label="全部" min-width="70%">
              <template slot-scope="scope">
                <div class="history-item">{{ scope.row.content }}</div>
              </template>
            </el-table-column>
            <el-table-column label="修改时间" align="right" min-width="30%">
              <template slot-scope="scope">
                <div class="history-time">{{ scope.row.time }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="removeHistoryRow(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-content"><div class="empty-text">暂无历史记录</div></div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { getCardList, removeCard } from '@/api/card'
import { getAlertList, removeAlert } from '@/api/alert'
import { getQuestionStarList, removeQuestionStar } from '@/api/question'
import { getHistoryList, removeHistory } from '@/api/history'

export default {
  name: 'MyCenter',
  data() {
    return {
      activeMenu: 'address',
      searchKeyword: '',
      cardSearchKeyword: '',
      questionSearchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 数据区
      cardList: [],
      cardShowList: [],
      alertList: [],
      questionList: [],
      historyList: [],
      // 加载与异常状态
      loading: false,
      error: null
    }
  },
  computed: {
    menuCounts() {
      return {
        address: this.alertList.length,
        f2code: this.cardShowList.length,
        questions: this.questionList.length,
        edit: this.cardList.length,
        tags: this.historyList.length
      }
    },
    filteredCardList() {
      if (!this.cardSearchKeyword) return this.cardList
      return this.cardList.filter(item => (item.name || '').includes(this.cardSearchKeyword))
    },
    filteredCardShowList() {
      if (!this.cardSearchKeyword) return this.cardShowList
      return this.cardShowList.filter(item => (item.name || '').includes(this.cardSearchKeyword))
    },
    filteredQuestionList() {
      if (!this.questionSearchKeyword) return this.questionList
      return this.questionList.filter(item => (item.question || '').includes(this.questionSearchKeyword))
    },
    filteredHistoryList() {
      if (!this.searchKeyword) return this.historyList
      return this.historyList.filter(item => (item.content || '').includes(this.searchKeyword))
    }
  },
  created() {
    this.fetchAllData()
  },
  methods: {
    async fetchAllData() {
      this.loading = true
      this.error = null
      try {
        // 指标告警
        const alertRes = await getAlertList({ pageNum: 1, pageSize: 100 })
        this.alertList = alertRes.items || alertRes.rows || []
        // 卡片提醒/保存的卡片
        // 此处cardList即为数据库cards表的数据
        const cardRes = await getCardList({ pageNum: 1, pageSize: 100 })
        this.cardList = cardRes.items || cardRes.rows || []
        this.cardShowList = this.cardList // 视图分离后可做筛选
        // 关注的问题
        const questionRes = await getQuestionStarList({ pageNum: 1, pageSize: 100 })
        this.questionList = questionRes.items || questionRes.rows || []
        // 搜索历史
        const historyRes = await getHistoryList({ pageNum: 1, pageSize: 100 })
        this.historyList = historyRes.items || historyRes.rows || []
      } catch (e) {
        this.error = e.message || '数据加载失败'
      } finally {
        this.loading = false
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchAllData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchAllData()
    },
    async removeCardRow(row) {
      this.loading = true
      try {
        await removeCard(row.id || row.cardId)
        this.$message.success('删除成功')
        this.fetchAllData()
      } catch (e) {
        this.$message.error(e.message || '删除失败')
      } finally {
        this.loading = false
      }
    },
    async removeAlertRow(row) {
      this.loading = true
      try {
        await removeAlert(row.id || row.alertId)
        this.$message.success('删除成功')
        this.fetchAllData()
      } catch (e) {
        this.$message.error(e.message || '删除失败')
      } finally {
        this.loading = false
      }
    },
    async removeQuestionRow(row) {
      this.loading = true
      try {
        await removeQuestionStar(row.id || row.starId)
        this.$message.success('取消关注成功')
        this.fetchAllData()
      } catch (e) {
        this.$message.error(e.message || '操作失败')
      } finally {
        this.loading = false
      }
    },
    async removeHistoryRow(row) {
      this.loading = true
      try {
        await removeHistory(row.id || row.historyId)
        this.$message.success('删除成功')
        this.fetchAllData()
      } catch (e) {
        this.$message.error(e.message || '删除失败')
      } finally {
        this.loading = false
      }
    }
  }
</script>

<style scoped>
.app-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.my-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: calc(100vh - 40px);
}

/* 左侧菜单样式 */
.left-menu {
  width: 200px;
  border-right: 1px solid #ebeef5;
  padding: 20px 0;
}

.menu-item {
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #606266;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item.active {
  color: #409EFF;
  background-color: #ecf5ff;
}

.menu-item i {
  margin-right: 8px;
  font-size: 16px;
}

/* 右侧内容区样式 */
.content-area {
  flex: 1;
  padding: 20px;
  position: relative;
}

.header {
  margin-bottom: 20px;
}

.add-button {
  border-radius: 20px;
  padding: 8px 20px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.empty-icon i {
  font-size: 24px;
  color: #909399;
}

.empty-text {
  font-size: 14px;
}

/* 搜索历史样式 */
.search-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
}

.user-desc {
  font-size: 14px;
  color: #909399;
}

.search-box {
  width: 300px;
}

.search-history-list {
  margin-bottom: 20px;
}

.history-item {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  padding: 10px 0;
}

.history-time {
  font-size: 14px;
  color: #909399;
  padding: 10px 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 保存的卡片样式 */
.saved-cards-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.card-table-container {
  position: relative;
  min-height: 300px;
}

.empty-table {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
}

.link-text {
  color: #409EFF;
  text-decoration: none;
}

.link-text:hover {
  text-decoration: underline;
}

/* 新搜索历史样式 */
.search-history-header-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-history-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.desc {
  font-size: 14px;
  color: #909399;
  margin-left: 5px;
}

.search-history-input {
  width: 300px;
}

.search-history-table {
  margin-top: 10px;
}

.search-history-table .el-table__header {
  background-color: #f9f9f9;
  border-bottom: 1px solid #ebeef5;
}

.search-history-table .el-table__header th {
  font-size: 14px;
  color: #606266;
  padding: 12px 0;
  text-align: left;
}

.search-history-table .el-table__body td {
  font-size: 14px;
  color: #303133;
  padding: 10px 0;
}

.search-history-table .el-table__body tr:hover {
  background-color: #f5f7fa;
}
</style>
