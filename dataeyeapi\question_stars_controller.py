from fastapi import APIRouter, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from config.get_db import get_db
from sqlalchemy import func
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from sqlalchemy import Column, BigInteger, String, Text, DateTime
from config.database import Base
import datetime

class QuestionStar(Base):
    __tablename__ = 'question_stars'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='收藏ID')
    project_id = Column(BigInteger, nullable=False, comment='项目ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    question = Column(Text, nullable=False, comment='问题内容')
    tokens = Column(Text, comment='问题标记(JSON格式)')
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment='创建时间')

router = APIRouter()

@router.get("/api/question_stars")
async def get_question_stars(pageNum: int = Query(1, ge=1), pageSize: int = Query(10, ge=1, le=100), db: AsyncSession = Depends(get_db)):
    offset = (pageNum - 1) * pageSize
    stmt = select(QuestionStar).offset(offset).limit(pageSize)
    total_stmt = select(func.count(QuestionStar.id))
    result = await db.execute(stmt)
    items = result.scalars().all()
    total_result = await db.execute(total_stmt)
    total = total_result.scalar()
    def serialize(q):
        def format_datetime(dt):
            if not dt:
                return ''
            if isinstance(dt, str):
                # 兼容字符串类型
                return dt.replace('T', ' ').split('.')[0]
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        return {
            'id': q.id,
            'project_id': q.project_id,
            'user_id': q.user_id,
            'question': q.question,
            'tokens': q.tokens,
            'created_at': format_datetime(q.created_at),
            'starTime': format_datetime(q.created_at),  # 兼容前端“关注时间”字段
        }
    return {
        'items': [serialize(q) for q in items],
        'total': total,
        'pageNum': pageNum,
        'pageSize': pageSize
    }

@router.delete("/api/question_stars/{star_id}")
async def delete_question_star(star_id: int, db: AsyncSession = Depends(get_db)):
    stmt = select(QuestionStar).where(QuestionStar.id == star_id)
    result = await db.execute(stmt)
    obj = result.scalar_one_or_none()
    if not obj:
        return {"success": False, "msg": "未找到该关注问题"}
    await db.delete(obj)
    await db.commit()
    return {"success": True, "msg": "已取消关注"}
