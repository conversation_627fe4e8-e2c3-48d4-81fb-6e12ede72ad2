{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1748313690000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "name", "data", "searchForm", "keyword", "dataType", "store", "time", "showFilterPopup", "filterPopupStyle", "filterSearchQuery", "activeTab", "showMorePopup", "morePopupStyle", "showSharePopup", "embedEnabled", "shareLink", "showReminderPopup", "reminderForm", "cardName", "email", "changeType", "timePeriod", "threshold", "contentChange", "method", "showUploadPopup", "uploadForm", "reportName", "description", "file", "showChartSelector", "selectedChartType", "showSettingsPopup", "settingsPopupStyle", "storeRevenueChart", "cloudRevenueChart", "refreshInterval", "chatMessages", "type", "text", "chatInput", "suggestions", "icon", "isLoading", "searchHistory", "storeRevenueData", "categories", "revenue", "profit", "growthRate", "cloudRevenueData", "mounted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "document", "addEventListener", "handleClickOutside", "handleKeydown", "startRealTimeUpdate", "showKeyboardShortcuts", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "removeEventListener", "methods", "_this", "$nextTick", "initStoreRevenueChart", "initCloudRevenueChart", "_this2", "init", "$refs", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "legend", "show", "xAxis", "axisLabel", "rotate", "fontSize", "yAxis", "position", "formatter", "value", "toFixed", "series", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "label", "params", "yAxisIndex", "lineStyle", "width", "setOption", "on", "handleChartClick", "_this3", "handleClose", "console", "log", "$message", "info", "handleSearch", "handleSearchEnhanced", "handlePrevious", "handleNext", "handleFilter", "_this4", "setFilterPopupPosition", "filterButton", "rect", "getBoundingClientRect", "popup<PERSON><PERSON><PERSON>", "leftPosition", "right", "top", "bottom", "left", "Math", "max", "zIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectFilter", "item", "success", "concat", "event", "popup", "target", "closest", "contains", "moreButtons", "moreButton1", "moreButton2", "moreButton3", "morePopup", "clickedMoreButton", "some", "button", "closeMorePopup", "sharePopup", "closeSharePopup", "reminderPopup", "closeReminderPopup", "uploadPopup", "closeUploadPopup", "chartSelector", "closeChartSelector", "settingsPopup", "settingsButtons", "querySelectorAll", "clickedSettingsButton", "Array", "from", "closeSettingsPopup", "handleMoreClick", "_this5", "setMorePopupPosition", "buttonElement", "handleCardReminder", "handleShareCard", "handleSaveCard", "_this6", "loading", "setTimeout", "handleUploadCSV", "handleDownloadPNG", "exportData", "handleRefresh", "refreshChartData", "handleDownload", "handleSettings", "_this7", "setSettingsPopupPosition", "copyShareLink", "_this8", "navigator", "clipboard", "writeText", "then", "catch", "textArea", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "confirm<PERSON><PERSON><PERSON>", "warning", "emailRegex", "test", "handleFileSelect", "files", "toLowerCase", "endsWith", "confirmUpload", "trim", "openChartSelector", "selectChartType", "chartType", "selectChartIcon", "sendChatMessage", "_this9", "push", "getCurrentTime", "userMessage", "generateAIResponse", "response", "includes", "handleSuggestionClick", "suggestion", "now", "Date", "getHours", "toString", "padStart", "getMinutes", "_this0", "unshift", "length", "pop", "map", "val", "floor", "random", "format", "_this1", "门店营业额", "品牌营业额", "导出时间", "toLocaleString", "_this10", "interval", "parseInt", "setInterval", "ctrl<PERSON>ey", "key", "preventDefault", "closeAllPopups", "filterDataByCategory", "category", "_this11", "resetData", "_this12"], "sources": ["src/views/datasearch/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- 顶部搜索栏 -->\n    <div class=\"search-container\">\n      <div class=\"search-form\">\n        <div class=\"search-input-wrapper\">\n          <input\n            type=\"text\"\n            v-model=\"searchForm.keyword\"\n            class=\"search-input\"\n            placeholder=\"搜索 门店营业额 前十 门店 营业额\"\n          />\n        </div>\n        <div class=\"search-buttons\">\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleSearch\">\n            <i class=\"search-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handlePrevious\">\n            <i class=\"left-arrow-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleNext\">\n            <i class=\"right-arrow-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleFilter\" ref=\"filterButton\">\n            <i class=\"filter-icon\"></i>\n          </button>\n        </div>\n\n        <!-- 筛选弹窗 -->\n        <div v-if=\"showFilterPopup\" class=\"filter-popup\" :style=\"filterPopupStyle\">\n          <div class=\"popup-header\">\n            <span>数据</span>\n            <button class=\"popup-close\" @click=\"closeFilterPopup\">×</button>\n          </div>\n          <div class=\"popup-search\">\n            <input type=\"text\" class=\"search-input\" placeholder=\"搜索\" v-model=\"filterSearchQuery\">\n            <i class=\"search-icon\"></i>\n          </div>\n          <div class=\"popup-tabs\">\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '维度' }\" @click=\"activeTab = '维度'\">维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '时间维度' }\" @click=\"activeTab = '时间维度'\">时间维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '指标' }\" @click=\"activeTab = '指标'\">指标</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '分析' }\" @click=\"activeTab = '分析'\">分析</div>\n          </div>\n          <div class=\"popup-content\">\n            <div v-if=\"activeTab === '维度'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('门店')\">\n                <span>门店</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('品牌')\">\n                <span>品牌</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('等')\">\n                <span>等</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('综合分析')\">\n                <span>综合分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('门店营业额')\">\n                <span>门店营业额</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('数据分析')\">\n                <span>数据分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n            </div>\n            <div v-if=\"activeTab === '时间维度'\" class=\"tab-content\">\n              <div class=\"filter-item\">\n                <span>日期</span>\n                <i class=\"arrow-icon down\">v</i>\n              </div>\n              <div class=\"time-units-row\">\n                <span class=\"time-unit\" @click=\"selectFilter('日')\">日</span>\n                <span class=\"time-unit\" @click=\"selectFilter('周')\">周</span>\n                <span class=\"time-unit\" @click=\"selectFilter('月')\">月</span>\n                <span class=\"time-unit\" @click=\"selectFilter('季')\">季</span>\n                <span class=\"time-unit\" @click=\"selectFilter('年')\">年</span>\n              </div>\n              <div class=\"time-item\">当日</div>\n              <div class=\"time-item\">数天</div>\n              <div class=\"time-item\">数十天</div>\n              <div class=\"time-item\">数月</div>\n              <div class=\"time-item\">2月1日至16日</div>\n              <div class=\"time-item\">2月1日至今</div>\n            </div>\n            <div v-if=\"activeTab === '指标'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进店顾客')\">进店顾客</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客单')\">客单</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('利润')\">利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('销售额')\">销售额</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进货数量')\">进货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('退货数量')\">退货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('总价值')\">总价值</div>\n              <div class=\"filter-item\" @click=\"selectFilter('公司利润率')\">公司利润率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客户数量')\">客户数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('今日利润')\">今日利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('全店成本率')\">全店成本率</div>\n            </div>\n            <div v-if=\"activeTab === '分析'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('增长')\">增长</div>\n              <div class=\"filter-item\" @click=\"selectFilter('开店')\">开店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n              <div class=\"filter-item\" @click=\"selectFilter('成交率')\">成交率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 顶部：门店营业额前十的 + 智能助手 -->\n    <div class=\"top-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton1\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"storeRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <div class=\"header-actions\">\n            <button class=\"send-btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <i class=\"panel-close\">×</i>\n          </div>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"chat-messages\">\n            <div v-for=\"(message, index) in chatMessages\" :key=\"index\"\n                 :class=\"['message-item', message.type === 'user' ? 'user-message' : 'assistant-message']\">\n              <div v-if=\"message.type === 'assistant'\" class=\"message-avatar\">\n                <div class=\"avatar-circle\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z\" fill=\"#1890ff\"/>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"message-content\">\n                <div class=\"message-text\" v-html=\"message.text.replace(/\\n/g, '<br>')\"></div>\n                <div class=\"message-time\">{{ message.time }}</div>\n              </div>\n            </div>\n            <div v-if=\"chatMessages.length === 1\" v-for=\"suggestion in suggestions\" :key=\"suggestion.text\"\n                 class=\"suggestion-item\" @click=\"handleSuggestionClick(suggestion)\">\n              <div class=\"suggestion-icon\">{{ suggestion.icon }}</div>\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <div class=\"input-wrapper\">\n              <input type=\"text\" v-model=\"chatInput\" @keyup.enter=\"sendChatMessage\"\n                     placeholder=\"请输入您的问题...\" class=\"chat-input\">\n              <button class=\"input-send-btn\" @click=\"sendChatMessage\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"#1890ff\"/>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 中间：营业额同比 单独一行 -->\n    <div class=\"middle-section\">\n      <div class=\"value-card\">\n        <div class=\"value-header\">\n          <div class=\"value-title\">\n            <i class=\"chart-icon\"></i>\n            <span>营业额同比</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"value-meta\">\n            <span class=\"value-date\">2024-01-01 至 12-31</span>\n            <span class=\"value-type\">月报</span>\n          </div>\n          <div class=\"value-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton2\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n          </div>\n        </div>\n        <div class=\"value-content\">\n          <div class=\"value-main\">\n            <span class=\"value-label\">营业额(总) / 元</span>\n            <div class=\"value-number\">165.32<span class=\"value-unit\">亿</span></div>\n            <div class=\"value-change\">\n              <span class=\"change-text\">同比上期</span>\n              <span class=\"change-value positive\">+4.73%(+7.43亿)</span>\n              <i class=\"change-arrow up\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部：品牌门店营业额前十的 + 智能助手 -->\n    <div class=\"bottom-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>品牌门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton3\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"cloudRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <div class=\"header-actions\">\n            <button class=\"send-btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <i class=\"panel-close\">×</i>\n          </div>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"chat-messages\">\n            <div v-for=\"(message, index) in chatMessages\" :key=\"'bottom-' + index\"\n                 :class=\"['message-item', message.type === 'user' ? 'user-message' : 'assistant-message']\">\n              <div v-if=\"message.type === 'assistant'\" class=\"message-avatar\">\n                <div class=\"avatar-circle\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z\" fill=\"#1890ff\"/>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"message-content\">\n                <div class=\"message-text\" v-html=\"message.text.replace(/\\n/g, '<br>')\"></div>\n                <div class=\"message-time\">{{ message.time }}</div>\n              </div>\n            </div>\n            <div v-if=\"chatMessages.length === 1\" v-for=\"suggestion in suggestions\" :key=\"'bottom-' + suggestion.text\"\n                 class=\"suggestion-item\" @click=\"handleSuggestionClick(suggestion)\">\n              <div class=\"suggestion-icon\">{{ suggestion.icon }}</div>\n              <div class=\"suggestion-text\">{{ suggestion.text }}</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <div class=\"input-wrapper\">\n              <input type=\"text\" v-model=\"chatInput\" @keyup.enter=\"sendChatMessage\"\n                     placeholder=\"请输入您的问题...\" class=\"chat-input\">\n              <button class=\"input-send-btn\" @click=\"sendChatMessage\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"#1890ff\"/>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 更多操作弹窗 -->\n    <div v-if=\"showMorePopup\" class=\"more-popup\" :style=\"morePopupStyle\" @click.stop>\n      <div class=\"more-popup-content\">\n        <div class=\"more-action-item\" @click.stop=\"handleCardReminder\">卡片提醒</div>\n        <div class=\"more-action-item\" @click.stop=\"handleShareCard\">分享卡片</div>\n        <div class=\"more-action-item\" @click=\"handleSaveCard\">保存卡片</div>\n        <div class=\"more-action-item\" @click.stop=\"handleUploadCSV\">上传CSV</div>\n        <div class=\"more-action-item\" @click=\"handleDownloadPNG\">下载PNG</div>\n      </div>\n    </div>\n\n    <!-- 分享卡片弹窗 -->\n    <div v-if=\"showSharePopup\" class=\"share-popup-overlay\" @click=\"closeSharePopup\">\n      <div class=\"share-popup\" @click.stop>\n        <div class=\"share-popup-header\">\n          <span class=\"share-popup-title\">分享链接</span>\n          <button class=\"share-popup-close\" @click=\"closeSharePopup\">×</button>\n        </div>\n        <div class=\"share-popup-content\">\n          <div class=\"share-description\">\n            分享分析结果，让更多的人看到你的洞察\n          </div>\n          <div class=\"share-option\">\n            <div class=\"share-option-label\">\n              <span>代码嵌入功能</span>\n            </div>\n            <div class=\"share-toggle\">\n              <input type=\"checkbox\" id=\"embedToggle\" v-model=\"embedEnabled\" class=\"toggle-input\">\n              <label for=\"embedToggle\" class=\"toggle-label\"></label>\n            </div>\n          </div>\n          <div class=\"share-link-section\">\n            <input\n              type=\"text\"\n              class=\"share-link-input\"\n              :value=\"shareLink\"\n              readonly\n              placeholder=\"https://dwz.cn/jzwMdMh\"\n            >\n            <button class=\"copy-link-btn\" @click=\"copyShareLink\">复制链接</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 卡片提醒弹窗 -->\n    <div v-if=\"showReminderPopup\" class=\"reminder-popup-overlay\" @click=\"closeReminderPopup\">\n      <div class=\"reminder-popup\" @click.stop>\n        <div class=\"reminder-popup-header\">\n          <span class=\"reminder-popup-title\">卡片提醒设置</span>\n          <button class=\"reminder-popup-close\" @click=\"closeReminderPopup\">×</button>\n        </div>\n        <div class=\"reminder-popup-content\">\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒卡片</label>\n            <select class=\"reminder-select\" v-model=\"reminderForm.cardName\">\n              <option value=\"\">请选择卡片</option>\n              <option value=\"门店营业额前十\">门店营业额前十</option>\n              <option value=\"云营业额前十\">云营业额前十</option>\n            </select>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒邮箱地址</label>\n            <input\n              type=\"email\"\n              class=\"reminder-input\"\n              v-model=\"reminderForm.email\"\n              placeholder=\"请输入邮箱地址\"\n            >\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">数据变化</label>\n            <div class=\"reminder-change-section\">\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.changeType\">\n                <option value=\"同比增减幅\">同比增减幅/元</option>\n                <option value=\"环比增减幅\">环比增减幅/元</option>\n              </select>\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.timePeriod\">\n                <option value=\"天数\">天数(天)</option>\n                <option value=\"周数\">周数(周)</option>\n                <option value=\"月数\">月数(月)</option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <div class=\"reminder-threshold-section\">\n              <input\n                type=\"number\"\n                class=\"reminder-number-input\"\n                v-model=\"reminderForm.threshold\"\n                placeholder=\"0\"\n              >\n              <span class=\"reminder-unit\">元</span>\n              <div class=\"reminder-checkbox-section\">\n                <input\n                  type=\"checkbox\"\n                  id=\"contentChange\"\n                  v-model=\"reminderForm.contentChange\"\n                  class=\"reminder-checkbox\"\n                >\n                <label for=\"contentChange\" class=\"reminder-checkbox-label\">内容变化提醒</label>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"reminder-description\">\n            当选择指标比上月数据变化超过设定阈值时，发送邮件提醒\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒方式</label>\n            <div class=\"reminder-method-section\">\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"emailMethod\"\n                  value=\"email\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"emailMethod\" class=\"reminder-radio-label\">邮件提醒</label>\n              </div>\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"smsMethod\"\n                  value=\"sms\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"smsMethod\" class=\"reminder-radio-label\">短信提醒</label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"reminder-popup-footer\">\n          <button class=\"reminder-cancel-btn\" @click=\"closeReminderPopup\">取消</button>\n          <button class=\"reminder-confirm-btn\" @click=\"confirmReminder\">确定，设置提醒人</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 上传CSV弹窗 -->\n    <div v-if=\"showUploadPopup\" class=\"upload-popup-overlay\" @click=\"closeUploadPopup\">\n      <div class=\"upload-popup\" @click.stop>\n        <div class=\"upload-popup-header\">\n          <span class=\"upload-popup-title\">加入报告</span>\n          <button class=\"upload-popup-close\" @click=\"closeUploadPopup\">×</button>\n        </div>\n        <div class=\"upload-popup-content\">\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">报告名称</label>\n            <input\n              type=\"text\"\n              class=\"upload-input\"\n              v-model=\"uploadForm.reportName\"\n              placeholder=\"请输入报告名称\"\n            >\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">描述信息</label>\n            <textarea\n              class=\"upload-textarea\"\n              v-model=\"uploadForm.description\"\n              placeholder=\"请输入描述信息\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">上传文件</label>\n            <div class=\"upload-file-section\">\n              <input\n                type=\"file\"\n                accept=\".csv\"\n                @change=\"handleFileSelect\"\n                class=\"upload-file-input\"\n                id=\"csvFileInput\"\n              >\n              <label for=\"csvFileInput\" class=\"upload-file-button\">\n                选择文件\n              </label>\n              <span class=\"upload-file-name\" v-if=\"uploadForm.file\">\n                {{ uploadForm.file.name }}\n              </span>\n              <span class=\"upload-file-placeholder\" v-else>\n                请选择CSV文件\n              </span>\n            </div>\n          </div>\n\n          <div class=\"upload-tips\">\n            <div class=\"upload-tips-title\">上传说明：</div>\n            <div class=\"upload-tips-content\">\n              • 支持CSV格式文件<br>\n              • 文件大小不超过10MB<br>\n              • 请确保数据格式正确\n            </div>\n          </div>\n        </div>\n\n        <div class=\"upload-popup-footer\">\n          <button class=\"upload-cancel-btn\" @click=\"closeUploadPopup\">取消</button>\n          <button class=\"upload-confirm-btn\" @click=\"confirmUpload\">确定</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 设置弹窗 -->\n    <div v-if=\"showSettingsPopup\" class=\"settings-popup\" :style=\"settingsPopupStyle\" @click.stop>\n      <div class=\"settings-popup-content\">\n        <div class=\"chart-types-grid\">\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('bar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"12\" width=\"4\" height=\"9\" fill=\"#1890ff\"/>\n                <rect x=\"10\" y=\"8\" width=\"4\" height=\"13\" fill=\"#1890ff\"/>\n                <rect x=\"17\" y=\"4\" width=\"4\" height=\"17\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">柱状图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('line')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <circle cx=\"3\" cy=\"17\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"11\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"15\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"7\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">折线图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('pie')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 2V12L20.5 7.5C19.5 4.5 16 2 12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M12 12L20.5 16.5C19.5 19.5 16 22 12 22C7 22 3 18 3 12C3 7 7 3 12 3V12Z\" fill=\"#52c41a\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">饼图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('area')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7V21H3V17Z\" fill=\"#1890ff\" opacity=\"0.3\"/>\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">面积图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('scatter')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"5\" cy=\"18\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"16\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"17\" cy=\"8\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"14\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">散点图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('radar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <polygon points=\"12,2 20,8 20,16 12,22 4,16 4,8\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"none\"/>\n                <polygon points=\"12,6 16,9 16,15 12,18 8,15 8,9\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"#1890ff\" opacity=\"0.3\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">雷达图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('gauge')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <path d=\"M12 12L16 8\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">仪表盘</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('funnel')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M6 4H18L16 8H8L6 4Z\" fill=\"#1890ff\"/>\n                <path d=\"M8 8H16L14 12H10L8 8Z\" fill=\"#52c41a\"/>\n                <path d=\"M10 12H14L13 16H11L10 12Z\" fill=\"#faad14\"/>\n                <path d=\"M11 16H13V20H11V16Z\" fill=\"#f5222d\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">漏斗图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('heatmap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"3\" width=\"4\" height=\"4\" fill=\"#1890ff\"/>\n                <rect x=\"8\" y=\"3\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"13\" y=\"3\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"18\" y=\"3\" width=\"3\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"3\" y=\"8\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"8\" y=\"8\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"13\" y=\"8\" width=\"4\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"18\" y=\"8\" width=\"3\" height=\"4\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">热力图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('treemap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"2\" y=\"2\" width=\"10\" height=\"8\" fill=\"#1890ff\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"2\" width=\"9\" height=\"5\" fill=\"#52c41a\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"8\" width=\"9\" height=\"3\" fill=\"#faad14\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"2\" y=\"11\" width=\"6\" height=\"11\" fill=\"#f5222d\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"9\" y=\"11\" width=\"13\" height=\"11\" fill=\"#722ed1\" stroke=\"#fff\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">矩形树图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sunburst')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"none\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"6\" fill=\"none\" stroke=\"#52c41a\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#faad14\"/>\n                <path d=\"M12 2L14 6L12 6L10 6L12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M22 12L18 14L18 12L18 10L22 12Z\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">旭日图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sankey')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 6C2 6 8 6 12 10C16 14 22 14 22 14\" stroke=\"#1890ff\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 12C2 12 8 12 12 12C16 12 22 12 22 12\" stroke=\"#52c41a\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 18C2 18 8 18 12 14C16 10 22 10 22 10\" stroke=\"#faad14\" stroke-width=\"3\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">桑基图</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"DataSearch\",\n  data() {\n    return {\n      // 搜索表单数据\n      searchForm: {\n        keyword: '门店 营业额 前十 门店 营业额',\n        dataType: 'store',\n        store: 'all',\n        time: '2024'\n      },\n      // 筛选弹窗相关\n      showFilterPopup: false,\n      filterPopupStyle: {},\n      filterSearchQuery: '',\n      activeTab: '维度',\n      // 更多操作弹窗\n      showMorePopup: false,\n      morePopupStyle: {},\n      // 分享弹窗相关\n      showSharePopup: false,\n      embedEnabled: true,\n      shareLink: 'https://dwz.cn/jzwMdMh',\n\n      // 卡片提醒弹窗\n      showReminderPopup: false,\n      reminderForm: {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      },\n      // 上传CSV弹窗\n      showUploadPopup: false,\n      uploadForm: {\n        reportName: '',\n        description: '',\n        file: null\n      },\n      // 图表选择弹窗\n      showChartSelector: false,\n      selectedChartType: 'bar',\n      // 设置弹窗\n      showSettingsPopup: false,\n      settingsPopupStyle: {},\n      // 图表实例\n      storeRevenueChart: null,\n      cloudRevenueChart: null,\n      // 刷新间隔\n      refreshInterval: '0',\n      // 智能助手相关\n      chatMessages: [\n        {\n          type: 'assistant',\n          text: '根据当前数据表现？',\n          time: '刚刚'\n        }\n      ],\n      chatInput: '',\n      suggestions: [\n        { icon: '💡', text: '深圳门店营业额最高，有什么成功经验可以分享？' },\n        { icon: '📊', text: '如何提升其他门店的营业额？' },\n        { icon: '🎯', text: '引用数据分析' }\n      ],\n      // 数据加载状态\n      isLoading: false,\n      // 搜索历史\n      searchHistory: [\n        '门店 营业额 前十',\n        '品牌 销售额 排行',\n        '区域 业绩 对比'\n      ],\n      // 门店营业额数据\n      storeRevenueData: {\n        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],\n        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],\n        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],\n        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]\n      },\n      // 云营业额数据\n      cloudRevenueData: {\n        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],\n        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],\n        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],\n        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]\n      }\n    };\n  },\n  mounted() {\n    this.initCharts();\n    // 添加点击外部关闭弹窗的事件监听\n    document.addEventListener('click', this.handleClickOutside);\n    // 添加键盘快捷键\n    document.addEventListener('keydown', this.handleKeydown);\n    // 启动实时数据更新\n    this.startRealTimeUpdate();\n    // 显示快捷键提示\n    this.showKeyboardShortcuts();\n  },\n  beforeDestroy() {\n    if (this.storeRevenueChart) {\n      this.storeRevenueChart.dispose();\n    }\n    if (this.cloudRevenueChart) {\n      this.cloudRevenueChart.dispose();\n    }\n    // 移除事件监听\n    document.removeEventListener('click', this.handleClickOutside);\n    document.removeEventListener('keydown', this.handleKeydown);\n  },\n  methods: {\n    /** 初始化图表 */\n    initCharts() {\n      this.$nextTick(() => {\n        this.initStoreRevenueChart();\n        this.initCloudRevenueChart();\n      });\n    },\n\n    /** 初始化门店营业额图表 */\n    initStoreRevenueChart() {\n      this.storeRevenueChart = echarts.init(this.$refs.storeRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.storeRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                if (value >= 10000) {\n                  return (value / 10000).toFixed(1) + '万';\n                }\n                return value;\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.storeRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.storeRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.storeRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.storeRevenueChart.setOption(option);\n\n      // 添加点击事件\n      this.storeRevenueChart.on('click', (params) => {\n        this.handleChartClick(params);\n      });\n    },\n\n    /** 初始化云营业额图表 */\n    initCloudRevenueChart() {\n      this.cloudRevenueChart = echarts.init(this.$refs.cloudRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.cloudRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                return (value / 10000).toFixed(0) + '万';\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.cloudRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.cloudRevenueChart.setOption(option);\n\n      // 添加点击事件\n      this.cloudRevenueChart.on('click', (params) => {\n        this.handleChartClick(params);\n      });\n    },\n\n    // 搜索栏图标按钮方法\n    handleClose() {\n      console.log('关闭搜索');\n      this.$message.info('关闭搜索');\n    },\n\n    handleSearch() {\n      this.handleSearchEnhanced();\n    },\n\n    handlePrevious() {\n      console.log('上一页');\n      this.$message.info('上一页功能');\n    },\n\n    handleNext() {\n      console.log('下一页');\n      this.$message.info('下一页功能');\n    },\n\n    handleFilter() {\n      console.log('筛选功能');\n      this.showFilterPopup = !this.showFilterPopup;\n\n      if (this.showFilterPopup) {\n        this.$nextTick(() => {\n          this.setFilterPopupPosition();\n        });\n      }\n    },\n\n    // 设置弹窗位置\n    setFilterPopupPosition() {\n      const filterButton = this.$refs.filterButton;\n      if (filterButton) {\n        const rect = filterButton.getBoundingClientRect();\n        // 计算弹窗宽度（280px）和按钮位置，让弹窗右边缘与按钮右边缘对齐\n        const popupWidth = 280;\n        const leftPosition = rect.right - popupWidth;\n\n        this.filterPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 8 + 'px', // 增加一点间距\n          left: Math.max(10, leftPosition) + 'px', // 确保不会超出屏幕左边界\n          zIndex: 1000\n        };\n      }\n    },\n\n    // 关闭筛选弹窗\n    closeFilterPopup() {\n      this.showFilterPopup = false;\n    },\n\n    // 选择筛选项\n    selectFilter(item) {\n      console.log('选择筛选项:', item);\n      this.$message.success(`已选择: ${item}`);\n      this.closeFilterPopup();\n    },\n\n    // 点击外部关闭弹窗\n    handleClickOutside(event) {\n      // 处理筛选弹窗\n      if (this.showFilterPopup) {\n        const filterButton = this.$refs.filterButton;\n        const popup = event.target.closest('.filter-popup');\n\n        // 如果点击的不是筛选按钮也不是弹窗内部，则关闭弹窗\n        if (!filterButton?.contains(event.target) && !popup) {\n          this.closeFilterPopup();\n        }\n      }\n\n      // 处理更多操作弹窗\n      if (this.showMorePopup) {\n        const moreButtons = [\n          this.$refs.moreButton1,\n          this.$refs.moreButton2,\n          this.$refs.moreButton3\n        ];\n        const morePopup = event.target.closest('.more-popup');\n\n        // 检查是否点击了任何更多按钮\n        const clickedMoreButton = moreButtons.some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是更多按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedMoreButton && !morePopup) {\n          this.closeMorePopup();\n        }\n      }\n\n      // 处理分享弹窗\n      if (this.showSharePopup) {\n        const sharePopup = event.target.closest('.share-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!sharePopup) {\n          this.closeSharePopup();\n        }\n      }\n\n      // 处理卡片提醒弹窗\n      if (this.showReminderPopup) {\n        const reminderPopup = event.target.closest('.reminder-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!reminderPopup) {\n          this.closeReminderPopup();\n        }\n      }\n\n      // 处理上传CSV弹窗\n      if (this.showUploadPopup) {\n        const uploadPopup = event.target.closest('.upload-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!uploadPopup) {\n          this.closeUploadPopup();\n        }\n      }\n\n      // 处理图表选择器弹窗\n      if (this.showChartSelector) {\n        const chartSelector = event.target.closest('.chart-selector');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!chartSelector) {\n          this.closeChartSelector();\n        }\n      }\n\n      // 处理设置弹窗\n      if (this.showSettingsPopup) {\n        const settingsPopup = event.target.closest('.settings-popup');\n        const settingsButtons = document.querySelectorAll('.action-icon.settings');\n\n        // 检查是否点击了任何设置按钮\n        const clickedSettingsButton = Array.from(settingsButtons).some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是设置按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedSettingsButton && !settingsPopup) {\n          this.closeSettingsPopup();\n        }\n      }\n    },\n\n    // 更多操作相关方法\n    handleMoreClick(event) {\n      this.showMorePopup = true;\n\n      this.$nextTick(() => {\n        this.setMorePopupPosition(event.target);\n      });\n    },\n\n    // 设置更多弹窗位置\n    setMorePopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.morePopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 60 + 'px', // 向左偏移一些，让弹窗居中对齐按钮\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeMorePopup() {\n      this.showMorePopup = false;\n    },\n\n    handleCardReminder() {\n      this.showReminderPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleShareCard() {\n      this.showSharePopup = true;\n      this.closeMorePopup();\n      this.$message.success('分享弹窗已打开');\n    },\n\n    handleSaveCard() {\n      this.$message.loading('正在保存卡片...', 2);\n      setTimeout(() => {\n        this.$message.success('卡片已保存到我的收藏');\n      }, 2000);\n      this.closeMorePopup();\n    },\n\n    handleUploadCSV() {\n      this.showUploadPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleDownloadPNG() {\n      this.exportData('PNG');\n      this.closeMorePopup();\n    },\n\n    // 图表按钮处理方法\n    handleRefresh() {\n      console.log('刷新数据');\n      this.$message.loading('正在刷新数据...', 1);\n      this.refreshChartData();\n    },\n\n    handleDownload() {\n      this.exportData('Excel');\n    },\n\n    handleSettings(event) {\n      console.log('设置');\n      this.showSettingsPopup = true;\n\n      this.$nextTick(() => {\n        this.setSettingsPopupPosition(event.target);\n      });\n    },\n\n    // 分享弹窗相关方法\n    closeSharePopup() {\n      this.showSharePopup = false;\n    },\n\n    copyShareLink() {\n      // 复制链接到剪贴板\n      navigator.clipboard.writeText(this.shareLink).then(() => {\n        this.$message.success('链接已复制到剪贴板');\n      }).catch(() => {\n        // 降级方案\n        const textArea = document.createElement('textarea');\n        textArea.value = this.shareLink;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        this.$message.success('链接已复制到剪贴板');\n      });\n    },\n\n    // 卡片提醒弹窗方法\n    closeReminderPopup() {\n      this.showReminderPopup = false;\n      // 重置表单\n      this.reminderForm = {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      };\n    },\n\n    confirmReminder() {\n      // 验证表单\n      if (!this.reminderForm.cardName) {\n        this.$message.warning('请选择提醒卡片');\n        return;\n      }\n      if (!this.reminderForm.email) {\n        this.$message.warning('请输入邮箱地址');\n        return;\n      }\n\n      // 这里可以添加邮箱格式验证\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(this.reminderForm.email)) {\n        this.$message.warning('请输入正确的邮箱格式');\n        return;\n      }\n\n      console.log('设置卡片提醒:', this.reminderForm);\n      this.$message.success('卡片提醒设置成功！');\n      this.closeReminderPopup();\n    },\n\n    // 上传CSV弹窗方法\n    closeUploadPopup() {\n      this.showUploadPopup = false;\n      // 重置表单\n      this.uploadForm = {\n        reportName: '',\n        description: '',\n        file: null\n      };\n    },\n\n    handleFileSelect(event) {\n      const file = event.target.files[0];\n      if (file) {\n        // 检查文件类型\n        if (!file.name.toLowerCase().endsWith('.csv')) {\n          this.$message.warning('请选择CSV格式的文件');\n          event.target.value = '';\n          return;\n        }\n        this.uploadForm.file = file;\n      }\n    },\n\n    confirmUpload() {\n      // 验证表单\n      if (!this.uploadForm.reportName.trim()) {\n        this.$message.warning('请输入报告名称');\n        return;\n      }\n      if (!this.uploadForm.file) {\n        this.$message.warning('请选择要上传的CSV文件');\n        return;\n      }\n\n      console.log('上传CSV:', this.uploadForm);\n      this.$message.success('CSV文件上传成功！');\n      this.closeUploadPopup();\n    },\n\n    // 图表选择器相关方法\n    openChartSelector() {\n      this.showChartSelector = true;\n    },\n\n    closeChartSelector() {\n      this.showChartSelector = false;\n    },\n\n    selectChartType(chartType) {\n      this.selectedChartType = chartType;\n      console.log('选择图表类型:', chartType);\n      this.closeChartSelector();\n    },\n\n    // 设置弹窗相关方法\n    setSettingsPopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.settingsPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 100 + 'px',\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeSettingsPopup() {\n      this.showSettingsPopup = false;\n    },\n\n    selectChartIcon(chartType) {\n      console.log('选择图表类型:', chartType);\n      this.$message.success(`已选择图表类型: ${chartType}`);\n      this.closeSettingsPopup();\n    },\n\n    // 智能助手相关方法\n    sendChatMessage() {\n      if (!this.chatInput.trim()) return;\n\n      // 添加用户消息\n      this.chatMessages.push({\n        type: 'user',\n        text: this.chatInput,\n        time: this.getCurrentTime()\n      });\n\n      const userMessage = this.chatInput;\n      this.chatInput = '';\n\n      // 模拟AI回复\n      setTimeout(() => {\n        this.generateAIResponse(userMessage);\n      }, 1000);\n    },\n\n    generateAIResponse(userMessage) {\n      let response = '';\n\n      if (userMessage.includes('深圳') || userMessage.includes('成功经验')) {\n        response = '深圳门店表现优异，营业额达到2.134万元，主要成功因素包括：\\n\\n1. 地理位置优势 - 位于核心商圈\\n2. 客户群体消费能力强\\n3. 产品组合策略精准\\n4. 服务质量持续优化';\n      } else if (userMessage.includes('提升') || userMessage.includes('营业额')) {\n        response = '基于深圳门店的成功经验，建议其他门店：\\n\\n• 学习深圳门店的运营模式\\n• 根据当地市场调整产品结构\\n• 加强员工培训提升服务水平\\n• 优化店面布局和客户体验\\n• 制定针对性的营销策略';\n      } else if (userMessage.includes('数据分析') || userMessage.includes('分析')) {\n        response = '根据当前数据分析显示：\\n\\n• 华南地区（深圳、广州）表现最佳\\n• 华东地区存在下降趋势，需要关注\\n• 整体营业额同比增长4.73%\\n• 建议重点关注排名靠后的门店';\n      } else {\n        response = '感谢您的提问！我正在分析相关数据，为您提供专业的建议。您可以询问关于门店营业额、运营策略或数据分析的问题。';\n      }\n\n      this.chatMessages.push({\n        type: 'assistant',\n        text: response,\n        time: this.getCurrentTime()\n      });\n    },\n\n    handleSuggestionClick(suggestion) {\n      this.chatInput = suggestion.text;\n      this.sendChatMessage();\n    },\n\n    getCurrentTime() {\n      const now = new Date();\n      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;\n    },\n\n    // 搜索功能增强\n    handleSearchEnhanced() {\n      this.isLoading = true;\n      this.$message.loading('正在搜索数据...', 2);\n\n      // 模拟搜索延迟\n      setTimeout(() => {\n        this.isLoading = false;\n        this.$message.success('搜索完成！');\n\n        // 添加到搜索历史\n        if (this.searchForm.keyword && !this.searchHistory.includes(this.searchForm.keyword)) {\n          this.searchHistory.unshift(this.searchForm.keyword);\n          if (this.searchHistory.length > 5) {\n            this.searchHistory.pop();\n          }\n        }\n\n        // 刷新图表数据\n        this.refreshChartData();\n      }, 2000);\n    },\n\n    // 刷新图表数据\n    refreshChartData() {\n      // 模拟数据变化\n      this.storeRevenueData.revenue = this.storeRevenueData.revenue.map(val =>\n        val + Math.floor(Math.random() * 2000 - 1000)\n      );\n      this.storeRevenueData.profit = this.storeRevenueData.profit.map(val =>\n        val + Math.floor(Math.random() * 1500 - 750)\n      );\n      this.storeRevenueData.growthRate = this.storeRevenueData.growthRate.map(val =>\n        +(val + Math.random() * 2 - 1).toFixed(2)\n      );\n\n      // 重新渲染图表\n      this.initCharts();\n    },\n\n    // 数据导出功能\n    exportData(format) {\n      this.$message.loading(`正在导出${format}格式数据...`, 2);\n\n      setTimeout(() => {\n        this.$message.success(`${format}数据导出成功！`);\n\n        // 模拟下载\n        const data = {\n          门店营业额: this.storeRevenueData,\n          品牌营业额: this.cloudRevenueData,\n          导出时间: new Date().toLocaleString()\n        };\n\n        console.log('导出数据:', data);\n      }, 2000);\n    },\n\n    // 实时数据更新\n    startRealTimeUpdate() {\n      if (this.refreshInterval !== '0') {\n        const interval = parseInt(this.refreshInterval) * 1000;\n\n        setInterval(() => {\n          this.refreshChartData();\n          this.$message.info('数据已自动更新');\n        }, interval);\n\n        this.$message.success(`已开启${this.refreshInterval}秒自动刷新`);\n      }\n    },\n\n    // 键盘快捷键处理\n    handleKeydown(event) {\n      // Ctrl + Enter 执行搜索\n      if (event.ctrlKey && event.key === 'Enter') {\n        event.preventDefault();\n        this.handleSearch();\n      }\n      // F5 刷新数据\n      if (event.key === 'F5') {\n        event.preventDefault();\n        this.handleRefresh();\n      }\n      // Ctrl + S 保存卡片\n      if (event.ctrlKey && event.key === 's') {\n        event.preventDefault();\n        this.handleSaveCard();\n      }\n      // Ctrl + D 下载数据\n      if (event.ctrlKey && event.key === 'd') {\n        event.preventDefault();\n        this.handleDownload();\n      }\n      // Escape 关闭所有弹窗\n      if (event.key === 'Escape') {\n        this.closeAllPopups();\n      }\n    },\n\n    // 关闭所有弹窗\n    closeAllPopups() {\n      this.showFilterPopup = false;\n      this.showMorePopup = false;\n      this.showSharePopup = false;\n      this.showReminderPopup = false;\n      this.showUploadPopup = false;\n      this.showChartSelector = false;\n      this.showSettingsPopup = false;\n    },\n\n    // 图表交互增强\n    handleChartClick(params) {\n      console.log('图表点击事件:', params);\n      this.$message.info(`点击了: ${params.name} - ${params.value}`);\n    },\n\n    // 数据筛选功能\n    filterDataByCategory(category) {\n      this.$message.loading('正在筛选数据...', 1);\n\n      setTimeout(() => {\n        // 模拟数据筛选\n        if (category === '华南地区') {\n          this.storeRevenueData.categories = ['深圳', '广州'];\n          this.storeRevenueData.revenue = [21340, 16200];\n          this.storeRevenueData.profit = [22410, 18940];\n          this.storeRevenueData.growthRate = [11.39, 9.04];\n        } else if (category === '华东地区') {\n          this.storeRevenueData.categories = ['上海', '杭州', '南京'];\n          this.storeRevenueData.revenue = [8100, 7610, 6200];\n          this.storeRevenueData.profit = [12400, 7600, 6420];\n          this.storeRevenueData.growthRate = [7.60, 5.37, 5.04];\n        }\n\n        this.initCharts();\n        this.$message.success(`已筛选${category}数据`);\n      }, 1000);\n    },\n\n    // 重置数据\n    resetData() {\n      this.storeRevenueData = {\n        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],\n        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],\n        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],\n        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]\n      };\n\n      this.cloudRevenueData = {\n        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],\n        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],\n        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],\n        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]\n      };\n\n      this.initCharts();\n      this.$message.success('数据已重置');\n    },\n\n    // 显示快捷键提示\n    showKeyboardShortcuts() {\n      setTimeout(() => {\n        this.$message.info('💡 快捷键提示：Ctrl+Enter搜索，F5刷新，Ctrl+S保存，Ctrl+D下载，ESC关闭弹窗', 5);\n      }, 3000);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n// 搜索栏样式\n.search-container {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 4px;\n  padding: 12px 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n\n  .search-form {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .search-input-wrapper {\n      flex: 1;\n\n      .search-input {\n        width: 100%;\n        height: 32px;\n        padding: 4px 12px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        font-size: 14px;\n        color: #333;\n        background: #fff;\n        outline: none;\n        transition: border-color 0.3s;\n\n        &:focus {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        &::placeholder {\n          color: #999;\n        }\n      }\n    }\n\n    .search-buttons {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .btn-icon {\n        width: 32px;\n        height: 32px;\n        padding: 6px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        background: #fff;\n        cursor: pointer;\n        transition: all 0.3s;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n\n        &:hover {\n          background: #f5f5f5;\n          border-color: #40a9ff;\n        }\n\n        .close-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA0TDQgMTJMMTIgMTJMMTIgNFoiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;\n          background-size: contain;\n        }\n\n        .search-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTJDMy42ODYyOSAxMiAxIDkuMzEzNzEgMSA2QzEgMi42ODYyOSAzLjY4NjI5IDAgNyAwQzEwLjMxMzcgMCAxMyAyLjY4NjI5IDEzIDZDMTMgOS4zMTM3MSAxMC4zMTM3IDEyIDcgMTJaTTcgMTFDOS43NjE0MiAxMSAxMiA4Ljc2MTQyIDEyIDZDMTIgMy4yMzg1OCA5Ljc2MTQyIDEgNyAxQzQuMjM4NTggMSAyIDMuMjM4NTggMiA2QzIgOC43NjE0MiA0LjIzODU4IDExIDcgMTFaIiBmaWxsPSIjNjY2Ii8+CjxwYXRoIGQ9Ik0xMSAxMUwxNSAxNSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n          background-size: contain;\n        }\n\n        .left-arrow-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDEyTDYgOEwxMCA0IiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;\n          background-size: contain;\n        }\n\n        .right-arrow-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgNEwxMCA4TDYgMTIiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;\n          background-size: contain;\n        }\n\n        .filter-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgM0gxNEwxMCA3VjEzTDYgMTFWN0wyIDNaIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;\n          background-size: contain;\n        }\n      }\n    }\n  }\n}\n\n// 筛选弹窗样式\n.filter-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  width: 280px;\n  max-height: 450px;\n  overflow: hidden;\n\n  .popup-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 12px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n    border-radius: 6px 6px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .popup-close {\n      background: none;\n      border: none;\n      font-size: 16px;\n      color: #8c8c8c;\n      cursor: pointer;\n      padding: 0;\n      width: 20px;\n      height: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 2px;\n\n      &:hover {\n        background: #f5f5f5;\n        color: #595959;\n      }\n    }\n  }\n\n  .popup-search {\n    padding: 12px;\n    border-bottom: 1px solid #f0f0f0;\n    position: relative;\n\n    .search-input {\n      width: 100%;\n      padding: 6px 12px 6px 32px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      outline: none;\n      background: #ffffff;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 20px;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 14px;\n      height: 14px;\n      background: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"%23999\" stroke-width=\"2\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/></svg>') no-repeat center;\n      background-size: contain;\n    }\n  }\n\n  .popup-tabs {\n    display: flex;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n\n    .tab-item {\n      flex: 1;\n      padding: 8px 12px;\n      text-align: center;\n      font-size: 13px;\n      color: #595959;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 2px solid transparent;\n\n      &:hover {\n        color: #1890ff;\n        background: #f5f5f5;\n      }\n\n      &.active {\n        color: #1890ff;\n        background: #ffffff;\n        border-bottom-color: #1890ff;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .popup-content {\n    padding: 0;\n    max-height: 300px;\n    overflow-y: auto;\n\n    .tab-content {\n      .filter-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n\n        &:active {\n          background: #e6f7ff;\n          color: #1890ff;\n        }\n\n        .arrow-icon {\n          font-size: 12px;\n          color: #8c8c8c;\n          font-style: normal;\n        }\n      }\n\n      .time-units-row {\n        padding: 10px 16px;\n        border-bottom: 1px solid #f5f5f5;\n        display: flex;\n        gap: 16px;\n\n        .time-unit {\n          font-size: 14px;\n          color: #262626;\n          cursor: pointer;\n          transition: all 0.2s;\n          padding: 4px 8px;\n          border-radius: 4px;\n\n          &:hover {\n            background: #f0f8ff;\n            color: #1890ff;\n          }\n        }\n      }\n\n      .time-item {\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n      }\n    }\n  }\n}\n\n.search-form {\n  .form-row {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    flex-wrap: wrap;\n  }\n\n  .form-group {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .form-label {\n      font-size: 13px;\n      color: #595959;\n      font-weight: 400;\n      white-space: nowrap;\n      margin: 0;\n    }\n\n    .form-input {\n      height: 28px;\n      padding: 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff;\n      outline: none;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &.keyword-input {\n        width: 200px;\n      }\n\n      &.time-input {\n        width: 60px;\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n        font-size: 13px;\n      }\n    }\n\n    .form-select {\n      height: 28px;\n      padding: 0 20px 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\") no-repeat right 6px center/12px 12px;\n      outline: none;\n      appearance: none;\n      cursor: pointer;\n      min-width: 80px;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n    }\n  }\n\n  .form-buttons {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-left: auto;\n\n    .btn {\n      height: 28px;\n      padding: 0 12px;\n      border-radius: 4px;\n      font-size: 13px;\n      font-weight: 400;\n      border: 1px solid;\n      cursor: pointer;\n      outline: none;\n      transition: all 0.2s;\n      white-space: nowrap;\n\n      &.btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n        color: #ffffff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n\n      &.btn-default {\n        background: #ffffff;\n        border-color: #d9d9d9;\n        color: #595959;\n\n        &:hover {\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n      }\n\n      &.btn-success {\n        background: #52c41a;\n        border-color: #52c41a;\n        color: #ffffff;\n\n        &:hover {\n          background: #73d13d;\n          border-color: #73d13d;\n        }\n      }\n    }\n  }\n}\n\n// 顶部区域：门店营业额前十的 + 智能助手\n.top-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 中间区域：营业额同比 单独一行\n.middle-section {\n  width: 100%;\n}\n\n// 底部区域：品牌门店营业额前十的 + 智能助手\n.bottom-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 主图表区域\n.main-chart {\n  flex: 2.5; // 图表占据更多空间，比例约为 2.5:1\n  min-width: 0;\n}\n\n// 智能助手面板\n.assistant-panel {\n  width: 320px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  flex-shrink: 0;\n  display: flex;\n  flex-direction: column;\n  height: 400px; // 减少智能助手面板的高度\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    flex-shrink: 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .send-btn {\n        width: 28px;\n        height: 28px;\n        border-radius: 50%;\n        background: #1890ff;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.2s;\n\n        &:hover {\n          background: #40a9ff;\n          transform: scale(1.05);\n        }\n\n        &:active {\n          background: #096dd9;\n          transform: scale(0.95);\n        }\n      }\n\n      .panel-close {\n        cursor: pointer;\n        font-size: 16px;\n        color: #8c8c8c;\n\n        &:hover {\n          color: #262626;\n        }\n      }\n    }\n  }\n\n  .panel-content {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    overflow: hidden;\n\n    .chat-messages {\n      flex: 1;\n      padding: 16px;\n      overflow-y: auto;\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n\n      .message-item {\n        display: flex;\n        align-items: flex-start;\n        gap: 8px;\n\n        &.user-message {\n          flex-direction: row-reverse;\n\n          .message-content {\n            background: #1890ff;\n            color: white;\n            border-radius: 12px 12px 4px 12px;\n            max-width: 70%;\n          }\n        }\n\n        &.assistant-message {\n          .message-avatar {\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            background: #f0f8ff;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n\n            .avatar-circle {\n              width: 24px;\n              height: 24px;\n              border-radius: 50%;\n              background: #e6f7ff;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n            }\n          }\n\n          .message-content {\n            background: #f5f5f5;\n            color: #262626;\n            border-radius: 12px 12px 12px 4px;\n            max-width: 70%;\n          }\n        }\n\n        .message-content {\n          padding: 8px 12px;\n\n          .message-text {\n            font-size: 13px;\n            line-height: 1.4;\n            margin-bottom: 4px;\n          }\n\n          .message-time {\n            font-size: 11px;\n            opacity: 0.7;\n          }\n        }\n      }\n\n      .suggestion-item {\n        display: flex;\n        align-items: center;\n        padding: 8px 12px;\n        margin: 4px 0;\n        background: #f8f9fa;\n        border-radius: 6px;\n        cursor: pointer;\n        transition: background-color 0.2s;\n\n        &:hover {\n          background: #e9ecef;\n        }\n\n        .suggestion-icon {\n          margin-right: 8px;\n          font-size: 14px;\n        }\n\n        .suggestion-text {\n          font-size: 13px;\n          color: #666;\n        }\n      }\n    }\n\n    .input-area {\n      padding: 12px 16px;\n      border-top: 1px solid #f0f0f0;\n      background: #fafbfc;\n      flex-shrink: 0;\n\n      .input-wrapper {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        background: white;\n        border: 1px solid #d9d9d9;\n        border-radius: 20px;\n        padding: 6px 12px;\n\n        &:focus-within {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        .chat-input {\n          flex: 1;\n          border: none;\n          outline: none;\n          font-size: 13px;\n          padding: 4px 0;\n          background: transparent;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n        }\n\n        .input-send-btn {\n          width: 24px;\n          height: 24px;\n          border: none;\n          background: transparent;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n          transition: all 0.2s;\n\n          &:hover {\n            background: #f0f8ff;\n          }\n\n          &:active {\n            background: #e6f7ff;\n          }\n        }\n      }\n    }\n  }\n}\n\n.chart-card, .value-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.chart-header, .value-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.chart-title, .value-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n\n  .chart-icon {\n    width: 16px;\n    height: 16px;\n    background: #1890ff;\n    border-radius: 2px;\n  }\n\n  .help-icon {\n    width: 16px;\n    height: 16px;\n    background: #d9d9d9;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    color: #ffffff;\n    cursor: pointer;\n  }\n}\n\n.chart-meta, .value-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 12px;\n  color: #8c8c8c;\n\n  .chart-date, .value-date {\n    color: #595959;\n  }\n\n  .chart-type, .value-type {\n    background: #f0f0f0;\n    padding: 2px 6px;\n    border-radius: 2px;\n  }\n\n  .chart-source {\n    color: #1890ff;\n  }\n}\n\n.chart-actions, .value-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  .action-icon {\n    width: 16px;\n    height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    background-size: 12px 12px;\n    background-position: center;\n    background-repeat: no-repeat;\n    transition: all 0.2s;\n\n    &.refresh {\n      background-color: #52c41a;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #73d13d;\n      }\n    }\n\n    &.download {\n      background-color: #1890ff;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #40a9ff;\n      }\n    }\n\n    &.more {\n      background-color: #8c8c8c;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #a6a6a6;\n      }\n    }\n\n    &.settings {\n      background-color: #722ed1;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3crect x='3' y='12' width='4' height='9'/%3e%3crect x='10' y='8' width='4' height='13'/%3e%3crect x='17' y='4' width='4' height='17'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #9254de;\n      }\n    }\n\n    &.close {\n      background-color: #ff4d4f;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #ff7875;\n      }\n    }\n  }\n\n  .chart-status {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-left: 8px;\n  }\n}\n\n.chart-content {\n  padding: 20px;\n}\n\n.chart-legend {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 16px;\n  font-size: 12px;\n\n  .legend-item {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .legend-color {\n      width: 12px;\n      height: 12px;\n      border-radius: 2px;\n\n      &.blue {\n        background: #5B8FF9;\n      }\n\n      &.yellow {\n        background: #FFD666;\n      }\n\n      &.line {\n        background: #FF6B6B;\n        border-radius: 50%;\n        width: 8px;\n        height: 8px;\n      }\n    }\n  }\n}\n\n.chart-wrapper {\n  width: 100%;\n  height: 300px;\n}\n\n.chart {\n  width: 100%;\n  height: 100%;\n}\n\n.value-content {\n  padding: 20px;\n}\n\n.value-main {\n  .value-label {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .value-number {\n    font-size: 36px;\n    font-weight: bold;\n    color: #262626;\n    line-height: 1;\n    margin-bottom: 12px;\n\n    .value-unit {\n      font-size: 18px;\n      color: #8c8c8c;\n      margin-left: 4px;\n    }\n  }\n\n  .value-change {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    font-size: 12px;\n\n    .change-text {\n      color: #8c8c8c;\n    }\n\n    .change-value {\n      &.positive {\n        color: #52c41a;\n      }\n\n      &.negative {\n        color: #ff4d4f;\n      }\n    }\n\n    .change-arrow {\n      width: 0;\n      height: 0;\n\n      &.up {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-bottom: 6px solid #52c41a;\n      }\n\n      &.down {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-top: 6px solid #ff4d4f;\n      }\n    }\n  }\n}\n\n.control-panel {\n  position: fixed;\n  right: 20px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 280px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    border-radius: 8px 8px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .panel-close {\n      cursor: pointer;\n      font-size: 16px;\n      color: #8c8c8c;\n    }\n  }\n\n  .panel-content {\n    padding: 20px;\n\n    .panel-section {\n      h4 {\n        margin: 0 0 12px 0;\n        font-size: 14px;\n        color: #262626;\n      }\n\n      .setting-item {\n        margin-bottom: 16px;\n\n        label {\n          display: block;\n          margin-bottom: 6px;\n          font-size: 12px;\n          color: #8c8c8c;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .search-container {\n    .search-form {\n      .form-row {\n        gap: 12px;\n      }\n\n      .form-group {\n        .form-input.keyword-input {\n          width: 160px;\n        }\n      }\n\n      .form-buttons {\n        margin-left: 0;\n        margin-top: 8px;\n        width: 100%;\n        justify-content: flex-start;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1; // 智能助手面板在移动端显示在图表上方\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-container {\n    padding: 10px;\n    gap: 15px;\n  }\n\n  .search-container {\n    .search-form {\n      .form-row {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 12px;\n      }\n\n      .form-group {\n        width: 100%;\n\n        .form-input {\n          flex: 1;\n          min-width: 120px;\n\n          &.keyword-input {\n            width: 100%;\n          }\n        }\n\n        .form-select {\n          flex: 1;\n          min-width: 120px;\n        }\n      }\n\n      .form-buttons {\n        width: 100%;\n        justify-content: center;\n        margin-top: 12px;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1;\n\n    .panel-content {\n      padding: 15px;\n\n      .assistant-item {\n        padding: 10px 0;\n\n        .assistant-icon {\n          width: 28px;\n          height: 28px;\n          font-size: 16px;\n        }\n\n        .assistant-text {\n          font-size: 13px;\n        }\n      }\n    }\n  }\n\n  .chart-header, .value-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n    padding: 12px 16px;\n  }\n\n  .chart-meta, .value-meta {\n    order: 1;\n  }\n\n  .chart-actions, .value-actions {\n    order: 2;\n    align-self: flex-end;\n  }\n\n  .chart-content {\n    padding: 16px;\n  }\n\n  .value-content {\n    padding: 16px;\n  }\n\n  .chart-wrapper {\n    height: 250px;\n  }\n\n  .value-number {\n    font-size: 28px !important;\n\n    .value-unit {\n      font-size: 14px !important;\n    }\n  }\n}\n\n// 更多操作弹窗样式\n.more-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  min-width: 160px;\n  overflow: hidden;\n  position: fixed;\n  z-index: 2000;\n\n  .more-popup-content {\n    .more-action-item {\n      padding: 12px 16px;\n      font-size: 14px;\n      color: #262626;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 1px solid #f5f5f5;\n      line-height: 1.4;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover {\n        background: #f0f8ff;\n        color: #1890ff;\n      }\n\n      &:active {\n        background: #e6f7ff;\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n// 分享弹窗样式\n.share-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-popup {\n  background: white;\n  border-radius: 8px;\n  width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.share-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.share-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.share-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.share-popup-content {\n  padding: 20px;\n}\n\n.share-description {\n  color: #666;\n  font-size: 14px;\n  margin-bottom: 20px;\n  line-height: 1.5;\n}\n\n.share-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.share-option-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.share-toggle {\n  position: relative;\n}\n\n.toggle-input {\n  display: none;\n}\n\n.toggle-label {\n  display: block;\n  width: 44px;\n  height: 24px;\n  background: #ddd;\n  border-radius: 12px;\n  cursor: pointer;\n  position: relative;\n  transition: background 0.3s;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 2px;\n    left: 2px;\n    width: 20px;\n    height: 20px;\n    background: white;\n    border-radius: 50%;\n    transition: transform 0.3s;\n  }\n}\n\n.toggle-input:checked + .toggle-label {\n  background: #1890ff;\n\n  &::after {\n    transform: translateX(20px);\n  }\n}\n\n.share-link-section {\n  display: flex;\n  gap: 8px;\n}\n\n.share-link-input {\n  flex: 1;\n  height: 36px;\n  padding: 0 12px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n  background: #f5f5f5;\n  outline: none;\n}\n\n.copy-link-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 卡片提醒弹窗样式\n.reminder-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 10000 !important;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.reminder-popup {\n  background: white;\n  border-radius: 8px;\n  width: 520px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  z-index: 10001 !important;\n  position: relative;\n}\n\n.reminder-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.reminder-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.reminder-popup-content {\n  padding: 20px;\n}\n\n.reminder-form-item {\n  margin-bottom: 16px;\n\n  .reminder-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .reminder-select {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    cursor: pointer;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n  }\n\n  .reminder-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .reminder-change-section {\n    display: flex;\n    gap: 12px;\n\n    .reminder-select-small {\n      flex: 1;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n      cursor: pointer;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n  }\n\n  .reminder-threshold-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .reminder-number-input {\n      width: 120px;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n\n    .reminder-unit {\n      font-size: 14px;\n      color: #666;\n    }\n\n    .reminder-checkbox-section {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      margin-left: auto;\n\n      .reminder-checkbox {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-checkbox-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n\n  .reminder-method-section {\n    display: flex;\n    gap: 20px;\n\n    .reminder-radio-item {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n\n      .reminder-radio {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-radio-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n}\n\n.reminder-description {\n  font-size: 13px;\n  color: #999;\n  margin: 16px 0;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  line-height: 1.5;\n}\n\n.reminder-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.reminder-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 上传CSV弹窗样式\n.upload-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-popup {\n  background: white;\n  border-radius: 8px;\n  width: 480px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.upload-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.upload-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.upload-popup-content {\n  padding: 20px;\n}\n\n.upload-form-item {\n  margin-bottom: 16px;\n\n  .upload-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .upload-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-textarea {\n    width: 100%;\n    padding: 8px 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    resize: vertical;\n    min-height: 80px;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-file-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .upload-file-input {\n      display: none;\n    }\n\n    .upload-file-button {\n      height: 36px;\n      padding: 0 16px;\n      background: #1890ff;\n      border: none;\n      border-radius: 4px;\n      color: white;\n      font-size: 14px;\n      cursor: pointer;\n      transition: background 0.3s;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:hover {\n        background: #40a9ff;\n      }\n\n      &:active {\n        background: #096dd9;\n      }\n    }\n\n    .upload-file-name {\n      font-size: 14px;\n      color: #333;\n      flex: 1;\n    }\n\n    .upload-file-placeholder {\n      font-size: 14px;\n      color: #bfbfbf;\n      flex: 1;\n    }\n  }\n}\n\n.upload-tips {\n  margin-top: 20px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n\n  .upload-tips-title {\n    font-size: 14px;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 8px;\n  }\n\n  .upload-tips-content {\n    font-size: 13px;\n    color: #666;\n    line-height: 1.6;\n  }\n}\n\n.upload-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.upload-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 设置弹窗样式\n.settings-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  padding: 8px;\n  overflow: hidden;\n\n  .settings-popup-content {\n    .chart-types-grid {\n      display: grid;\n      grid-template-columns: repeat(4, 1fr);\n      gap: 8px;\n\n      .chart-type-item {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 8px 4px;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-radius: 4px;\n        min-height: 60px;\n\n        &:hover {\n          background: #f0f8ff;\n          transform: translateY(-2px);\n        }\n\n        &:active {\n          background: #e6f7ff;\n          transform: translateY(0);\n        }\n\n        .chart-icon {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 4px;\n\n          svg {\n            width: 20px;\n            height: 20px;\n            transition: all 0.2s;\n          }\n        }\n\n        .chart-label {\n          font-size: 12px;\n          color: #262626;\n          text-align: center;\n          line-height: 1.2;\n          white-space: nowrap;\n        }\n\n        &:hover {\n          .chart-icon svg {\n            transform: scale(1.1);\n          }\n\n          .chart-label {\n            color: #1890ff;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAurBA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACA;MACAC,eAAA;MACAC,gBAAA;MACAC,iBAAA;MACAC,SAAA;MACA;MACAC,aAAA;MACAC,cAAA;MACA;MACAC,cAAA;MACAC,YAAA;MACAC,SAAA;MAEA;MACAC,iBAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;QACAC,aAAA;QACAC,MAAA;MACA;MACA;MACAC,eAAA;MACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,IAAA;MACA;MACA;MACAC,iBAAA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,kBAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;MACA;MACAC,eAAA;MACA;MACAC,YAAA,GACA;QACAC,IAAA;QACAC,IAAA;QACAjC,IAAA;MACA,EACA;MACAkC,SAAA;MACAC,WAAA,GACA;QAAAC,IAAA;QAAAH,IAAA;MAAA,GACA;QAAAG,IAAA;QAAAH,IAAA;MAAA,GACA;QAAAG,IAAA;QAAAH,IAAA;MAAA,EACA;MACA;MACAI,SAAA;MACA;MACAC,aAAA,GACA,aACA,aACA,WACA;MACA;MACAC,gBAAA;QACAC,UAAA;QACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA;MACAC,gBAAA;QACAJ,UAAA;QACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA;IACA;IACAC,QAAA,CAAAC,gBAAA,eAAAC,kBAAA;IACA;IACAF,QAAA,CAAAC,gBAAA,iBAAAE,aAAA;IACA;IACA,KAAAC,mBAAA;IACA;IACA,KAAAC,qBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAzB,iBAAA;MACA,KAAAA,iBAAA,CAAA0B,OAAA;IACA;IACA,SAAAzB,iBAAA;MACA,KAAAA,iBAAA,CAAAyB,OAAA;IACA;IACA;IACAP,QAAA,CAAAQ,mBAAA,eAAAN,kBAAA;IACAF,QAAA,CAAAQ,mBAAA,iBAAAL,aAAA;EACA;EACAM,OAAA;IACA,YACAV,UAAA,WAAAA,WAAA;MAAA,IAAAW,KAAA;MACA,KAAAC,SAAA;QACAD,KAAA,CAAAE,qBAAA;QACAF,KAAA,CAAAG,qBAAA;MACA;IACA;IAEA,iBACAD,qBAAA,WAAAA,sBAAA;MAAA,IAAAE,MAAA;MACA,KAAAjC,iBAAA,GAAArC,OAAA,CAAAuE,IAAA,MAAAC,KAAA,CAAAnC,iBAAA;MAEA,IAAAoC,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAnC,IAAA;YACAoC,UAAA;cACAC,KAAA;YACA;UACA;QACA;QACAC,MAAA;UACAC,IAAA;QACA;QACAC,KAAA,GACA;UACAxC,IAAA;UACArC,IAAA,OAAA4C,gBAAA,CAAAC,UAAA;UACA2B,WAAA;YACAnC,IAAA;UACA;UACAyC,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA,EACA;QACAC,KAAA,GACA;UACA5C,IAAA;UACAtC,IAAA;UACAmF,QAAA;UACAJ,SAAA;YACAK,SAAA,WAAAA,UAAAC,KAAA;cACA,IAAAA,KAAA;gBACA,QAAAA,KAAA,UAAAC,OAAA;cACA;cACA,OAAAD,KAAA;YACA;UACA;QACA,GACA;UACA/C,IAAA;UACAtC,IAAA;UACAmF,QAAA;UACAJ,SAAA;YACAK,SAAA;UACA;QACA,EACA;QACAG,MAAA,GACA;UACAvF,IAAA;UACAsC,IAAA;UACArC,IAAA,OAAA4C,gBAAA,CAAAE,OAAA;UACAyC,SAAA;YACAb,KAAA;UACA;UACAc,QAAA;UACAC,KAAA;YACAb,IAAA;YACAM,QAAA;YACAC,SAAA,WAAAA,UAAAO,MAAA;cACA,QAAAA,MAAA,CAAAN,KAAA,UAAAC,OAAA;YACA;YACAL,QAAA;UACA;QACA,GACA;UACAjF,IAAA;UACAsC,IAAA;UACArC,IAAA,OAAA4C,gBAAA,CAAAG,MAAA;UACAwC,SAAA;YACAb,KAAA;UACA;UACAc,QAAA;UACAC,KAAA;YACAb,IAAA;YACAM,QAAA;YACAC,SAAA,WAAAA,UAAAO,MAAA;cACA,QAAAA,MAAA,CAAAN,KAAA,UAAAC,OAAA;YACA;YACAL,QAAA;UACA;QACA,GACA;UACAjF,IAAA;UACAsC,IAAA;UACAsD,UAAA;UACA3F,IAAA,OAAA4C,gBAAA,CAAAI,UAAA;UACAuC,SAAA;YACAb,KAAA;UACA;UACAkB,SAAA;YACAC,KAAA;UACA;UACAJ,KAAA;YACAb,IAAA;YACAM,QAAA;YACAC,SAAA;YACAH,QAAA;UACA;QACA;MAEA;MAEA,KAAA/C,iBAAA,CAAA6D,SAAA,CAAAzB,MAAA;;MAEA;MACA,KAAApC,iBAAA,CAAA8D,EAAA,oBAAAL,MAAA;QACAxB,MAAA,CAAA8B,gBAAA,CAAAN,MAAA;MACA;IACA;IAEA,gBACAzB,qBAAA,WAAAA,sBAAA;MAAA,IAAAgC,MAAA;MACA,KAAA/D,iBAAA,GAAAtC,OAAA,CAAAuE,IAAA,MAAAC,KAAA,CAAAlC,iBAAA;MAEA,IAAAmC,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAnC,IAAA;YACAoC,UAAA;cACAC,KAAA;YACA;UACA;QACA;QACAC,MAAA;UACAC,IAAA;QACA;QACAC,KAAA,GACA;UACAxC,IAAA;UACArC,IAAA,OAAAiD,gBAAA,CAAAJ,UAAA;UACA2B,WAAA;YACAnC,IAAA;UACA;UACAyC,SAAA;YACAC,MAAA;YACAC,QAAA;UACA;QACA,EACA;QACAC,KAAA,GACA;UACA5C,IAAA;UACAtC,IAAA;UACAmF,QAAA;UACAJ,SAAA;YACAK,SAAA,WAAAA,UAAAC,KAAA;cACA,QAAAA,KAAA,UAAAC,OAAA;YACA;UACA;QACA,GACA;UACAhD,IAAA;UACAtC,IAAA;UACAmF,QAAA;UACAJ,SAAA;YACAK,SAAA;UACA;QACA,EACA;QACAG,MAAA,GACA;UACAvF,IAAA;UACAsC,IAAA;UACArC,IAAA,OAAAiD,gBAAA,CAAAH,OAAA;UACAyC,SAAA;YACAb,KAAA;UACA;UACAc,QAAA;UACAC,KAAA;YACAb,IAAA;YACAM,QAAA;YACAC,SAAA,WAAAA,UAAAO,MAAA;cACA,QAAAA,MAAA,CAAAN,KAAA,UAAAC,OAAA;YACA;YACAL,QAAA;UACA;QACA,GACA;UACAjF,IAAA;UACAsC,IAAA;UACArC,IAAA,OAAAiD,gBAAA,CAAAF,MAAA;UACAwC,SAAA;YACAb,KAAA;UACA;UACAc,QAAA;UACAC,KAAA;YACAb,IAAA;YACAM,QAAA;YACAC,SAAA,WAAAA,UAAAO,MAAA;cACA,QAAAA,MAAA,CAAAN,KAAA,UAAAC,OAAA;YACA;YACAL,QAAA;UACA;QACA,GACA;UACAjF,IAAA;UACAsC,IAAA;UACAsD,UAAA;UACA3F,IAAA,OAAAiD,gBAAA,CAAAD,UAAA;UACAuC,SAAA;YACAb,KAAA;UACA;UACAkB,SAAA;YACAC,KAAA;UACA;UACAJ,KAAA;YACAb,IAAA;YACAM,QAAA;YACAC,SAAA;YACAH,QAAA;UACA;QACA;MAEA;MAEA,KAAA9C,iBAAA,CAAA4D,SAAA,CAAAzB,MAAA;;MAEA;MACA,KAAAnC,iBAAA,CAAA6D,EAAA,oBAAAL,MAAA;QACAO,MAAA,CAAAD,gBAAA,CAAAN,MAAA;MACA;IACA;IAEA;IACAQ,WAAA,WAAAA,YAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MACA,KAAAC,oBAAA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACAN,OAAA,CAAAC,GAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEAI,UAAA,WAAAA,WAAA;MACAP,OAAA,CAAAC,GAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEAK,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACAT,OAAA,CAAAC,GAAA;MACA,KAAA9F,eAAA,SAAAA,eAAA;MAEA,SAAAA,eAAA;QACA,KAAAyD,SAAA;UACA6C,MAAA,CAAAC,sBAAA;QACA;MACA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAA;MACA,IAAAC,YAAA,QAAA1C,KAAA,CAAA0C,YAAA;MACA,IAAAA,YAAA;QACA,IAAAC,IAAA,GAAAD,YAAA,CAAAE,qBAAA;QACA;QACA,IAAAC,UAAA;QACA,IAAAC,YAAA,GAAAH,IAAA,CAAAI,KAAA,GAAAF,UAAA;QAEA,KAAA1G,gBAAA;UACA2E,QAAA;UACAkC,GAAA,EAAAL,IAAA,CAAAM,MAAA;UAAA;UACAC,IAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAN,YAAA;UAAA;UACAO,MAAA;QACA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAApH,eAAA;IACA;IAEA;IACAqH,YAAA,WAAAA,aAAAC,IAAA;MACAzB,OAAA,CAAAC,GAAA,WAAAwB,IAAA;MACA,KAAAvB,QAAA,CAAAwB,OAAA,wBAAAC,MAAA,CAAAF,IAAA;MACA,KAAAF,gBAAA;IACA;IAEA;IACApE,kBAAA,WAAAA,mBAAAyE,KAAA;MACA;MACA,SAAAzH,eAAA;QACA,IAAAwG,YAAA,QAAA1C,KAAA,CAAA0C,YAAA;QACA,IAAAkB,KAAA,GAAAD,KAAA,CAAAE,MAAA,CAAAC,OAAA;;QAEA;QACA,MAAApB,YAAA,aAAAA,YAAA,eAAAA,YAAA,CAAAqB,QAAA,CAAAJ,KAAA,CAAAE,MAAA,OAAAD,KAAA;UACA,KAAAN,gBAAA;QACA;MACA;;MAEA;MACA,SAAAhH,aAAA;QACA,IAAA0H,WAAA,IACA,KAAAhE,KAAA,CAAAiE,WAAA,EACA,KAAAjE,KAAA,CAAAkE,WAAA,EACA,KAAAlE,KAAA,CAAAmE,WAAA,CACA;QACA,IAAAC,SAAA,GAAAT,KAAA,CAAAE,MAAA,CAAAC,OAAA;;QAEA;QACA,IAAAO,iBAAA,GAAAL,WAAA,CAAAM,IAAA,WAAAC,MAAA;UAAA,OACAA,MAAA,IAAAA,MAAA,CAAAR,QAAA,CAAAJ,KAAA,CAAAE,MAAA;QAAA,CACA;;QAEA;QACA,KAAAQ,iBAAA,KAAAD,SAAA;UACA,KAAAI,cAAA;QACA;MACA;;MAEA;MACA,SAAAhI,cAAA;QACA,IAAAiI,UAAA,GAAAd,KAAA,CAAAE,MAAA,CAAAC,OAAA;QACA;QACA,KAAAW,UAAA;UACA,KAAAC,eAAA;QACA;MACA;;MAEA;MACA,SAAA/H,iBAAA;QACA,IAAAgI,aAAA,GAAAhB,KAAA,CAAAE,MAAA,CAAAC,OAAA;QACA;QACA,KAAAa,aAAA;UACA,KAAAC,kBAAA;QACA;MACA;;MAEA;MACA,SAAAxH,eAAA;QACA,IAAAyH,WAAA,GAAAlB,KAAA,CAAAE,MAAA,CAAAC,OAAA;QACA;QACA,KAAAe,WAAA;UACA,KAAAC,gBAAA;QACA;MACA;;MAEA;MACA,SAAArH,iBAAA;QACA,IAAAsH,aAAA,GAAApB,KAAA,CAAAE,MAAA,CAAAC,OAAA;QACA;QACA,KAAAiB,aAAA;UACA,KAAAC,kBAAA;QACA;MACA;;MAEA;MACA,SAAArH,iBAAA;QACA,IAAAsH,aAAA,GAAAtB,KAAA,CAAAE,MAAA,CAAAC,OAAA;QACA,IAAAoB,eAAA,GAAAlG,QAAA,CAAAmG,gBAAA;;QAEA;QACA,IAAAC,qBAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAJ,eAAA,EAAAZ,IAAA,WAAAC,MAAA;UAAA,OACAA,MAAA,IAAAA,MAAA,CAAAR,QAAA,CAAAJ,KAAA,CAAAE,MAAA;QAAA,CACA;;QAEA;QACA,KAAAuB,qBAAA,KAAAH,aAAA;UACA,KAAAM,kBAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA7B,KAAA;MAAA,IAAA8B,MAAA;MACA,KAAAnJ,aAAA;MAEA,KAAAqD,SAAA;QACA8F,MAAA,CAAAC,oBAAA,CAAA/B,KAAA,CAAAE,MAAA;MACA;IACA;IAEA;IACA6B,oBAAA,WAAAA,qBAAAC,aAAA;MACA,IAAAA,aAAA;QACA,IAAAhD,IAAA,GAAAgD,aAAA,CAAA/C,qBAAA;QACA,KAAArG,cAAA;UACAuE,QAAA;UACAkC,GAAA,EAAAL,IAAA,CAAAM,MAAA;UACAC,IAAA,EAAAP,IAAA,CAAAO,IAAA;UAAA;UACAG,MAAA;QACA;MACA;IACA;IAEAmB,cAAA,WAAAA,eAAA;MACA,KAAAlI,aAAA;IACA;IAEAsJ,kBAAA,WAAAA,mBAAA;MACA,KAAAjJ,iBAAA;MACA,KAAA6H,cAAA;IACA;IAEAqB,eAAA,WAAAA,gBAAA;MACA,KAAArJ,cAAA;MACA,KAAAgI,cAAA;MACA,KAAAvC,QAAA,CAAAwB,OAAA;IACA;IAEAqC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,QAAA,CAAA+D,OAAA;MACAC,UAAA;QACAF,MAAA,CAAA9D,QAAA,CAAAwB,OAAA;MACA;MACA,KAAAe,cAAA;IACA;IAEA0B,eAAA,WAAAA,gBAAA;MACA,KAAA9I,eAAA;MACA,KAAAoH,cAAA;IACA;IAEA2B,iBAAA,WAAAA,kBAAA;MACA,KAAAC,UAAA;MACA,KAAA5B,cAAA;IACA;IAEA;IACA6B,aAAA,WAAAA,cAAA;MACAtE,OAAA,CAAAC,GAAA;MACA,KAAAC,QAAA,CAAA+D,OAAA;MACA,KAAAM,gBAAA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACA,KAAAH,UAAA;IACA;IAEAI,cAAA,WAAAA,eAAA7C,KAAA;MAAA,IAAA8C,MAAA;MACA1E,OAAA,CAAAC,GAAA;MACA,KAAArE,iBAAA;MAEA,KAAAgC,SAAA;QACA8G,MAAA,CAAAC,wBAAA,CAAA/C,KAAA,CAAAE,MAAA;MACA;IACA;IAEA;IACAa,eAAA,WAAAA,gBAAA;MACA,KAAAlI,cAAA;IACA;IAEAmK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA;MACAC,SAAA,CAAAC,SAAA,CAAAC,SAAA,MAAArK,SAAA,EAAAsK,IAAA;QACAJ,MAAA,CAAA3E,QAAA,CAAAwB,OAAA;MACA,GAAAwD,KAAA;QACA;QACA,IAAAC,QAAA,GAAAlI,QAAA,CAAAmI,aAAA;QACAD,QAAA,CAAAlG,KAAA,GAAA4F,MAAA,CAAAlK,SAAA;QACAsC,QAAA,CAAAoI,IAAA,CAAAC,WAAA,CAAAH,QAAA;QACAA,QAAA,CAAAI,MAAA;QACAtI,QAAA,CAAAuI,WAAA;QACAvI,QAAA,CAAAoI,IAAA,CAAAI,WAAA,CAAAN,QAAA;QACAN,MAAA,CAAA3E,QAAA,CAAAwB,OAAA;MACA;IACA;IAEA;IACAmB,kBAAA,WAAAA,mBAAA;MACA,KAAAjI,iBAAA;MACA;MACA,KAAAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;QACAC,aAAA;QACAC,MAAA;MACA;IACA;IAEAsK,eAAA,WAAAA,gBAAA;MACA;MACA,UAAA7K,YAAA,CAAAC,QAAA;QACA,KAAAoF,QAAA,CAAAyF,OAAA;QACA;MACA;MACA,UAAA9K,YAAA,CAAAE,KAAA;QACA,KAAAmF,QAAA,CAAAyF,OAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA;MACA,KAAAA,UAAA,CAAAC,IAAA,MAAAhL,YAAA,CAAAE,KAAA;QACA,KAAAmF,QAAA,CAAAyF,OAAA;QACA;MACA;MAEA3F,OAAA,CAAAC,GAAA,iBAAApF,YAAA;MACA,KAAAqF,QAAA,CAAAwB,OAAA;MACA,KAAAmB,kBAAA;IACA;IAEA;IACAE,gBAAA,WAAAA,iBAAA;MACA,KAAA1H,eAAA;MACA;MACA,KAAAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,IAAA;MACA;IACA;IAEAqK,gBAAA,WAAAA,iBAAAlE,KAAA;MACA,IAAAnG,IAAA,GAAAmG,KAAA,CAAAE,MAAA,CAAAiE,KAAA;MACA,IAAAtK,IAAA;QACA;QACA,KAAAA,IAAA,CAAA7B,IAAA,CAAAoM,WAAA,GAAAC,QAAA;UACA,KAAA/F,QAAA,CAAAyF,OAAA;UACA/D,KAAA,CAAAE,MAAA,CAAA7C,KAAA;UACA;QACA;QACA,KAAA3D,UAAA,CAAAG,IAAA,GAAAA,IAAA;MACA;IACA;IAEAyK,aAAA,WAAAA,cAAA;MACA;MACA,UAAA5K,UAAA,CAAAC,UAAA,CAAA4K,IAAA;QACA,KAAAjG,QAAA,CAAAyF,OAAA;QACA;MACA;MACA,UAAArK,UAAA,CAAAG,IAAA;QACA,KAAAyE,QAAA,CAAAyF,OAAA;QACA;MACA;MAEA3F,OAAA,CAAAC,GAAA,gBAAA3E,UAAA;MACA,KAAA4E,QAAA,CAAAwB,OAAA;MACA,KAAAqB,gBAAA;IACA;IAEA;IACAqD,iBAAA,WAAAA,kBAAA;MACA,KAAA1K,iBAAA;IACA;IAEAuH,kBAAA,WAAAA,mBAAA;MACA,KAAAvH,iBAAA;IACA;IAEA2K,eAAA,WAAAA,gBAAAC,SAAA;MACA,KAAA3K,iBAAA,GAAA2K,SAAA;MACAtG,OAAA,CAAAC,GAAA,YAAAqG,SAAA;MACA,KAAArD,kBAAA;IACA;IAEA;IACA0B,wBAAA,WAAAA,yBAAAf,aAAA;MACA,IAAAA,aAAA;QACA,IAAAhD,IAAA,GAAAgD,aAAA,CAAA/C,qBAAA;QACA,KAAAhF,kBAAA;UACAkD,QAAA;UACAkC,GAAA,EAAAL,IAAA,CAAAM,MAAA;UACAC,IAAA,EAAAP,IAAA,CAAAO,IAAA;UACAG,MAAA;QACA;MACA;IACA;IAEAkC,kBAAA,WAAAA,mBAAA;MACA,KAAA5H,iBAAA;IACA;IAEA2K,eAAA,WAAAA,gBAAAD,SAAA;MACAtG,OAAA,CAAAC,GAAA,YAAAqG,SAAA;MACA,KAAApG,QAAA,CAAAwB,OAAA,gDAAAC,MAAA,CAAA2E,SAAA;MACA,KAAA9C,kBAAA;IACA;IAEA;IACAgD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,UAAArK,SAAA,CAAA+J,IAAA;;MAEA;MACA,KAAAlK,YAAA,CAAAyK,IAAA;QACAxK,IAAA;QACAC,IAAA,OAAAC,SAAA;QACAlC,IAAA,OAAAyM,cAAA;MACA;MAEA,IAAAC,WAAA,QAAAxK,SAAA;MACA,KAAAA,SAAA;;MAEA;MACA8H,UAAA;QACAuC,MAAA,CAAAI,kBAAA,CAAAD,WAAA;MACA;IACA;IAEAC,kBAAA,WAAAA,mBAAAD,WAAA;MACA,IAAAE,QAAA;MAEA,IAAAF,WAAA,CAAAG,QAAA,UAAAH,WAAA,CAAAG,QAAA;QACAD,QAAA;MACA,WAAAF,WAAA,CAAAG,QAAA,UAAAH,WAAA,CAAAG,QAAA;QACAD,QAAA;MACA,WAAAF,WAAA,CAAAG,QAAA,YAAAH,WAAA,CAAAG,QAAA;QACAD,QAAA;MACA;QACAA,QAAA;MACA;MAEA,KAAA7K,YAAA,CAAAyK,IAAA;QACAxK,IAAA;QACAC,IAAA,EAAA2K,QAAA;QACA5M,IAAA,OAAAyM,cAAA;MACA;IACA;IAEAK,qBAAA,WAAAA,sBAAAC,UAAA;MACA,KAAA7K,SAAA,GAAA6K,UAAA,CAAA9K,IAAA;MACA,KAAAqK,eAAA;IACA;IAEAG,cAAA,WAAAA,eAAA;MACA,IAAAO,GAAA,OAAAC,IAAA;MACA,UAAAxF,MAAA,CAAAuF,GAAA,CAAAE,QAAA,GAAAC,QAAA,GAAAC,QAAA,eAAA3F,MAAA,CAAAuF,GAAA,CAAAK,UAAA,GAAAF,QAAA,GAAAC,QAAA;IACA;IAEA;IACAjH,oBAAA,WAAAA,qBAAA;MAAA,IAAAmH,MAAA;MACA,KAAAjL,SAAA;MACA,KAAA2D,QAAA,CAAA+D,OAAA;;MAEA;MACAC,UAAA;QACAsD,MAAA,CAAAjL,SAAA;QACAiL,MAAA,CAAAtH,QAAA,CAAAwB,OAAA;;QAEA;QACA,IAAA8F,MAAA,CAAA1N,UAAA,CAAAC,OAAA,KAAAyN,MAAA,CAAAhL,aAAA,CAAAuK,QAAA,CAAAS,MAAA,CAAA1N,UAAA,CAAAC,OAAA;UACAyN,MAAA,CAAAhL,aAAA,CAAAiL,OAAA,CAAAD,MAAA,CAAA1N,UAAA,CAAAC,OAAA;UACA,IAAAyN,MAAA,CAAAhL,aAAA,CAAAkL,MAAA;YACAF,MAAA,CAAAhL,aAAA,CAAAmL,GAAA;UACA;QACA;;QAEA;QACAH,MAAA,CAAAjD,gBAAA;MACA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAA9H,gBAAA,CAAAE,OAAA,QAAAF,gBAAA,CAAAE,OAAA,CAAAiL,GAAA,WAAAC,GAAA;QAAA,OACAA,GAAA,GAAAzG,IAAA,CAAA0G,KAAA,CAAA1G,IAAA,CAAA2G,MAAA;MAAA,CACA;MACA,KAAAtL,gBAAA,CAAAG,MAAA,QAAAH,gBAAA,CAAAG,MAAA,CAAAgL,GAAA,WAAAC,GAAA;QAAA,OACAA,GAAA,GAAAzG,IAAA,CAAA0G,KAAA,CAAA1G,IAAA,CAAA2G,MAAA;MAAA,CACA;MACA,KAAAtL,gBAAA,CAAAI,UAAA,QAAAJ,gBAAA,CAAAI,UAAA,CAAA+K,GAAA,WAAAC,GAAA;QAAA,OACA,EAAAA,GAAA,GAAAzG,IAAA,CAAA2G,MAAA,YAAA7I,OAAA;MAAA,CACA;;MAEA;MACA,KAAAlC,UAAA;IACA;IAEA;IACAqH,UAAA,WAAAA,WAAA2D,MAAA;MAAA,IAAAC,MAAA;MACA,KAAA/H,QAAA,CAAA+D,OAAA,4BAAAtC,MAAA,CAAAqG,MAAA;MAEA9D,UAAA;QACA+D,MAAA,CAAA/H,QAAA,CAAAwB,OAAA,IAAAC,MAAA,CAAAqG,MAAA;;QAEA;QACA,IAAAnO,IAAA;UACAqO,KAAA,EAAAD,MAAA,CAAAxL,gBAAA;UACA0L,KAAA,EAAAF,MAAA,CAAAnL,gBAAA;UACAsL,IAAA,MAAAjB,IAAA,GAAAkB,cAAA;QACA;QAEArI,OAAA,CAAAC,GAAA,UAAApG,IAAA;MACA;IACA;IAEA;IACAwD,mBAAA,WAAAA,oBAAA;MAAA,IAAAiL,OAAA;MACA,SAAAtM,eAAA;QACA,IAAAuM,QAAA,GAAAC,QAAA,MAAAxM,eAAA;QAEAyM,WAAA;UACAH,OAAA,CAAA/D,gBAAA;UACA+D,OAAA,CAAApI,QAAA,CAAAC,IAAA;QACA,GAAAoI,QAAA;QAEA,KAAArI,QAAA,CAAAwB,OAAA,sBAAAC,MAAA,MAAA3F,eAAA;MACA;IACA;IAEA;IACAoB,aAAA,WAAAA,cAAAwE,KAAA;MACA;MACA,IAAAA,KAAA,CAAA8G,OAAA,IAAA9G,KAAA,CAAA+G,GAAA;QACA/G,KAAA,CAAAgH,cAAA;QACA,KAAAxI,YAAA;MACA;MACA;MACA,IAAAwB,KAAA,CAAA+G,GAAA;QACA/G,KAAA,CAAAgH,cAAA;QACA,KAAAtE,aAAA;MACA;MACA;MACA,IAAA1C,KAAA,CAAA8G,OAAA,IAAA9G,KAAA,CAAA+G,GAAA;QACA/G,KAAA,CAAAgH,cAAA;QACA,KAAA7E,cAAA;MACA;MACA;MACA,IAAAnC,KAAA,CAAA8G,OAAA,IAAA9G,KAAA,CAAA+G,GAAA;QACA/G,KAAA,CAAAgH,cAAA;QACA,KAAApE,cAAA;MACA;MACA;MACA,IAAA5C,KAAA,CAAA+G,GAAA;QACA,KAAAE,cAAA;MACA;IACA;IAEA;IACAA,cAAA,WAAAA,eAAA;MACA,KAAA1O,eAAA;MACA,KAAAI,aAAA;MACA,KAAAE,cAAA;MACA,KAAAG,iBAAA;MACA,KAAAS,eAAA;MACA,KAAAK,iBAAA;MACA,KAAAE,iBAAA;IACA;IAEA;IACAiE,gBAAA,WAAAA,iBAAAN,MAAA;MACAS,OAAA,CAAAC,GAAA,YAAAV,MAAA;MACA,KAAAW,QAAA,CAAAC,IAAA,wBAAAwB,MAAA,CAAApC,MAAA,CAAA3F,IAAA,SAAA+H,MAAA,CAAApC,MAAA,CAAAN,KAAA;IACA;IAEA;IACA6J,oBAAA,WAAAA,qBAAAC,QAAA;MAAA,IAAAC,OAAA;MACA,KAAA9I,QAAA,CAAA+D,OAAA;MAEAC,UAAA;QACA;QACA,IAAA6E,QAAA;UACAC,OAAA,CAAAvM,gBAAA,CAAAC,UAAA;UACAsM,OAAA,CAAAvM,gBAAA,CAAAE,OAAA;UACAqM,OAAA,CAAAvM,gBAAA,CAAAG,MAAA;UACAoM,OAAA,CAAAvM,gBAAA,CAAAI,UAAA;QACA,WAAAkM,QAAA;UACAC,OAAA,CAAAvM,gBAAA,CAAAC,UAAA;UACAsM,OAAA,CAAAvM,gBAAA,CAAAE,OAAA;UACAqM,OAAA,CAAAvM,gBAAA,CAAAG,MAAA;UACAoM,OAAA,CAAAvM,gBAAA,CAAAI,UAAA;QACA;QAEAmM,OAAA,CAAAhM,UAAA;QACAgM,OAAA,CAAA9I,QAAA,CAAAwB,OAAA,sBAAAC,MAAA,CAAAoH,QAAA;MACA;IACA;IAEA;IACAE,SAAA,WAAAA,UAAA;MACA,KAAAxM,gBAAA;QACAC,UAAA;QACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;MAEA,KAAAC,gBAAA;QACAJ,UAAA;QACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;MAEA,KAAAG,UAAA;MACA,KAAAkD,QAAA,CAAAwB,OAAA;IACA;IAEA;IACApE,qBAAA,WAAAA,sBAAA;MAAA,IAAA4L,OAAA;MACAhF,UAAA;QACAgF,OAAA,CAAAhJ,QAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}