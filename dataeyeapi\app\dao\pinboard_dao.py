#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from sqlalchemy import select, func, and_, or_, text
from sqlalchemy.orm import selectinload, joinedload
from config.database import AsyncSessionLocal
from app.models.pinboard_models import (
    Pinboard, PinboardTag, PinboardTagRelation,
    AccountProject, PinboardShare, PinboardStar,
    UserNotificationSettings, PinboardUpdate, PinboardReminder,
    PinboardReminderLog, PinboardDeleteRecord
)
from utils.log_util import logger
from datetime import datetime

class PinboardDao:
    """Pinboard数据访问层"""

    @staticmethod
    async def get_pinboards_with_pagination(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        project_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        分页获取Pinboard列表
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件
                conditions = [Pinboard.is_deleted == 0]

                # 添加项目过滤
                if project_id:
                    conditions.append(Pinboard.project_id == project_id)

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        Pinboard.name.like(f'%{search}%'),
                        Pinboard.description.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(Pinboard).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(Pinboard.uid)).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                query = base_query.order_by(Pinboard.updated_at.desc()).offset(offset).limit(size)
                result = await session.execute(query)
                pinboards = result.scalars().all()

                # 获取详细信息
                items = []
                for pinboard in pinboards:
                    # 获取标签信息
                    tags_query = select(PinboardTag).join(
                        PinboardTagRelation, PinboardTag.id == PinboardTagRelation.tag_id
                    ).where(PinboardTagRelation.pinboard_uid == pinboard.uid)
                    tags_result = await session.execute(tags_query)
                    tags = tags_result.scalars().all()

                    # 获取用户信息（这里简化处理，实际应该关联用户表）
                    owner_info = {
                        'user_name': pinboard.create_by or '未知用户',
                        'avatar': ''
                    }

                    items.append({
                        'pinboard': pinboard,
                        'tags': tags,
                        'owner': owner_info
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"分页获取Pinboard列表失败: {str(e)}")
                raise e

    @staticmethod
    async def get_my_pinboards_with_pagination(
        user_id: int,
        page: int = 1,
        size: int = 20,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分页获取我的Pinboard列表
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件
                conditions = [
                    Pinboard.is_deleted == 0,
                    Pinboard.owner_id == user_id
                ]

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        Pinboard.name.like(f'%{search}%'),
                        Pinboard.description.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(Pinboard).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(Pinboard.uid)).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                query = base_query.order_by(Pinboard.updated_at.desc()).offset(offset).limit(size)
                result = await session.execute(query)
                pinboards = result.scalars().all()

                # 获取详细信息
                items = []
                for pinboard in pinboards:
                    # 获取标签信息
                    tags_query = select(PinboardTag).join(
                        PinboardTagRelation, PinboardTag.id == PinboardTagRelation.tag_id
                    ).where(PinboardTagRelation.pinboard_uid == pinboard.uid)
                    tags_result = await session.execute(tags_query)
                    tags = tags_result.scalars().all()

                    # 获取用户信息
                    owner_info = {
                        'user_name': pinboard.create_by or '未知用户',
                        'avatar': ''
                    }

                    items.append({
                        'pinboard': pinboard,
                        'tags': tags,
                        'owner': owner_info
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"分页获取我的Pinboard列表失败: {str(e)}")
                raise e

    @staticmethod
    async def create_pinboard(
        uid: str,
        project_id: int,
        name: str,
        description: Optional[str],
        owner_id: int,
        is_template: bool = False,
        create_by: str = '',
        update_by: str = ''
    ) -> Pinboard:
        """
        创建Pinboard
        """
        async with AsyncSessionLocal() as session:
            try:
                pinboard = Pinboard(
                    uid=uid,
                    project_id=project_id,
                    name=name,
                    description=description,
                    owner_id=owner_id,
                    is_template=is_template,
                    create_by=create_by,
                    update_by=update_by
                )

                session.add(pinboard)
                await session.commit()
                await session.refresh(pinboard)

                return pinboard

            except Exception as e:
                await session.rollback()
                logger.error(f"创建Pinboard失败: {str(e)}")
                raise e

    @staticmethod
    async def add_pinboard_tags(pinboard_uid: str, tag_names: List[str], project_id: int):
        """
        为Pinboard添加标签
        """
        async with AsyncSessionLocal() as session:
            try:
                for tag_name in tag_names:
                    # 查找或创建标签
                    tag_query = select(PinboardTag).where(
                        and_(PinboardTag.project_id == project_id, PinboardTag.name == tag_name)
                    )
                    tag_result = await session.execute(tag_query)
                    tag = tag_result.scalar_one_or_none()

                    if not tag:
                        # 创建新标签
                        tag = PinboardTag(
                            project_id=project_id,
                            name=tag_name,
                            color='#409EFF'
                        )
                        session.add(tag)
                        await session.flush()

                    # 创建关联关系
                    relation = PinboardTagRelation(
                        pinboard_uid=pinboard_uid,
                        tag_id=tag.id
                    )
                    session.add(relation)

                await session.commit()

            except Exception as e:
                await session.rollback()
                logger.error(f"添加Pinboard标签失败: {str(e)}")
                raise e

    @staticmethod
    async def get_tags_with_count(user_id: int, project_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取标签列表及其关联的报告数量
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件
                conditions = []
                if project_id:
                    conditions.append(PinboardTag.project_id == project_id)

                # 查询标签及其关联的Pinboard数量
                query = select(
                    PinboardTag,
                    func.count(PinboardTagRelation.id).label('count')
                ).outerjoin(
                    PinboardTagRelation, PinboardTag.id == PinboardTagRelation.tag_id
                ).outerjoin(
                    Pinboard, and_(
                        PinboardTagRelation.pinboard_uid == Pinboard.uid,
                        Pinboard.is_deleted == 0
                    )
                )

                if conditions:
                    query = query.where(and_(*conditions))

                query = query.group_by(PinboardTag.id).order_by(PinboardTag.created_at.desc())

                result = await session.execute(query)
                rows = result.all()

                return [{'tag': row[0], 'count': row[1]} for row in rows]

            except Exception as e:
                logger.error(f"获取标签列表失败: {str(e)}")
                raise e

    @staticmethod
    async def delete_pinboard(pinboard_uid: str, user_id: int):
        """
        删除Pinboard（软删除）
        """
        async with AsyncSessionLocal() as session:
            try:
                # 查找Pinboard
                query = select(Pinboard).where(
                    and_(Pinboard.uid == pinboard_uid, Pinboard.owner_id == user_id)
                )
                result = await session.execute(query)
                pinboard = result.scalar_one_or_none()

                if not pinboard:
                    raise ValueError("Pinboard不存在或无权限删除")

                # 软删除
                pinboard.is_deleted = 1
                pinboard.updated_at = datetime.now()

                await session.commit()

            except Exception as e:
                await session.rollback()
                logger.error(f"删除Pinboard失败: {str(e)}")
                raise e

    @staticmethod
    async def get_collaborate_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        tag: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取协作报告列表（共享给用户的报告）
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件 - 查找共享给用户的报告
                conditions = [
                    Pinboard.is_deleted == 0,
                    Pinboard.is_shared == 1,
                    or_(
                        PinboardShare.user_id == user_id,  # 直接共享给用户
                        PinboardShare.user_id.is_(None)    # 共享给所有人
                    )
                ]

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        Pinboard.name.like(f'%{search}%'),
                        Pinboard.description.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(Pinboard).join(
                    PinboardShare, Pinboard.uid == PinboardShare.pinboard_uid
                ).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(Pinboard.uid.distinct())).join(
                    PinboardShare, Pinboard.uid == PinboardShare.pinboard_uid
                ).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                query = base_query.distinct().order_by(Pinboard.updated_at.desc()).offset(offset).limit(size)
                result = await session.execute(query)
                pinboards = result.scalars().all()

                # 获取详细信息
                items = []
                for pinboard in pinboards:
                    # 获取标签信息
                    tags_query = select(PinboardTag).join(
                        PinboardTagRelation, PinboardTag.id == PinboardTagRelation.tag_id
                    ).where(PinboardTagRelation.pinboard_uid == pinboard.uid)
                    tags_result = await session.execute(tags_query)
                    tags = tags_result.scalars().all()

                    # 获取用户信息
                    owner_info = {
                        'user_name': pinboard.create_by or '未知用户',
                        'avatar': ''
                    }

                    # 获取协作者数量
                    collaborators_query = select(func.count(PinboardShare.id)).where(
                        PinboardShare.pinboard_uid == pinboard.uid
                    )
                    collaborators_result = await session.execute(collaborators_query)
                    collaborators_count = collaborators_result.scalar()

                    items.append({
                        'pinboard': pinboard,
                        'tags': tags,
                        'owner': owner_info,
                        'collaborators_count': collaborators_count
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"获取协作报告列表失败: {str(e)}")
                raise e

    @staticmethod
    async def get_shared_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        filter_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取共享报告列表（用户创建并共享的报告）
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件 - 查找用户创建并共享的报告
                conditions = [
                    Pinboard.is_deleted == 0,
                    Pinboard.owner_id == user_id,
                    Pinboard.is_shared == 1
                ]

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        Pinboard.name.like(f'%{search}%'),
                        Pinboard.description.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(Pinboard).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(Pinboard.uid)).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                query = base_query.order_by(Pinboard.updated_at.desc()).offset(offset).limit(size)
                result = await session.execute(query)
                pinboards = result.scalars().all()

                # 获取详细信息
                items = []
                for pinboard in pinboards:
                    # 获取分享信息
                    share_query = select(PinboardShare).where(
                        PinboardShare.pinboard_uid == pinboard.uid
                    ).limit(1)
                    share_result = await session.execute(share_query)
                    share_info = share_result.scalar_one_or_none()

                    share_data = {}
                    if share_info:
                        share_data = {
                            'share_url': share_info.share_url or '',
                            'access_password': share_info.access_password or '',
                            'expire_time': share_info.expire_time.isoformat() if share_info.expire_time else '',
                            'view_count': share_info.view_count or 0,
                            'permissions': share_info.permissions or [],
                            'created_at': share_info.created_at
                        }

                    items.append({
                        'pinboard': pinboard,
                        'share_info': share_data
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"获取共享报告列表失败: {str(e)}")
                raise e

    @staticmethod
    async def get_followed_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取关注报告列表（用户收藏的报告）
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件 - 查找用户收藏的报告
                conditions = [
                    Pinboard.is_deleted == 0,
                    PinboardStar.user_id == user_id
                ]

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        Pinboard.name.like(f'%{search}%'),
                        Pinboard.description.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(Pinboard).join(
                    PinboardStar, Pinboard.uid == PinboardStar.pinboard_uid
                ).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(Pinboard.uid)).join(
                    PinboardStar, Pinboard.uid == PinboardStar.pinboard_uid
                ).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                query = base_query.order_by(PinboardStar.created_at.desc()).offset(offset).limit(size)
                result = await session.execute(query)
                pinboards = result.scalars().all()

                # 获取详细信息
                items = []
                for pinboard in pinboards:
                    # 获取收藏信息
                    star_query = select(PinboardStar).where(
                        and_(
                            PinboardStar.pinboard_uid == pinboard.uid,
                            PinboardStar.user_id == user_id
                        )
                    )
                    star_result = await session.execute(star_query)
                    star_info = star_result.scalar_one_or_none()

                    # 获取用户信息
                    owner_info = {
                        'user_name': pinboard.create_by or '未知用户',
                        'avatar': ''
                    }

                    # 检查是否有更新（简化处理）
                    has_update = (datetime.now() - pinboard.updated_at).days <= 1

                    items.append({
                        'pinboard': pinboard,
                        'owner': owner_info,
                        'star_info': {
                            'created_at': star_info.created_at if star_info else datetime.now()
                        },
                        'category': '数据分析',  # 简化处理
                        'has_update': has_update
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"获取关注报告列表失败: {str(e)}")
                raise e

    @staticmethod
    async def get_reminders(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取定时提醒列表
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件
                conditions = [
                    Pinboard.owner_id == user_id,  # 只能看到自己创建的报告的提醒
                    Pinboard.is_deleted == 0
                ]

                # 添加状态筛选
                if status == 'enabled':
                    conditions.append(PinboardReminder.enabled == 1)
                elif status == 'disabled':
                    conditions.append(PinboardReminder.enabled == 0)

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        PinboardReminder.name.like(f'%{search}%'),
                        Pinboard.name.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(PinboardReminder).join(
                    Pinboard, PinboardReminder.pinboard_uid == Pinboard.uid
                ).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(PinboardReminder.id)).join(
                    Pinboard, PinboardReminder.pinboard_uid == Pinboard.uid
                ).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                query = base_query.order_by(PinboardReminder.created_at.desc()).offset(offset).limit(size)
                result = await session.execute(query)
                reminders = result.scalars().all()

                # 获取详细信息
                items = []
                for reminder in reminders:
                    # 获取关联的Pinboard信息
                    pinboard_query = select(Pinboard).where(Pinboard.uid == reminder.pinboard_uid)
                    pinboard_result = await session.execute(pinboard_query)
                    pinboard = pinboard_result.scalar_one_or_none()

                    items.append({
                        'reminder': reminder,
                        'pinboard': {
                            'name': pinboard.name if pinboard else '未知报告'
                        }
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"获取定时提醒列表失败: {str(e)}")
                raise e

    @staticmethod
    async def create_reminder(
        name: str,
        description: Optional[str],
        pinboard_uid: str,
        frequency: str,
        cron_expression: Optional[str],
        execute_time: str,
        recipients: List[str],
        push_methods: List[str],
        expire_date: Optional[str],
        create_by: str
    ) -> PinboardReminder:
        """
        创建定时提醒
        """
        async with AsyncSessionLocal() as session:
            try:
                # 计算下次执行时间（简化处理）
                next_run_time = datetime.now()
                if frequency == 'daily':
                    next_run_time = next_run_time.replace(hour=int(execute_time.split(':')[0]), minute=int(execute_time.split(':')[1]))

                import json

                reminder = PinboardReminder(
                    name=name,
                    description=description,
                    pinboard_uid=pinboard_uid,
                    frequency=frequency,
                    cron_expression=cron_expression,
                    execute_time=execute_time,
                    recipients=json.dumps(recipients),  # 转换为JSON字符串
                    push_methods=json.dumps(push_methods),  # 转换为JSON字符串
                    expire_date=datetime.fromisoformat(expire_date) if expire_date else None,
                    next_run_time=next_run_time,
                    create_by=create_by
                )

                session.add(reminder)
                await session.commit()
                await session.refresh(reminder)

                return reminder

            except Exception as e:
                await session.rollback()
                logger.error(f"创建定时提醒失败: {str(e)}")
                raise e

    @staticmethod
    async def get_trash_reports(
        user_id: int,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        sort_by: Optional[str] = "delete_time"
    ) -> Dict[str, Any]:
        """
        获取回收站报告列表
        """
        async with AsyncSessionLocal() as session:
            try:
                # 构建查询条件 - 查找已删除的报告
                conditions = [
                    Pinboard.is_deleted == 1,
                    Pinboard.owner_id == user_id
                ]

                # 添加搜索条件
                if search:
                    search_condition = or_(
                        Pinboard.name.like(f'%{search}%'),
                        Pinboard.description.like(f'%{search}%')
                    )
                    conditions.append(search_condition)

                # 构建基础查询
                base_query = select(Pinboard).where(and_(*conditions))

                # 获取总数
                count_query = select(func.count(Pinboard.uid)).where(and_(*conditions))
                total_result = await session.execute(count_query)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                if sort_by == "delete_time":
                    query = base_query.order_by(Pinboard.updated_at.desc()).offset(offset).limit(size)
                else:
                    query = base_query.order_by(Pinboard.name).offset(offset).limit(size)

                result = await session.execute(query)
                pinboards = result.scalars().all()

                # 获取详细信息
                items = []
                for pinboard in pinboards:
                    # 查找删除记录
                    delete_record_query = select(PinboardDeleteRecord).where(
                        PinboardDeleteRecord.pinboard_uid == pinboard.uid
                    ).order_by(PinboardDeleteRecord.delete_time.desc()).limit(1)
                    delete_record_result = await session.execute(delete_record_query)
                    delete_record = delete_record_result.scalar_one_or_none()

                    delete_info = {}
                    if delete_record:
                        delete_info = {
                            'original_path': delete_record.original_path or '',
                            'delete_time': delete_record.delete_time,
                            'delete_by_name': delete_record.delete_by or '未知用户',
                            'file_size': delete_record.file_size or '未知'
                        }
                    else:
                        # 如果没有删除记录，使用默认值
                        delete_info = {
                            'original_path': f'/reports/{pinboard.name}',
                            'delete_time': pinboard.updated_at,
                            'delete_by_name': pinboard.update_by or '未知用户',
                            'file_size': '未知'
                        }

                    items.append({
                        'pinboard': pinboard,
                        'delete_record': delete_info
                    })

                return {
                    'items': items,
                    'total': total
                }

            except Exception as e:
                logger.error(f"获取回收站报告列表失败: {str(e)}")
                raise e

    @staticmethod
    def restore_report_sync(
        pinboard_uid: str,
        user_id: int,
        restore_by: str
    ) -> Dict[str, Any]:
        """
        恢复报告 (同步版本)
        """
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from config.env import DataBaseConfig
        from urllib.parse import quote_plus

        # 创建同步数据库连接
        SYNC_DATABASE_URL = (
            f'mysql+pymysql://{DataBaseConfig.db_username}:{quote_plus(DataBaseConfig.db_password)}@'
            f'{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}'
        )

        sync_engine = create_engine(SYNC_DATABASE_URL)
        SyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

        with SyncSessionLocal() as session:
            try:
                # 查找要恢复的报告
                pinboard = session.query(Pinboard).filter(
                    Pinboard.uid == pinboard_uid,
                    Pinboard.owner_id == user_id,
                    Pinboard.is_deleted == 1
                ).first()

                if not pinboard:
                    logger.error(f"恢复报告失败: 报告不存在或无权限恢复 - pinboard_uid: {pinboard_uid}, user_id: {user_id}")
                    raise ValueError("报告不存在或无权限恢复")

                logger.info(f"找到要恢复的报告: {pinboard.name}")

                # 恢复报告
                pinboard.is_deleted = 0
                pinboard.updated_at = datetime.now()
                pinboard.update_by = restore_by

                # 更新删除记录
                delete_record = session.query(PinboardDeleteRecord).filter(
                    PinboardDeleteRecord.pinboard_uid == pinboard_uid
                ).order_by(PinboardDeleteRecord.delete_time.desc()).first()

                if delete_record:
                    logger.info(f"找到删除记录，更新恢复状态")
                    delete_record.restored = 1
                    delete_record.restore_time = datetime.now()
                    delete_record.restore_by = user_id
                    delete_record.restore_path = f'/reports/{pinboard.name}'

                # 提交事务
                session.commit()
                logger.info(f"成功恢复报告: {pinboard.name}")

                return {
                    'restore_path': f'/reports/{pinboard.name}'
                }

            except ValueError as ve:
                session.rollback()
                logger.error(f"恢复报告失败: {str(ve)}")
                raise ve
            except Exception as e:
                session.rollback()
                logger.error(f"恢复报告失败: {str(e)}")
                raise ValueError("报告不存在或无权限恢复")

    @staticmethod
    async def restore_report(
        pinboard_uid: str,
        user_id: int,
        restore_by: str
    ) -> Dict[str, Any]:
        """
        恢复报告 (异步包装器)
        """
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            PinboardDao.restore_report_sync,
            pinboard_uid,
            user_id,
            restore_by
        )
