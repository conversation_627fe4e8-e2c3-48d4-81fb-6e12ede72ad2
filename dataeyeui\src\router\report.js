import Layout from '@/layout'

const reportRouter = {
  path: '/report',
  component: Layout,
  redirect: '/report/recent',
  name: 'Report',
  meta: {
    title: '报告',
    icon: 'documentation'
  },
  children: [
    {
      path: 'recent',
      component: () => import('@/views/report/recent/index'),
      name: 'ReportRecent',
      meta: { title: '最近', icon: 'time' }
    },
    {
      path: 'my',
      component: () => import('@/views/report/my/index'),
      name: 'ReportMy',
      meta: { title: '我的', icon: 'user' }
    },
    {
      path: 'collaborate',
      component: () => import('@/views/report/collaborate/index'),
      name: 'ReportCollaborate',
      meta: { title: '协作', icon: 'peoples' }
    },
    {
      path: 'shared',
      component: () => import('@/views/report/shared/index'),
      name: 'ReportShared',
      meta: { title: '共享', icon: 'share' }
    },
    {
      path: 'followed',
      component: () => import('@/views/report/followed/index'),
      name: 'ReportFollowed',
      meta: { title: '已关注', icon: 'star' }
    },
    {
      path: 'timed',
      component: () => import('@/views/report/timed/index'),
      name: 'ReportTimed',
      meta: { title: '定时提醒', icon: 'time-range' }
    },
    {
      path: 'trash',
      component: () => import('@/views/report/trash/index'),
      name: 'ReportTrash',
      meta: { title: '回收站', icon: 'delete' }
    }
  ]
}

export default reportRouter
