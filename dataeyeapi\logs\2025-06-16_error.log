2025-06-16 10:06:30.879 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:06:30.879 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:06:32.431 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:06:32.432 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:06:32.434 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:06:32.889 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:06:33.433 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:06:33.435 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:08:07.889 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:08:07.889 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:08:10.848 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:08:10.850 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:08:10.851 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:08:11.297 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:08:12.099 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:08:12.099 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:08:22.946 | d772a20da1074936966da2af19bedc91 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为40886c31-df08-47ba-b10e-7419959f00a2的会话获取图片验证码成功
2025-06-16 10:08:27.078 | a6288320083c418db68afb759ba46855 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 10:08:27.630 | 67bae2a5108f4139a084a047db107c1c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:08:28.630 | 372b49cee4904df389690d95ae05cfd7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:09:24.805 | 473419b676ee4db28c50f117b2979cac | WARNING  | module_admin.service.login_service:__check_login_captcha:158 - 验证码已失效
2025-06-16 10:09:24.805 | 473419b676ee4db28c50f117b2979cac | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码已失效
2025-06-16 10:09:35.038 | dfb9cb3ad5fa4daf8a5f1c31a0bb8fb6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为95640502-7f05-434e-bd19-7e422456eeeb的会话获取图片验证码成功
2025-06-16 10:10:58.094 | 5c54e6eb6a804e05a326e8e88a5fbc9a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:10:58.600 | 517a7d86a0af4f659a5d988282fc9249 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:11:03.973 | 7e9ef379b3ba42afba7110eec049308a | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:11:04.553 | 7e9ef379b3ba42afba7110eec049308a | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-16 10:11:07.337 | d251ac464edf471f89e16aece35328a7 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:11:07.580 | d251ac464edf471f89e16aece35328a7 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:11:07.758 | d251ac464edf471f89e16aece35328a7 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:11:07.759 | d251ac464edf471f89e16aece35328a7 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:11:17.989 | 4608caa1a8ac4ec6b1faf5656e5faf3c | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-16 10:11:18.395 | 4608caa1a8ac4ec6b1faf5656e5faf3c | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-16 10:12:06.671 | c12e6da536b849739dff3b2601d52dbc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:12:07.621 | 25c28804afe742f98acfaedd388c8c0f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:12:15.243 | ee9eee211bf341de9f0f173368c69935 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:12:15.856 | ee9eee211bf341de9f0f173368c69935 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:12:15.957 | ee9eee211bf341de9f0f173368c69935 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:12:15.957 | ee9eee211bf341de9f0f173368c69935 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:12:52.530 | 61391673b8d4476c932d43b4bed9dce7 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-16 10:12:52.922 | 61391673b8d4476c932d43b4bed9dce7 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-16 10:12:53.051 | fdc9ac2ff95a4b29b0571c8d38a64bc6 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:12:53.098 | 5afac933619e4091a2af71a530a34e2d | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:12:53.276 | 5afac933619e4091a2af71a530a34e2d | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:12:53.463 | fdc9ac2ff95a4b29b0571c8d38a64bc6 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-16 10:13:01.311 | ac282bae07b34e918ffbdfa2f6eede0d | INFO     | app.controllers.pinboard_controller:delete_pinboard:125 - 用户 admin 删除Pinboard: pb-005-uuid-005
2025-06-16 10:13:01.731 | ac282bae07b34e918ffbdfa2f6eede0d | INFO     | app.controllers.pinboard_controller:delete_pinboard:133 - 成功删除Pinboard: pb-005-uuid-005
2025-06-16 10:13:03.034 | 595a00a0360842149aec0ad2b911447d | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:13:04.230 | 595a00a0360842149aec0ad2b911447d | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 10:13:06.555 | 931221690e1a4242ad63b2d55714b723 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:13:07.363 | 931221690e1a4242ad63b2d55714b723 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-16 10:13:11.055 | 9c40f895d7d4487d8c899a8ad6e6a541 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-16 10:13:11.618 | 9c40f895d7d4487d8c899a8ad6e6a541 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:13:11.619 | 9c40f895d7d4487d8c899a8ad6e6a541 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:13:11.619 | 9c40f895d7d4487d8c899a8ad6e6a541 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:13:12.599 | 771e06c38d2145728587079967546161 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-16 10:13:12.820 | 771e06c38d2145728587079967546161 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:13:12.820 | 771e06c38d2145728587079967546161 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:13:12.821 | 771e06c38d2145728587079967546161 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:14:37.062 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:14:37.063 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:14:40.001 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:14:40.001 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:14:40.003 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:14:40.564 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:14:41.379 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:14:41.379 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:17:35.545 | 8424f18ac48a4ed19c9aa872ec50af3c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:17:35.780 | ca124e9ef93646a3b4a79d1496d6d38c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:17:43.438 | 4e8ed972ac9c4cd2b8f1eaa82288ba45 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:17:44.531 | 4e8ed972ac9c4cd2b8f1eaa82288ba45 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:17:51.938 | 4ba4fb028dfc4dd6b6eb2e2616196c5f | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-16 10:17:52.652 | 4ba4fb028dfc4dd6b6eb2e2616196c5f | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-16 10:17:55.931 | 1e79a53dd6384c7a804c54f8b5b22862 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:18:01.459 | 1e79a53dd6384c7a804c54f8b5b22862 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-16 10:18:15.388 | 7029015ab9754f88913e95b256f56c29 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:18:17.228 | 7029015ab9754f88913e95b256f56c29 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:18:25.628 | 61862b899d974af496318ff79f88c498 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:18:26.739 | 78962c99799e4a4290e66ed6559f8d82 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:18:27.153 | 78962c99799e4a4290e66ed6559f8d82 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-16 10:18:27.378 | 866429ba08c54e308704429446b871eb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:18:31.372 | 81a65b89c34947eea7de4097ec0f6419 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:18:31.675 | a913344a1fc144d98daefa25582bd243 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:18:32.220 | 81a65b89c34947eea7de4097ec0f6419 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-16 10:18:32.616 | a913344a1fc144d98daefa25582bd243 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:18:40.253 | de1535a6d0724a89b846e3b83c1775a7 | INFO     | app.controllers.pinboard_controller:delete_pinboard:125 - 用户 admin 删除Pinboard: default-pinboard
2025-06-16 10:18:40.355 | de1535a6d0724a89b846e3b83c1775a7 | INFO     | app.controllers.pinboard_controller:delete_pinboard:133 - 成功删除Pinboard: default-pinboard
2025-06-16 10:18:41.112 | 2dea60c5d1a14e9196ff774d469db55f | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:18:41.528 | 2dea60c5d1a14e9196ff774d469db55f | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 10:18:45.699 | fc7f0bdc94d7430e94ece5304c027da7 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:18:46.104 | fc7f0bdc94d7430e94ece5304c027da7 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-16 10:18:53.915 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:18:54.509 | 0377d50966434e9a972663f25e845599 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:18:54.605 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:18:54.606 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:18:54.606 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:18:54.662 | 0377d50966434e9a972663f25e845599 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:18:54.663 | 0377d50966434e9a972663f25e845599 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:18:54.663 | 0377d50966434e9a972663f25e845599 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:09.908 | a8012af2a8c44686bf58ab5762df2b71 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:19:10.266 | a8012af2a8c44686bf58ab5762df2b71 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.268 | a8012af2a8c44686bf58ab5762df2b71 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.268 | a8012af2a8c44686bf58ab5762df2b71 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.842 | f4067385bc09411dae08bcd180b3eeca | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:19:10.923 | f4067385bc09411dae08bcd180b3eeca | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.923 | f4067385bc09411dae08bcd180b3eeca | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.923 | f4067385bc09411dae08bcd180b3eeca | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:20:27.551 | 0aeeee79759d4909ae1e6641882471f7 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:20:28.006 | 0aeeee79759d4909ae1e6641882471f7 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:20:40.903 | 248e6cb3e2c946e5b97d21891948abec | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:20:41.716 | 248e6cb3e2c946e5b97d21891948abec | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:20:51.549 | b127e806e1894cc5ab3452c917a1e5d3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:20:51.919 | b127e806e1894cc5ab3452c917a1e5d3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:27:38.133 | 7ec7437ef9084281907acb13864a6b45 | WARNING  | module_admin.service.login_service:get_current_user:210 - 用户token已失效，请重新登录
2025-06-16 10:30:01.203 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 10:30:01.204 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 10:30:04.185 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:30:04.187 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:30:07.165 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:30:07.165 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:30:07.166 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:30:08.165 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:30:08.962 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:30:08.962 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:30:34.092 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:30:34.093 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:30:38.839 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:30:38.839 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:30:38.840 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:30:40.657 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:30:43.423 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:30:43.424 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:32:01.217 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 10:32:01.217 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 10:32:04.262 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:32:04.263 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:32:07.311 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:32:07.312 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:32:07.313 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:32:08.497 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:32:12.056 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:32:12.057 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:32:15.526 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:32:15.527 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:32:17.520 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:32:17.520 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:32:17.521 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:32:18.464 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:32:19.719 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:32:19.719 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:34:34.349 | 77e81144e0d24837be0f37aa2c38c70c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:34:34.798 | 3adfc221594d4e07add1c65dfb2f0157 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:34:35.753 | 63a0eef8edbe467eb4db48bf660ec729 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:34:36.179 | 63a0eef8edbe467eb4db48bf660ec729 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 10:34:36.628 | fd4730353df94607a640a2bd47f81fae | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:34:36.947 | fd4730353df94607a640a2bd47f81fae | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:34:38.571 | 65178327d5484305a52b46f0da03db31 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:34:38.724 | 65178327d5484305a52b46f0da03db31 | ERROR    | app.services.pinboard_service:get_trash_reports:607 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-06-16 10:34:38.724 | 65178327d5484305a52b46f0da03db31 | ERROR    | app.controllers.pinboard_controller:get_trash_reports:340 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-06-16 10:34:39.943 | 17554622c0d44e728ce7354155656215 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:34:40.352 | 17554622c0d44e728ce7354155656215 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:44:32.081 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:44:32.082 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:44:35.053 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:44:35.054 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:44:35.056 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:44:35.884 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:44:36.966 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:44:36.966 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:49:10.702 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 10:49:10.702 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:14:08.775 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-16 11:14:08.776 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:14:09.875 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:14:09.875 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:14:09.878 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:14:10.326 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:14:10.882 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:14:10.882 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-16 11:14:49.536 | fc8470f580954b13a6fb6ddcbc93b0b6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a186b6aa-06f7-4a7a-9db0-086d40bea0f7的会话获取图片验证码成功
2025-06-16 11:14:54.031 | 216d10cb6e884f4085c24c9baf11d5dd | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 11:14:54.241 | 86d89a52a82f468c9614e6eba4685c4c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:14:54.729 | 138886661bb643b5a1649f1677dc097f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 11:15:03.442 | d3c39dfe0f464207a301d2bf90b2263c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:15:03.615 | 8a771925c6534950951b5d161757fc1b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 11:15:47.471 | 4c39c5da1df34a159e2ca7996e148f80 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 11:15:47.737 | 4c39c5da1df34a159e2ca7996e148f80 | ERROR    | app.services.pinboard_service:get_trash_reports:607 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-06-16 11:15:47.737 | 4c39c5da1df34a159e2ca7996e148f80 | ERROR    | app.controllers.pinboard_controller:get_trash_reports:340 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-06-16 11:15:53.260 | 8edfc94a36f341118a6329bec03bab05 | INFO     | app.controllers.pinboard_controller:get_pinboard_list:49 - 用户 admin 请求Pinboard列表
2025-06-16 11:15:53.265 | a59962bc0b3049d3a0774354248d812f | INFO     | app.controllers.pinboard_controller:get_reminders:270 - 用户 admin 请求定时提醒列表
2025-06-16 11:15:53.469 | a59962bc0b3049d3a0774354248d812f | INFO     | app.controllers.pinboard_controller:get_reminders:281 - 成功获取定时提醒列表，共 0 条记录
2025-06-16 11:15:53.664 | 8edfc94a36f341118a6329bec03bab05 | INFO     | app.controllers.pinboard_controller:get_pinboard_list:60 - 成功获取Pinboard列表，共 7 条记录
2025-06-16 11:15:54.844 | 41c571aa924f40d58f1c09836effb9d1 | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-16 11:15:55.000 | 41c571aa924f40d58f1c09836effb9d1 | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-16 11:15:56.219 | 52c9b9ce869244e0a5dc124513685bf3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 11:15:56.362 | 52c9b9ce869244e0a5dc124513685bf3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 11:15:58.001 | 0def281422e4498e8e679338bea2e6da | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-16 11:15:58.187 | 0def281422e4498e8e679338bea2e6da | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-16 11:15:59.254 | bcb58a14899345c79d357f76c83c3271 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 11:15:59.262 | 3798b1b6cfde449e85d544640389e920 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 11:15:59.337 | 3798b1b6cfde449e85d544640389e920 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 11:15:59.480 | bcb58a14899345c79d357f76c83c3271 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 11:16:52.573 | bfd5261468f146cbb101f8708357feee | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:16:52.826 | 75dd5be531444f169cc1f2aeb6d3ce0d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 11:26:43.941 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:26:43.941 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:26:47.726 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-16 11:26:47.727 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:26:48.809 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:26:48.809 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:26:48.811 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:26:49.247 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:26:49.806 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:26:49.806 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-16 11:27:17.294 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:27:17.295 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:27:21.068 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-16 11:27:21.069 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:27:22.359 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:27:22.359 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:27:22.362 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:27:22.880 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:27:23.383 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:27:23.384 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-16 11:27:50.652 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:27:50.653 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:27:55.036 |  | INFO     | server:lifespan:40 - RuoYi-FastAPI开始启动
2025-06-16 11:27:55.036 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:27:56.218 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:27:56.219 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:27:56.220 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:27:56.679 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:27:57.204 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:27:57.205 |  | INFO     | server:lifespan:47 - RuoYi-FastAPI启动成功
2025-06-16 11:28:24.543 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:28:24.543 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:28:28.692 |  | INFO     | server:lifespan:40 - RuoYi-FastAPI开始启动
2025-06-16 11:28:28.693 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:28:29.926 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:28:29.927 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:28:29.929 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:28:30.411 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:28:30.956 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:28:30.957 |  | INFO     | server:lifespan:47 - RuoYi-FastAPI启动成功
2025-06-16 11:32:10.362 | 0c8691ad593a41289ce6c4aed414ae74 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:32:10.479 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:32:10.479 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:32:14.155 |  | INFO     | server:lifespan:40 - RuoYi-FastAPI开始启动
2025-06-16 11:32:14.155 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:32:15.283 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:32:15.283 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:32:15.286 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:32:15.745 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:32:16.346 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:32:16.346 |  | INFO     | server:lifespan:47 - RuoYi-FastAPI启动成功
2025-06-16 11:32:16.478 | 5ce8b7ef223d4cd1822986332db90203 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 12:01:30.709 | 19455b51f756425e9b245e2c4394ae3a | ERROR    | exceptions.handle:exception_handler:70 - (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):

  File "asyncmy\\connection.pyx", line 658, in asyncmy.connection.Connection._read_bytes
    data = await self._reader.readexactly(num_bytes)

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\streams.py", line 712, in readexactly
    raise self._exception
          │    └ ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
          └ <StreamReader exception=ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None) transport=<_SelectorSocketTranspo...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\selector_events.py", line 856, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           │    │          │    └ 262144
           │    │          └ <_SelectorSocketTransport closed fd=2088>
           │    └ None
           └ <_SelectorSocketTransport closed fd=2088>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000002479E5E6C10>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002479E606B20>
    └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002479EA67790>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002479EA678B0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x00000247C23499C0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x00000247C25F2F80 (otid=0x000002479DEA4060) dead>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x00000247C23499C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\asyncmy\\error...

asyncmy.errors.OperationalError: (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 364
               │     └ 3
               └ <function _main at 0x00000247997ED670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 364
           │    └ <function BaseProcess._bootstrap at 0x00000247997558B0>
           └ <SpawnProcess name='SpawnProcess-7' parent=27420 started>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\multiprocessing\process.py", line 315, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000024799746EE0>
    └ <SpawnProcess name='SpawnProcess-7' parent=27420 started>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000247998099A0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-7' parent=27420 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-7' parent=27420 started>
    │    └ <function subprocess_started at 0x000002479BB0E5E0>
    └ <SpawnProcess name='SpawnProcess-7' parent=27420 started>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=2076, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000024799809AC0>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=2076, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x000002479BB30B80>
           │       │   └ <uvicorn.server.Server object at 0x0000024799809AC0>
           │       └ <function run at 0x0000024799820550>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\__init__.py'>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000247C204E7C0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002479B3AE700>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002479B3AE670>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002479B3B11F0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002479B340DC0>
    └ <Handle <TaskWakeupMethWrapper object at 0x00000247C257C1C0>(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskWakeupMethWrapper object at 0x00000247C257C1C0>(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskWakeupMethWrapper object at 0x00000247C257C1C0>(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskWakeupMethWrapper object at 0x00000247C257C1C0>(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000247C222B3A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000247C2...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000024...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000024799809E50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000247C222B3A0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000247C2...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000024...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000247C2...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000024...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000247C2344370>
          └ <fastapi.applications.FastAPI object at 0x0000024799809E50>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000247C2503EE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000024...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x00000247C23442E0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000247C2344370>

  File "D:\jgst\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000247C2353820>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x00000247C23442B0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x00000247C23442E0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000247C2353820>
          │         │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x00000247C25663A0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x00000247BF787940>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x00000247C25663A0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000247C2344250>
          └ <starlette.middleware.gzip.GZipResponder object at 0x00000247C25663A0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x00000247C25663A0>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000247C2344220>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000247C2344250>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x00000247C25663A0>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000247C25665B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000247C1CDBE20>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000247C2344220>
          └ <function wrap_app_handling_exceptions at 0x000002479DCA0E50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000247C254C280>
          │   │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000247C1CDBE20>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000247C254C280>
          │    │                │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000247C1CDBE20>>
          └ <fastapi.routing.APIRouter object at 0x00000247C1CDBE20>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000247C254C280>
          │     │      │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x000002479DCBF310>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000247C254C280>
          │    │   │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000247C1CE3040>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000247C254C280>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000247C2362310>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000247C254C8B0>
          └ <function wrap_app_handling_exceptions at 0x000002479DCA0E50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000247C254C940>
          │   │      └ <function RequestResponseCycle.receive at 0x00000247C23538B0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000247C254C8B0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000247C2362310>
                     └ <function get_request_handler.<locals>.app at 0x00000247C1CE30D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\fastapi\routing.py", line 291, in app
    solved_result = await solve_dependencies(
                          └ <function solve_dependencies at 0x000002479DC87AF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\fastapi\dependencies\utils.py", line 638, in solve_dependencies
    solved = await call(**solved_result.values)
                   │      │             └ {'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwdF9uYW1lIjoiXHU3ODE0XHU1M...
                   │      └ SolvedDependency(values={'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwd...
                   └ <bound method LoginService.get_current_user of <class 'module_admin.service.login_service.LoginService'>>

  File "D:\jgst\dataeyeapi\module_admin\service\login_service.py", line 212, in get_current_user
    query_user = await UserDao.get_user_by_id(query_db, user_id=token_data.user_id)
                       │       │              │                 │          └ 1
                       │       │              │                 └ TokenData(user_id=1)
                       │       │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000247C25961C0>
                       │       └ <classmethod object at 0x00000247BF9B8FD0>
                       └ <class 'module_admin.dao.user_dao.UserDao'>

  File "D:\jgst\dataeyeapi\module_admin\dao\user_dao.py", line 89, in get_user_by_id
    await db.execute(
          │  └ <function AsyncSession.execute at 0x000002479E9DACA0>
          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000247C25961C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x000002479DEDF310>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x00000247C25F2F80 (otid=0x000002479DEA4060) dead>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000002479E8FCC10>
           └ <sqlalchemy.orm.session.Session object at 0x00000247C25963D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod object at 0x000002479E7927F0>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x000002479E52C4C0>
             └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x00000247C2596040>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x000002479E52C790>
           └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x000002479E52C940>
          └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x000002479E52C9D0>
           └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x000002479E52CC10>
    └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
          │                    │              └ (<class 'asyncmy.errors.OperationalError'>, OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 1...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ OperationalError("(asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000002479E5E6C10>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002479E606B20>
    └ <sqlalchemy.engine.base.Connection object at 0x00000247C257CA00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002479EA67790>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002479EA678B0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x00000247C23499C0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x00000247C25F2F80 (otid=0x000002479DEA4060) dead>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x00000247C23499C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x00000247C25EE900>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\asyncmy\\error...

sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 12:01:31.028 | 7907a5973fec40aca93bf975ad40cc8e | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-16 12:01:31.090 | e68c303409a943228f94e0616b0f03b4 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为fed24288-83e5-4f6a-85da-ad8244497674的会话获取图片验证码成功
2025-06-16 12:01:38.681 | d9fbf9179b0a40cdbaa36f01e5e0fb35 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 12:01:38.905 | ec498c19784d4e3a9b0c386ccb0d1f93 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 12:01:39.416 | 437d3ff9bcc74319a65b49cfbf1c5bca | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 12:05:40.262 | 89bbea34baeb4c62b73cbddb0502b87d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 12:05:40.455 | ddba1c0aeaf642a5b52e801dfb1e786b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 12:06:29.756 | 09e31a1fcef3439b92646e5b5427a606 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 12:06:29.961 | 2bf2d7c5908c417daafbee9cc2379bc9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 12:07:08.211 | 9c99e3071b31410486c14f0a677d3e72 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 12:07:08.435 | 0427820c0e3a459abf76f3a471beb41c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 13:51:41.291 | f93f4a8a17c340ccbae4ab448ac20dec | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-16 13:51:41.365 | 2a666aa107f94c58807ad3d391d093b3 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-16 13:51:41.406 | 1f68d6bed88a4e1aa376fa4cf8433c49 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为543cdff4-01ea-47cc-8447-385daca18da0的会话获取图片验证码成功
2025-06-16 13:51:46.188 | 57de595836ea4d1a87dcad1c8399bb7a | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 13:51:46.791 | 399a7629a35c4373a1c7274a848783db | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 13:51:47.044 | 50d841196e24453f8e1d7042dfd4dd18 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 13:55:45.739 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 13:55:45.740 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 13:55:52.512 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI开始启动
2025-06-16 13:55:52.513 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 13:55:54.656 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 13:55:54.656 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 13:55:54.659 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 13:55:55.088 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 13:55:55.606 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 13:55:55.606 |  | INFO     | server:lifespan:48 - RuoYi-FastAPI启动成功
2025-06-16 13:56:22.068 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 13:56:22.069 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 13:56:25.812 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI开始启动
2025-06-16 13:56:25.812 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 13:56:27.274 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 13:56:27.274 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 13:56:27.277 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 13:56:27.861 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 13:56:28.534 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 13:56:28.534 |  | INFO     | server:lifespan:48 - RuoYi-FastAPI启动成功
2025-06-16 13:56:32.182 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI开始启动
2025-06-16 13:56:32.182 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 13:56:33.349 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 13:56:33.349 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 13:56:33.351 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 13:56:33.807 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 13:56:34.320 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 13:56:34.321 |  | INFO     | server:lifespan:48 - RuoYi-FastAPI启动成功
2025-06-16 13:57:01.726 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 13:57:01.726 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 13:57:06.737 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI开始启动
2025-06-16 13:57:06.738 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 13:57:08.922 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 13:57:08.923 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 13:57:08.926 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 13:57:09.377 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 13:57:10.079 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 13:57:10.080 |  | INFO     | server:lifespan:48 - RuoYi-FastAPI启动成功
2025-06-16 13:57:29.131 | f242b7d4180645b0ad8a440e63d5e7ff | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 13:57:29.306 | 7556147cc0034fc8b3c915d3b990f6cf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 13:58:10.641 | b6d2050e54734c0abc8e71a3c30cec36 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 13:58:10.868 | 8803c3c4fe4d4698b189bf0100eea88a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:02:03.579 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 14:02:03.581 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 14:02:07.905 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI开始启动
2025-06-16 14:02:07.906 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 14:02:10.267 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 14:02:10.267 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 14:02:10.269 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 14:02:10.777 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 14:02:11.296 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 14:02:11.296 |  | INFO     | server:lifespan:48 - RuoYi-FastAPI启动成功
2025-06-16 14:07:03.361 | fa3d7607c57c45ec94d2137531b50e0c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:07:03.556 | ae14ff291ccd48d396591a15c9b865b7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:12:53.997 | 7b90af320be14d24bbef51379f97148a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:12:54.192 | 5bd03c0ddae24ac09009cf33c2cf75b8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:13:04.497 | 3764d69e1c10430fb8505a68f0f109f9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:13:04.697 | 61faccab8ea9441a81a75bf983fada65 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:13:14.658 | ee4e50d7fe7a4953a0c1e727386a35d9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:13:14.858 | 5fa9166c08ec446b8e9e4ff47cea5aaf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:13:30.244 | 3cc80334b5ae4081bec67288d657968e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:13:30.445 | 3996cc0545224bd788a6e353a40c4f84 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:19:47.349 | a4ec3b5796e74e309cb61a484fc8e86c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:19:47.549 | bb5ad6f14ecb48e08c3d33edbbbbb503 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:46:27.086 | 9f3c3e332985449f885063eeb8fca191 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:46:27.283 | 9ff1cd2df7f9488fa6886aa963266b07 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:46:35.349 | e5a75b7eba784b80b963d81397c01422 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:46:35.602 | 03d283fe68a4407f9dd9c689b5a4a1bf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:51:29.745 | 199f2c6ebf144fdfb5910a8143884636 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:51:29.958 | 38001b0b237a4a369ecc6278259f5218 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 14:51:55.214 | 981637191023454e90d121f4eb3a4600 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 14:51:55.457 | 082220b3a43c4c7f8b13f199b45937cd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 15:15:35.917 | 547dfb6b90cb4c7caeee5d4a70a99107 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 15:15:36.166 | 7a6aeda66fbd4b3884e66b8c26f9c74e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 15:16:43.417 | 2e5436aaf9a142a99e9e4aa20cd7fbc2 | INFO     | module_admin.controller.user_controller:query_detail_system_user_profile:210 - 获取user_id为1的信息成功
2025-06-16 15:33:44.556 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 15:33:44.557 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 15:34:05.470 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI开始启动
2025-06-16 15:34:05.472 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 15:34:06.824 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 15:34:06.824 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 15:34:06.826 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 15:34:07.331 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 15:34:07.971 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 15:34:07.973 |  | INFO     | server:lifespan:48 - RuoYi-FastAPI启动成功
2025-06-16 15:35:24.664 | 2c10b68c0a994464a3150510b312fa79 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a2680876-78fc-4692-86b7-9077dafabc19的会话获取图片验证码成功
2025-06-16 15:36:13.139 | 3f8cb879ed7549e8bef2f91db171dec7 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 15:36:13.375 | 015c8caefed84a32885dd4d08a779d85 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 15:36:13.878 | 8f854f67f1dc4df0a7cfcff891461a5f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 15:38:29.701 | cc05bf6a4eb444ab9bdbe516f67886c4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 15:38:29.900 | 8554e5b590cc4b519c520c7bd1cec50d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 15:38:56.062 | 747e02a6d6c541dc826668f26d4620fe | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 15:38:56.262 | 57f951c64a344b208b1e85133a6c7e95 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
