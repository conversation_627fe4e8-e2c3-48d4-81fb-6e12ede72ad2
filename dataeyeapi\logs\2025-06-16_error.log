2025-06-16 10:06:30.879 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:06:30.879 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:06:32.431 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:06:32.432 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:06:32.434 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:06:32.889 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:06:33.433 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:06:33.435 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:08:07.889 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:08:07.889 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:08:10.848 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:08:10.850 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:08:10.851 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:08:11.297 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:08:12.099 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:08:12.099 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:08:22.946 | d772a20da1074936966da2af19bedc91 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为40886c31-df08-47ba-b10e-7419959f00a2的会话获取图片验证码成功
2025-06-16 10:08:27.078 | a6288320083c418db68afb759ba46855 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 10:08:27.630 | 67bae2a5108f4139a084a047db107c1c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:08:28.630 | 372b49cee4904df389690d95ae05cfd7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:09:24.805 | 473419b676ee4db28c50f117b2979cac | WARNING  | module_admin.service.login_service:__check_login_captcha:158 - 验证码已失效
2025-06-16 10:09:24.805 | 473419b676ee4db28c50f117b2979cac | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码已失效
2025-06-16 10:09:35.038 | dfb9cb3ad5fa4daf8a5f1c31a0bb8fb6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为95640502-7f05-434e-bd19-7e422456eeeb的会话获取图片验证码成功
2025-06-16 10:10:58.094 | 5c54e6eb6a804e05a326e8e88a5fbc9a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:10:58.600 | 517a7d86a0af4f659a5d988282fc9249 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:11:03.973 | 7e9ef379b3ba42afba7110eec049308a | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:11:04.553 | 7e9ef379b3ba42afba7110eec049308a | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-16 10:11:07.337 | d251ac464edf471f89e16aece35328a7 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:11:07.580 | d251ac464edf471f89e16aece35328a7 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:11:07.758 | d251ac464edf471f89e16aece35328a7 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:11:07.759 | d251ac464edf471f89e16aece35328a7 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:11:17.989 | 4608caa1a8ac4ec6b1faf5656e5faf3c | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-16 10:11:18.395 | 4608caa1a8ac4ec6b1faf5656e5faf3c | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-16 10:12:06.671 | c12e6da536b849739dff3b2601d52dbc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:12:07.621 | 25c28804afe742f98acfaedd388c8c0f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:12:15.243 | ee9eee211bf341de9f0f173368c69935 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:12:15.856 | ee9eee211bf341de9f0f173368c69935 | ERROR    | app.dao.pinboard_dao:get_shared_reports:470 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:12:15.957 | ee9eee211bf341de9f0f173368c69935 | ERROR    | app.services.pinboard_service:get_shared_reports:384 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:12:15.957 | ee9eee211bf341de9f0f173368c69935 | ERROR    | app.controllers.pinboard_controller:get_shared_reports:225 - 获取共享报告列表失败: (asyncmy.errors.OperationalError) (1054, "Unknown column 'pinboard_shares.share_url' in 'field list'")
[SQL: SELECT pinboard_shares.id, pinboard_shares.pinboard_uid, pinboard_shares.user_id, pinboard_shares.share_url, pinboard_shares.access_password, pinboard_shares.expire_time, pinboard_shares.view_count, pinboard_shares.permissions, pinboard_shares.created_at, pinboard_shares.create_by 
FROM pinboard_shares 
WHERE pinboard_shares.pinboard_uid = %s 
 LIMIT %s]
[parameters: ('pb-005-uuid-005', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-16 10:12:52.530 | 61391673b8d4476c932d43b4bed9dce7 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-16 10:12:52.922 | 61391673b8d4476c932d43b4bed9dce7 | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-16 10:12:53.051 | fdc9ac2ff95a4b29b0571c8d38a64bc6 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:12:53.098 | 5afac933619e4091a2af71a530a34e2d | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:12:53.276 | 5afac933619e4091a2af71a530a34e2d | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:12:53.463 | fdc9ac2ff95a4b29b0571c8d38a64bc6 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-16 10:13:01.311 | ac282bae07b34e918ffbdfa2f6eede0d | INFO     | app.controllers.pinboard_controller:delete_pinboard:125 - 用户 admin 删除Pinboard: pb-005-uuid-005
2025-06-16 10:13:01.731 | ac282bae07b34e918ffbdfa2f6eede0d | INFO     | app.controllers.pinboard_controller:delete_pinboard:133 - 成功删除Pinboard: pb-005-uuid-005
2025-06-16 10:13:03.034 | 595a00a0360842149aec0ad2b911447d | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:13:04.230 | 595a00a0360842149aec0ad2b911447d | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 10:13:06.555 | 931221690e1a4242ad63b2d55714b723 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:13:07.363 | 931221690e1a4242ad63b2d55714b723 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-16 10:13:11.055 | 9c40f895d7d4487d8c899a8ad6e6a541 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-16 10:13:11.618 | 9c40f895d7d4487d8c899a8ad6e6a541 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:13:11.619 | 9c40f895d7d4487d8c899a8ad6e6a541 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:13:11.619 | 9c40f895d7d4487d8c899a8ad6e6a541 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'pb-005-uuid-005'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:13:12.599 | 771e06c38d2145728587079967546161 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: pb-005-uuid-005
2025-06-16 10:13:12.820 | 771e06c38d2145728587079967546161 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:13:12.820 | 771e06c38d2145728587079967546161 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:13:12.821 | 771e06c38d2145728587079967546161 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:14:37.062 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:14:37.063 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:14:40.001 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:14:40.001 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:14:40.003 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:14:40.564 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:14:41.379 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:14:41.379 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:17:35.545 | 8424f18ac48a4ed19c9aa872ec50af3c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:17:35.780 | ca124e9ef93646a3b4a79d1496d6d38c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:17:43.438 | 4e8ed972ac9c4cd2b8f1eaa82288ba45 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:17:44.531 | 4e8ed972ac9c4cd2b8f1eaa82288ba45 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:17:51.938 | 4ba4fb028dfc4dd6b6eb2e2616196c5f | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-16 10:17:52.652 | 4ba4fb028dfc4dd6b6eb2e2616196c5f | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-16 10:17:55.931 | 1e79a53dd6384c7a804c54f8b5b22862 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:18:01.459 | 1e79a53dd6384c7a804c54f8b5b22862 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 0 条记录
2025-06-16 10:18:15.388 | 7029015ab9754f88913e95b256f56c29 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:18:17.228 | 7029015ab9754f88913e95b256f56c29 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:18:25.628 | 61862b899d974af496318ff79f88c498 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:18:26.739 | 78962c99799e4a4290e66ed6559f8d82 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:18:27.153 | 78962c99799e4a4290e66ed6559f8d82 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-16 10:18:27.378 | 866429ba08c54e308704429446b871eb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:18:31.372 | 81a65b89c34947eea7de4097ec0f6419 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:18:31.675 | a913344a1fc144d98daefa25582bd243 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:18:32.220 | 81a65b89c34947eea7de4097ec0f6419 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 7 条记录
2025-06-16 10:18:32.616 | a913344a1fc144d98daefa25582bd243 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:18:40.253 | de1535a6d0724a89b846e3b83c1775a7 | INFO     | app.controllers.pinboard_controller:delete_pinboard:125 - 用户 admin 删除Pinboard: default-pinboard
2025-06-16 10:18:40.355 | de1535a6d0724a89b846e3b83c1775a7 | INFO     | app.controllers.pinboard_controller:delete_pinboard:133 - 成功删除Pinboard: default-pinboard
2025-06-16 10:18:41.112 | 2dea60c5d1a14e9196ff774d469db55f | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:18:41.528 | 2dea60c5d1a14e9196ff774d469db55f | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 10:18:45.699 | fc7f0bdc94d7430e94ece5304c027da7 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:18:46.104 | fc7f0bdc94d7430e94ece5304c027da7 | INFO     | app.controllers.pinboard_controller:get_trash_reports:336 - 成功获取回收站报告列表，共 1 条记录
2025-06-16 10:18:53.915 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:18:54.509 | 0377d50966434e9a972663f25e845599 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:18:54.605 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:18:54.606 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:18:54.606 | bdb25482f7ea4dc1bd3d2f3b75dfd37e | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT pinboards.uid AS pinboards_uid, pinboards.project_id AS pinboards_project_id, pinboards.name AS pinboards_name, pinboards.description AS pinboards_description, pinboards.owner_id AS pinboards_owner_id, pinboards.is_template AS pinboards_is_template, pinboards.is_shared AS pinboards_is_shared, pinboards.share_type AS pinboards_share_type, pinboards.is_deleted AS pinboards_is_deleted, pinboards.created_at AS pinboards_created_at, pinboards.updated_at AS pinboards_updated_at, pinboards.create_by AS pinboards_create_by, pinboards.update_by AS pinboards_update_by 
FROM pinboards 
WHERE pinboards.uid = %s]
[parameters: [{'pk_1': 'default-pinboard'}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-06-16 10:18:54.662 | 0377d50966434e9a972663f25e845599 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:18:54.663 | 0377d50966434e9a972663f25e845599 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:18:54.663 | 0377d50966434e9a972663f25e845599 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:09.908 | a8012af2a8c44686bf58ab5762df2b71 | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:19:10.266 | a8012af2a8c44686bf58ab5762df2b71 | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.268 | a8012af2a8c44686bf58ab5762df2b71 | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.268 | a8012af2a8c44686bf58ab5762df2b71 | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.842 | f4067385bc09411dae08bcd180b3eeca | INFO     | app.controllers.pinboard_controller:restore_report:352 - 用户 admin 恢复报告: default-pinboard
2025-06-16 10:19:10.923 | f4067385bc09411dae08bcd180b3eeca | ERROR    | app.dao.pinboard_dao:restore_report:821 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.923 | f4067385bc09411dae08bcd180b3eeca | ERROR    | app.services.pinboard_service:restore_report:634 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:19:10.923 | f4067385bc09411dae08bcd180b3eeca | ERROR    | app.controllers.pinboard_controller:restore_report:365 - 恢复报告失败: 报告不存在或无权限恢复
2025-06-16 10:20:27.551 | 0aeeee79759d4909ae1e6641882471f7 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:20:28.006 | 0aeeee79759d4909ae1e6641882471f7 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:20:40.903 | 248e6cb3e2c946e5b97d21891948abec | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:20:41.716 | 248e6cb3e2c946e5b97d21891948abec | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:20:51.549 | b127e806e1894cc5ab3452c917a1e5d3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:20:51.919 | b127e806e1894cc5ab3452c917a1e5d3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:27:38.133 | 7ec7437ef9084281907acb13864a6b45 | WARNING  | module_admin.service.login_service:get_current_user:210 - 用户token已失效，请重新登录
2025-06-16 10:30:01.203 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 10:30:01.204 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 10:30:04.185 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:30:04.187 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:30:07.165 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:30:07.165 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:30:07.166 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:30:08.165 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:30:08.962 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:30:08.962 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:30:34.092 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:30:34.093 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:30:38.839 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:30:38.839 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:30:38.840 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:30:40.657 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:30:43.423 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:30:43.424 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:32:01.217 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 10:32:01.217 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 10:32:04.262 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:32:04.263 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:32:07.311 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:32:07.312 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:32:07.313 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:32:08.497 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:32:12.056 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:32:12.057 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:32:15.526 |  | INFO     | __main__:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:32:15.527 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:32:17.520 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:32:17.520 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:32:17.521 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:32:18.464 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:32:19.719 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:32:19.719 |  | INFO     | __main__:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:34:34.349 | 77e81144e0d24837be0f37aa2c38c70c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 10:34:34.798 | 3adfc221594d4e07add1c65dfb2f0157 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 10:34:35.753 | 63a0eef8edbe467eb4db48bf660ec729 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 10:34:36.179 | 63a0eef8edbe467eb4db48bf660ec729 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 10:34:36.628 | fd4730353df94607a640a2bd47f81fae | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 10:34:36.947 | fd4730353df94607a640a2bd47f81fae | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 10:34:38.571 | 65178327d5484305a52b46f0da03db31 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 10:34:38.724 | 65178327d5484305a52b46f0da03db31 | ERROR    | app.services.pinboard_service:get_trash_reports:607 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-06-16 10:34:38.724 | 65178327d5484305a52b46f0da03db31 | ERROR    | app.controllers.pinboard_controller:get_trash_reports:340 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-06-16 10:34:39.943 | 17554622c0d44e728ce7354155656215 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 10:34:40.352 | 17554622c0d44e728ce7354155656215 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 10:44:32.081 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-16 10:44:32.082 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 10:44:35.053 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 10:44:35.054 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 10:44:35.056 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 10:44:35.884 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 10:44:36.966 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 10:44:36.966 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-16 10:49:10.702 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 10:49:10.702 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:14:08.775 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-16 11:14:08.776 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:14:09.875 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:14:09.875 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:14:09.878 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:14:10.326 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:14:10.882 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:14:10.882 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-16 11:14:49.536 | fc8470f580954b13a6fb6ddcbc93b0b6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a186b6aa-06f7-4a7a-9db0-086d40bea0f7的会话获取图片验证码成功
2025-06-16 11:14:54.031 | 216d10cb6e884f4085c24c9baf11d5dd | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-16 11:14:54.241 | 86d89a52a82f468c9614e6eba4685c4c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:14:54.729 | 138886661bb643b5a1649f1677dc097f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 11:15:03.442 | d3c39dfe0f464207a301d2bf90b2263c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:15:03.615 | 8a771925c6534950951b5d161757fc1b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 11:15:47.471 | 4c39c5da1df34a159e2ca7996e148f80 | INFO     | app.controllers.pinboard_controller:get_trash_reports:325 - 用户 admin 请求回收站报告列表
2025-06-16 11:15:47.737 | 4c39c5da1df34a159e2ca7996e148f80 | ERROR    | app.services.pinboard_service:get_trash_reports:607 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-06-16 11:15:47.737 | 4c39c5da1df34a159e2ca7996e148f80 | ERROR    | app.controllers.pinboard_controller:get_trash_reports:340 - 获取回收站报告列表失败: 1 validation error for TrashReportItem
delete_by
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.10/v/string_type
2025-06-16 11:15:53.260 | 8edfc94a36f341118a6329bec03bab05 | INFO     | app.controllers.pinboard_controller:get_pinboard_list:49 - 用户 admin 请求Pinboard列表
2025-06-16 11:15:53.265 | a59962bc0b3049d3a0774354248d812f | INFO     | app.controllers.pinboard_controller:get_reminders:270 - 用户 admin 请求定时提醒列表
2025-06-16 11:15:53.469 | a59962bc0b3049d3a0774354248d812f | INFO     | app.controllers.pinboard_controller:get_reminders:281 - 成功获取定时提醒列表，共 0 条记录
2025-06-16 11:15:53.664 | 8edfc94a36f341118a6329bec03bab05 | INFO     | app.controllers.pinboard_controller:get_pinboard_list:60 - 成功获取Pinboard列表，共 7 条记录
2025-06-16 11:15:54.844 | 41c571aa924f40d58f1c09836effb9d1 | INFO     | app.controllers.pinboard_controller:get_followed_reports:240 - 用户 admin 请求关注报告列表
2025-06-16 11:15:55.000 | 41c571aa924f40d58f1c09836effb9d1 | INFO     | app.controllers.pinboard_controller:get_followed_reports:251 - 成功获取关注报告列表，共 2 条记录
2025-06-16 11:15:56.219 | 52c9b9ce869244e0a5dc124513685bf3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:210 - 用户 admin 请求共享报告列表
2025-06-16 11:15:56.362 | 52c9b9ce869244e0a5dc124513685bf3 | INFO     | app.controllers.pinboard_controller:get_shared_reports:221 - 成功获取共享报告列表，共 2 条记录
2025-06-16 11:15:58.001 | 0def281422e4498e8e679338bea2e6da | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:180 - 用户 admin 请求协作报告列表
2025-06-16 11:15:58.187 | 0def281422e4498e8e679338bea2e6da | INFO     | app.controllers.pinboard_controller:get_collaborate_reports:191 - 成功获取协作报告列表，共 2 条记录
2025-06-16 11:15:59.254 | bcb58a14899345c79d357f76c83c3271 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:151 - 用户 admin 请求我的Pinboard列表
2025-06-16 11:15:59.262 | 3798b1b6cfde449e85d544640389e920 | INFO     | app.controllers.pinboard_controller:get_tags:101 - 用户 admin 请求标签列表
2025-06-16 11:15:59.337 | 3798b1b6cfde449e85d544640389e920 | INFO     | app.controllers.pinboard_controller:get_tags:109 - 成功获取标签列表，共 4 个标签
2025-06-16 11:15:59.480 | bcb58a14899345c79d357f76c83c3271 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:161 - 成功获取我的Pinboard列表，共 6 条记录
2025-06-16 11:16:52.573 | bfd5261468f146cbb101f8708357feee | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:16:52.826 | 75dd5be531444f169cc1f2aeb6d3ce0d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-16 11:26:43.941 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:26:43.941 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:26:47.726 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-16 11:26:47.727 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:26:48.809 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:26:48.809 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:26:48.811 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:26:49.247 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:26:49.806 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:26:49.806 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-16 11:27:17.294 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:27:17.295 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:27:21.068 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-16 11:27:21.069 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:27:22.359 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:27:22.359 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:27:22.362 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:27:22.880 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:27:23.383 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:27:23.384 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-16 11:27:50.652 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:27:50.653 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:27:55.036 |  | INFO     | server:lifespan:40 - RuoYi-FastAPI开始启动
2025-06-16 11:27:55.036 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:27:56.218 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:27:56.219 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:27:56.220 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:27:56.679 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:27:57.204 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:27:57.205 |  | INFO     | server:lifespan:47 - RuoYi-FastAPI启动成功
2025-06-16 11:28:24.543 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:28:24.543 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-16 11:28:28.692 |  | INFO     | server:lifespan:40 - RuoYi-FastAPI开始启动
2025-06-16 11:28:28.693 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-16 11:28:29.926 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-16 11:28:29.927 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-16 11:28:29.929 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-16 11:28:30.411 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-16 11:28:30.956 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-16 11:28:30.957 |  | INFO     | server:lifespan:47 - RuoYi-FastAPI启动成功
2025-06-16 11:32:10.362 | 0c8691ad593a41289ce6c4aed414ae74 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-16 11:32:10.479 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-16 11:32:10.479 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
