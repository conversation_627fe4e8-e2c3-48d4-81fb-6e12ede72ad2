{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\dataseek\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\dataseek\\index.vue", "mtime": 1748351864000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "question", "showConversation", "currentConversation", "isLoading", "quickQuestions", "text", "icon", "recentQuestions", "title", "id", "questionTag", "answerTitle", "answerValue", "timestamp", "responseTime", "methods", "handleQuestionClick", "item", "backToHome", "createNewQuestion", "selectQuickQuestion", "handleSend", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "newConversation", "_t", "w", "_context", "n", "trim", "a", "p", "Promise", "resolve", "setTimeout", "substring", "Date", "now", "toLocaleString", "unshift", "v", "$message", "error", "f"], "sources": ["src/views/dataseek/index.vue"], "sourcesContent": ["<template>\n  <div class=\"dataseek-container\">\n    <!-- Left Sidebar -->\n    <div class=\"sidebar\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" class=\"new-question-btn\" @click=\"createNewQuestion\">新问题</el-button>\n      <div class=\"recent-questions\">\n        <div class=\"recent-questions-title\">最近问答</div>\n        <ul>\n          <li v-for=\"(item, index) in recentQuestions\" :key=\"index\" class=\"recent-question-item\" @click=\"handleQuestionClick(item)\">\n            {{ item.title }}\n          </li>\n        </ul>\n      </div>\n    </div>\n\n    <!-- Main Content -->\n    <div class=\"main-content\">\n      <!-- 对话显示区域 -->\n      <transition name=\"slide-fade\" mode=\"out-in\">\n        <div v-if=\"showConversation\" key=\"conversation\" class=\"conversation-area\">\n          <div class=\"conversation-header\">\n            <div class=\"header-avatar\">\n              <el-avatar :size=\"32\" style=\"background-color: #909399;\">{{ currentConversation.title.charAt(0) }}</el-avatar>\n              <span class=\"header-title\">{{ currentConversation.title }}</span>\n            </div>\n          </div>\n\n          <div class=\"conversation-content\">\n            <!-- 绿色标签区域 -->\n            <div class=\"context-tags\">\n              <div class=\"tag-item green-tag\">\n                <i class=\"el-icon-edit\" />\n                <span>输入上下文对话内容</span>\n                <i class=\"el-icon-arrow-down\" />\n              </div>\n              <div class=\"tag-item green-tag\">\n                <i class=\"el-icon-search\" />\n                <span>搜索问题</span>\n                <i class=\"el-icon-arrow-down\" />\n              </div>\n              <div class=\"tag-item green-tag\">\n                <i class=\"el-icon-search\" />\n                <span>搜索问题</span>\n                <i class=\"el-icon-arrow-down\" />\n              </div>\n            </div>\n\n            <!-- AI回答区域 -->\n            <div class=\"ai-response-section\">\n              <!-- 蓝色问题标签 -->\n              <div class=\"question-tag\">\n                <el-avatar :size=\"20\" style=\"background-color: #409EFF; margin-right: 8px;\">AI</el-avatar>\n                <span class=\"question-text\">{{ currentConversation.questionTag }}</span>\n                <i class=\"el-icon-info\" style=\"margin-left: 8px; color: #909399;\" />\n              </div>\n\n              <!-- 时间戳 -->\n              <div class=\"timestamp\">\n                {{ currentConversation.timestamp }} {{ currentConversation.question }}\n              </div>\n\n              <!-- 回答内容 -->\n              <div class=\"answer-content\">\n                <div class=\"answer-title\">{{ currentConversation.answerTitle }}</div>\n                <div class=\"answer-value\">{{ currentConversation.answerValue }}</div>\n              </div>\n\n              <!-- 操作按钮 -->\n              <div class=\"action-buttons\">\n                <div class=\"left-actions\">\n                  <el-button type=\"text\" icon=\"el-icon-thumb\" size=\"mini\" />\n                  <el-button type=\"text\" icon=\"el-icon-chat-dot-round\" size=\"mini\" />\n                  <el-button type=\"text\" icon=\"el-icon-arrow-right\" size=\"mini\" />\n                </div>\n                <div class=\"right-time\">{{ currentConversation.responseTime }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 默认AI问答区域 -->\n        <div v-else key=\"qa-area\" class=\"ai-qa-area\">\n          <div class=\"ai-welcome-section\">\n            <div class=\"ai-icon\">\n              <i class=\"el-icon-cpu\" />\n            </div>\n            <h2 class=\"ai-title\">AI智能问数</h2>\n            <p class=\"ai-subtitle\">基于大数据的智能分析助手，为您提供精准的数据洞察</p>\n          </div>\n\n          <!-- 快捷问题推荐 -->\n          <div class=\"quick-questions\">\n            <div class=\"quick-question-title\">推荐问题</div>\n            <div class=\"quick-question-list\">\n              <div\n                v-for=\"(item, index) in quickQuestions\"\n                :key=\"index\"\n                class=\"quick-question-item\"\n                @click=\"selectQuickQuestion(item)\"\n              >\n                <i :class=\"item.icon\" />\n                <span>{{ item.text }}</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"qa-input-container\">\n            <el-input\n              v-model=\"question\"\n              :disabled=\"isLoading\"\n              placeholder=\"请输入您的问题，Shift + Enter 换行\"\n              type=\"textarea\"\n              :rows=\"4\"\n              class=\"input-area-dataseek\"\n              @keydown.enter.shift.prevent=\"handleSend\"\n            />\n            <el-button\n              :loading=\"isLoading\"\n              :disabled=\"!question.trim()\"\n              class=\"send-button\"\n              @click=\"handleSend\"\n            >\n              <i v-if=\"!isLoading\" class=\"el-icon-s-promotion\" />\n            </el-button>\n          </div>\n        </div>\n      </transition>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DataSeekIndex',\n  data() {\n    return {\n      question: '',\n      showConversation: false,\n      currentConversation: {},\n      isLoading: false,\n      quickQuestions: [\n        {\n          text: '分析去年销售数据趋势',\n          icon: 'el-icon-trend-charts'\n        },\n        {\n          text: '查看产品利润率排行',\n          icon: 'el-icon-pie-chart'\n        },\n        {\n          text: '对比各地区业绩表现',\n          icon: 'el-icon-data-analysis'\n        },\n        {\n          text: '预测下季度销售额',\n          icon: 'el-icon-data-line'\n        }\n      ],\n      recentQuestions: [\n        {\n          title: '去年的产品利润率',\n          id: 'profit-rate-1',\n          question: '请告诉我去年的产品利润率',\n          questionTag: '利润率',\n          answerTitle: '利润率',\n          answerValue: '0.00%',\n          timestamp: '2024.01.01 12:31',\n          responseTime: '回答耗时 2024-01-01 07:01'\n        },\n        {\n          title: '你的知识截止到哪天',\n          id: 'knowledge-cutoff',\n          question: '请告诉我你的知识截止到哪天',\n          questionTag: '你的知识截止到哪天',\n          answerTitle: '知识截止时间',\n          answerValue: '我的知识截止到2024年4月，无法获取之后的最新信息。如果您需要最新的数据或信息，建议您查询最新的资料或咨询相关专业人士。',\n          timestamp: '2024.01.01 12:31',\n          responseTime: '回答耗时 2024-01-01 07:01'\n        },\n        {\n          title: '去年的产品利润率',\n          id: 'profit-rate-2',\n          question: '请告诉我去年的产品利润率',\n          questionTag: '利润率',\n          answerTitle: '利润率',\n          answerValue: '15.8%',\n          timestamp: '2024.01.01 14:25',\n          responseTime: '回答耗时 2024-01-01 09:25'\n        }\n      ]\n    }\n  },\n  methods: {\n    handleQuestionClick(item) {\n      this.currentConversation = item\n      this.showConversation = true\n    },\n    backToHome() {\n      this.showConversation = false\n      this.currentConversation = {}\n    },\n    createNewQuestion() {\n      // 回到默认的AI智能问数页面\n      this.showConversation = false\n      this.currentConversation = {}\n      this.question = '' // 清空输入框\n      this.isLoading = false\n    },\n    selectQuickQuestion(item) {\n      this.question = item.text\n    },\n    async handleSend() {\n      if (!this.question.trim() || this.isLoading) return\n\n      this.isLoading = true\n\n      // 模拟API调用\n      try {\n        await new Promise(resolve => setTimeout(resolve, 2000))\n\n        // 创建新的对话记录\n        const newConversation = {\n          title: this.question.substring(0, 20) + '...',\n          id: 'new-' + Date.now(),\n          question: this.question,\n          questionTag: this.question.substring(0, 10),\n          answerTitle: '分析结果',\n          answerValue: '根据您的问题，我正在分析相关数据...',\n          timestamp: new Date().toLocaleString(),\n          responseTime: '回答耗时 ' + new Date().toLocaleString()\n        }\n\n        // 添加到最近问答\n        this.recentQuestions.unshift(newConversation)\n\n        // 显示对话\n        this.currentConversation = newConversation\n        this.showConversation = true\n        this.question = ''\n      } catch (error) {\n        this.$message.error('发送失败，请重试')\n      } finally {\n        this.isLoading = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dataseek-container {\n  display: flex;\n  height: calc(100vh - 50px); // Adjust based on actual header height\n  background-color: #ffffff; // White background for the page\n}\n\n.sidebar {\n  width: 280px; // Fixed width for the sidebar\n  background-color: #f8f9fa; // Light grey background for sidebar\n  padding: 20px;\n  border-right: 1px solid #e0e0e0; // Separator line\n  display: flex;\n  flex-direction: column;\n\n  .new-question-btn {\n    width: 100%;\n    margin-bottom: 20px;\n  }\n\n  .recent-questions-title {\n    font-size: 14px;\n    color: #606266; // Subdued text color\n    margin-bottom: 10px;\n  }\n\n  .recent-questions ul {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .recent-question-item {\n    padding: 10px 5px;\n    font-size: 14px;\n    color: #303133;\n    cursor: pointer;\n    border-radius: 4px;\n    &:hover {\n      background-color: #ecf5ff; // Light blue on hover\n    }\n  }\n}\n\n.main-content {\n  flex-grow: 1;\n  padding: 40px; // More padding for the main area\n  display: flex;\n  flex-direction: column;\n  align-items: center; // Center content horizontally\n  justify-content: center; // Center content vertically\n  background-color: #ffffff;\n}\n\n// 对话区域样式\n.conversation-area {\n  width: 100%;\n  max-width: 900px;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: flex-start;\n  padding: 20px;\n}\n\n.conversation-header {\n  width: 100%;\n  padding: 15px 0;\n  margin-bottom: 20px;\n\n  .header-avatar {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .header-title {\n      font-size: 16px;\n      color: #303133;\n      font-weight: 500;\n    }\n  }\n}\n\n.conversation-content {\n  width: 100%;\n  flex: 1;\n}\n\n// 绿色标签区域\n.context-tags {\n  margin-bottom: 25px;\n\n  .tag-item {\n    display: inline-flex;\n    align-items: center;\n    gap: 6px;\n    padding: 8px 12px;\n    margin-right: 10px;\n    margin-bottom: 8px;\n    border-radius: 16px;\n    font-size: 13px;\n    cursor: pointer;\n\n    &.green-tag {\n      background-color: #f0f9ff;\n      color: #10b981;\n      border: 1px solid #10b981;\n\n      i {\n        font-size: 12px;\n      }\n\n      &:hover {\n        background-color: #e6f7ff;\n      }\n    }\n  }\n}\n\n// AI回答区域\n.ai-response-section {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #f0f0f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n// 蓝色问题标签\n.question-tag {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12px;\n\n  .question-text {\n    background-color: #e6f3ff;\n    color: #1890ff;\n    padding: 6px 12px;\n    border-radius: 12px;\n    font-size: 13px;\n    border: 1px solid #1890ff;\n  }\n}\n\n// 时间戳\n.timestamp {\n  font-size: 12px;\n  color: #8c8c8c;\n  margin-bottom: 15px;\n  line-height: 1.4;\n}\n\n// 回答内容\n.answer-content {\n  margin-bottom: 20px;\n\n  .answer-title {\n    font-size: 14px;\n    color: #303133;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .answer-value {\n    font-size: 24px;\n    color: #303133;\n    font-weight: 600;\n    line-height: 1.4;\n  }\n}\n\n// 操作按钮\n.action-buttons {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .left-actions {\n    display: flex;\n    gap: 8px;\n\n    .el-button {\n      color: #8c8c8c;\n      padding: 6px;\n      border: none;\n      background: none;\n\n      &:hover {\n        color: #1890ff;\n        background-color: #f0f8ff;\n      }\n    }\n  }\n\n  .right-time {\n    font-size: 11px;\n    color: #bfbfbf;\n  }\n}\n\n.ai-qa-area {\n  width: 100%;\n  max-width: 800px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  animation: fadeInUp 0.6s ease-out;\n}\n\n// 欢迎区域\n.ai-welcome-section {\n  text-align: center;\n  margin-bottom: 40px;\n\n  .ai-icon {\n    width: 80px;\n    height: 80px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0 auto 20px;\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n\n    i {\n      font-size: 36px;\n      color: white;\n    }\n  }\n\n  .ai-title {\n    font-size: 32px;\n    font-weight: 600;\n    color: #303133;\n    margin-bottom: 12px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n\n  .ai-subtitle {\n    font-size: 16px;\n    color: #8c8c8c;\n    line-height: 1.6;\n    margin: 0;\n  }\n}\n\n// 快捷问题区域\n.quick-questions {\n  width: 100%;\n  margin-bottom: 40px;\n\n  .quick-question-title {\n    font-size: 16px;\n    font-weight: 500;\n    color: #303133;\n    margin-bottom: 16px;\n    text-align: center;\n  }\n\n  .quick-question-list {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 12px;\n\n    .quick-question-item {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      padding: 12px 16px;\n      background-color: #f8f9fa;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      font-size: 14px;\n      color: #495057;\n\n      i {\n        font-size: 16px;\n        color: #667eea;\n      }\n\n      &:hover {\n        background-color: #e3f2fd;\n        border-color: #667eea;\n        transform: translateY(-2px);\n        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);\n      }\n    }\n  }\n}\n\n.qa-input-container {\n  width: 100%;\n  position: relative;\n\n  .input-area-dataseek ::v-deep .el-textarea__inner {\n    border-radius: 12px;\n    padding: 20px;\n    padding-right: 70px;\n    font-size: 16px;\n    min-height: 120px !important;\n    border: 2px solid #e9ecef;\n    background-color: #ffffff;\n    transition: all 0.3s ease;\n    resize: none;\n\n    &:focus {\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n\n    &:disabled {\n      background-color: #f8f9fa;\n      cursor: not-allowed;\n    }\n  }\n\n  .send-button {\n    position: absolute;\n    right: 15px;\n    bottom: 15px;\n    border-radius: 50%;\n    width: 48px;\n    height: 48px;\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    border: none;\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n    transition: all 0.3s ease;\n\n    &:hover:not(:disabled) {\n      transform: translateY(-2px);\n      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);\n    }\n\n    &:disabled {\n      background: #d1d5db;\n      box-shadow: none;\n      cursor: not-allowed;\n      transform: none;\n    }\n\n    i {\n      font-size: 18px;\n    }\n  }\n}\n\n// 动画效果\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// 侧边栏优化\n.sidebar {\n  .new-question-btn {\n    border-radius: 8px;\n    font-weight: 500;\n    transition: all 0.3s ease;\n\n    &:hover {\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n    }\n  }\n\n  .recent-question-item {\n    transition: all 0.3s ease;\n    position: relative;\n    overflow: hidden;\n\n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 0;\n      height: 100%;\n      width: 3px;\n      background-color: #409EFF;\n      transform: scaleY(0);\n      transition: transform 0.3s ease;\n    }\n\n    &:hover::before {\n      transform: scaleY(1);\n    }\n\n    &:hover {\n      padding-left: 15px;\n    }\n  }\n}\n\n// 过渡动画\n.slide-fade-enter-active,\n.slide-fade-leave-active {\n  transition: all 0.4s ease;\n}\n\n.slide-fade-enter-from {\n  opacity: 0;\n  transform: translateX(30px);\n}\n\n.slide-fade-leave-to {\n  opacity: 0;\n  transform: translateX(-30px);\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .dataseek-container {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .sidebar {\n    width: 100%;\n    padding: 15px;\n    border-right: none;\n    border-bottom: 1px solid #e0e0e0;\n  }\n\n  .main-content {\n    padding: 20px;\n  }\n\n  .ai-qa-area {\n    max-width: 100%;\n  }\n\n  .quick-question-list {\n    grid-template-columns: 1fr;\n  }\n\n  .conversation-area {\n    padding: 10px;\n  }\n}\n\n@media (max-width: 480px) {\n  .ai-welcome-section {\n    .ai-icon {\n      width: 60px;\n      height: 60px;\n\n      i {\n        font-size: 28px;\n      }\n    }\n\n    .ai-title {\n      font-size: 24px;\n    }\n\n    .ai-subtitle {\n      font-size: 14px;\n    }\n  }\n\n  .qa-input-container {\n    .input-area-dataseek ::v-deep .el-textarea__inner {\n      min-height: 100px !important;\n      padding: 15px;\n      padding-right: 60px;\n    }\n\n    .send-button {\n      width: 40px;\n      height: 40px;\n      right: 10px;\n      bottom: 10px;\n    }\n  }\n}\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAoIA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,cAAA,GACA;QACAC,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA,EACA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,EAAA;QACAT,QAAA;QACAU,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;MACA,GACA;QACAN,KAAA;QACAC,EAAA;QACAT,QAAA;QACAU,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;MACA,GACA;QACAN,KAAA;QACAC,EAAA;QACAT,QAAA;QACAU,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAAf,mBAAA,GAAAe,IAAA;MACA,KAAAhB,gBAAA;IACA;IACAiB,UAAA,WAAAA,WAAA;MACA,KAAAjB,gBAAA;MACA,KAAAC,mBAAA;IACA;IACAiB,iBAAA,WAAAA,kBAAA;MACA;MACA,KAAAlB,gBAAA;MACA,KAAAC,mBAAA;MACA,KAAAF,QAAA;MACA,KAAAG,SAAA;IACA;IACAiB,mBAAA,WAAAA,oBAAAH,IAAA;MACA,KAAAjB,QAAA,GAAAiB,IAAA,CAAAZ,IAAA;IACA;IACAgB,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,eAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAA,MACA,CAAAV,KAAA,CAAAtB,QAAA,CAAAiC,IAAA,MAAAX,KAAA,CAAAnB,SAAA;gBAAA4B,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAG,CAAA;YAAA;cAEAZ,KAAA,CAAAnB,SAAA;;cAEA;cAAA4B,QAAA,CAAAI,CAAA;cAAAJ,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAI,OAAA,WAAAC,OAAA;gBAAA,OAAAC,UAAA,CAAAD,OAAA;cAAA;YAAA;cAEA;cACAT,eAAA;gBACApB,KAAA,EAAAc,KAAA,CAAAtB,QAAA,CAAAuC,SAAA;gBACA9B,EAAA,WAAA+B,IAAA,CAAAC,GAAA;gBACAzC,QAAA,EAAAsB,KAAA,CAAAtB,QAAA;gBACAU,WAAA,EAAAY,KAAA,CAAAtB,QAAA,CAAAuC,SAAA;gBACA5B,WAAA;gBACAC,WAAA;gBACAC,SAAA,MAAA2B,IAAA,GAAAE,cAAA;gBACA5B,YAAA,gBAAA0B,IAAA,GAAAE,cAAA;cACA,GAEA;cACApB,KAAA,CAAAf,eAAA,CAAAoC,OAAA,CAAAf,eAAA;;cAEA;cACAN,KAAA,CAAApB,mBAAA,GAAA0B,eAAA;cACAN,KAAA,CAAArB,gBAAA;cACAqB,KAAA,CAAAtB,QAAA;cAAA+B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAI,CAAA;cAAAN,EAAA,GAAAE,QAAA,CAAAa,CAAA;cAEAtB,KAAA,CAAAuB,QAAA,CAAAC,KAAA;YAAA;cAAAf,QAAA,CAAAI,CAAA;cAEAb,KAAA,CAAAnB,SAAA;cAAA,OAAA4B,QAAA,CAAAgB,CAAA;YAAA;cAAA,OAAAhB,QAAA,CAAAG,CAAA;UAAA;QAAA,GAAAP,OAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}