from sqlalchemy import Column, String, BigInteger, DateTime, Text, ForeignKey
from config.database import Base
import datetime

class UserSetting(Base):
    __tablename__ = 'user_settings'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='设置ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    setting_type = Column(String(50), nullable=False, comment='设置类型(search_settings, analysis_settings)')
    setting_key = Column(String(100), nullable=False, comment='设置键')
    setting_value = Column(Text, comment='设置值(JSON格式)')
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='更新时间')
