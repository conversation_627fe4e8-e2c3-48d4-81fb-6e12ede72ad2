#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, Query
from typing import List, Optional
from app.services.pinboard_service import PinboardService
from app.schemas.pinboard_schemas import (
    PinboardListResponse,
    PinboardCreateRequest,
    PinboardResponse,
    TagListResponse,
    CollaborateReportListResponse,
    SharedReportListResponse,
    FollowedReportListResponse,
    ReminderListResponse,
    ReminderCreateRequest,
    TrashReportListResponse
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel

router = APIRouter(prefix='/pinboard')

@router.get("/test", summary="测试接口")
async def test_pinboard():
    """
    测试Pinboard接口是否正常工作
    """
    try:
        return ResponseUtil.success(data={"message": "Pinboard接口正常工作", "timestamp": "2024-12-28"}, msg="测试成功")
    except Exception as e:
        logger.error(f"测试接口失败: {str(e)}")
        return ResponseUtil.error(msg=f"测试接口失败: {str(e)}")

@router.get("/list", response_model=PinboardListResponse, summary="获取Pinboard列表")
async def get_pinboard_list(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    project_id: Optional[int] = Query(None, description="项目ID")
):
    """
    获取用户的Pinboard列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求Pinboard列表")

        # 调用服务层获取数据
        result = await PinboardService.get_user_pinboards(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search,
            project_id=project_id
        )

        logger.info(f"成功获取Pinboard列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取Pinboard列表成功")

    except Exception as e:
        logger.error(f"获取Pinboard列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取Pinboard列表失败: {str(e)}")

@router.post("/create", response_model=PinboardResponse, summary="创建Pinboard")
async def create_pinboard(
    request: PinboardCreateRequest,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    创建新的Pinboard
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 创建Pinboard: {request.name}")

        # 调用服务层创建Pinboard
        result = await PinboardService.create_pinboard(
            request=request,
            user_id=current_user.user.user_id,
            username=current_user.user.user_name
        )

        logger.info(f"成功创建Pinboard: {result.uid}")
        return ResponseUtil.success(data=result, msg="创建Pinboard成功")

    except Exception as e:
        logger.error(f"创建Pinboard失败: {str(e)}")
        return ResponseUtil.error(msg=f"创建Pinboard失败: {str(e)}")

@router.get("/tags", response_model=TagListResponse, summary="获取标签列表")
async def get_tags(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    project_id: Optional[int] = Query(None, description="项目ID")
):
    """
    获取标签列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求标签列表")

        # 调用服务层获取标签
        result = await PinboardService.get_tags(
            user_id=current_user.user.user_id,
            project_id=project_id
        )

        logger.info(f"成功获取标签列表，共 {len(result)} 个标签")
        return ResponseUtil.success(data=result, msg="获取标签列表成功")

    except Exception as e:
        logger.error(f"获取标签列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取标签列表失败: {str(e)}")

@router.delete("/{pinboard_uid}", summary="删除Pinboard")
async def delete_pinboard(
    pinboard_uid: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    删除Pinboard
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 删除Pinboard: {pinboard_uid}")

        # 调用服务层删除Pinboard
        await PinboardService.delete_pinboard(
            pinboard_uid=pinboard_uid,
            user_id=current_user.user.user_id
        )

        logger.info(f"成功删除Pinboard: {pinboard_uid}")
        return ResponseUtil.success(msg="删除Pinboard成功")

    except Exception as e:
        logger.error(f"删除Pinboard失败: {str(e)}")
        return ResponseUtil.error(msg=f"删除Pinboard失败: {str(e)}")

@router.get("/my", response_model=PinboardListResponse, summary="获取我的Pinboard")
async def get_my_pinboards(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """
    获取当前用户创建的Pinboard列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求我的Pinboard列表")

        # 调用服务层获取用户自己的Pinboard
        result = await PinboardService.get_my_pinboards(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search
        )

        logger.info(f"成功获取我的Pinboard列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取我的Pinboard列表成功")

    except Exception as e:
        logger.error(f"获取我的Pinboard列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取我的Pinboard列表失败: {str(e)}")

@router.get("/collaborate", response_model=CollaborateReportListResponse, summary="获取协作报告列表")
async def get_collaborate_reports(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    tag: Optional[str] = Query(None, description="标签筛选")
):
    """
    获取协作报告列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求协作报告列表")

        # 调用服务层获取协作报告
        result = await PinboardService.get_collaborate_reports(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search,
            tag=tag
        )

        logger.info(f"成功获取协作报告列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取协作报告列表成功")

    except Exception as e:
        logger.error(f"获取协作报告列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取协作报告列表失败: {str(e)}")

@router.get("/shared", response_model=SharedReportListResponse, summary="获取共享报告列表")
async def get_shared_reports(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    filter_type: Optional[str] = Query(None, description="筛选类型")
):
    """
    获取共享报告列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求共享报告列表")

        # 调用服务层获取共享报告
        result = await PinboardService.get_shared_reports(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search,
            filter_type=filter_type
        )

        logger.info(f"成功获取共享报告列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取共享报告列表成功")

    except Exception as e:
        logger.error(f"获取共享报告列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取共享报告列表失败: {str(e)}")

@router.get("/followed", response_model=FollowedReportListResponse, summary="获取关注报告列表")
async def get_followed_reports(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category: Optional[str] = Query(None, description="分类筛选")
):
    """
    获取关注报告列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求关注报告列表")

        # 调用服务层获取关注报告
        result = await PinboardService.get_followed_reports(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search,
            category=category
        )

        logger.info(f"成功获取关注报告列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取关注报告列表成功")

    except Exception as e:
        logger.error(f"获取关注报告列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取关注报告列表失败: {str(e)}")

@router.get("/reminders", response_model=ReminderListResponse, summary="获取定时提醒列表")
async def get_reminders(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[str] = Query(None, description="状态筛选")
):
    """
    获取定时提醒列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求定时提醒列表")

        # 调用服务层获取定时提醒
        result = await PinboardService.get_reminders(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search,
            status=status
        )

        logger.info(f"成功获取定时提醒列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取定时提醒列表成功")

    except Exception as e:
        logger.error(f"获取定时提醒列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取定时提醒列表失败: {str(e)}")

@router.post("/reminders", summary="创建定时提醒")
async def create_reminder(
    request: ReminderCreateRequest,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    创建定时提醒
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 创建定时提醒: {request.name}")

        # 调用服务层创建提醒
        result = await PinboardService.create_reminder(
            request=request,
            user_id=current_user.user.user_id,
            create_by=current_user.user.user_name
        )

        logger.info(f"成功创建定时提醒: {request.name}")
        return ResponseUtil.success(data=result, msg="创建定时提醒成功")

    except Exception as e:
        logger.error(f"创建定时提醒失败: {str(e)}")
        return ResponseUtil.error(msg=f"创建定时提醒失败: {str(e)}")

@router.get("/trash", response_model=TrashReportListResponse, summary="获取回收站报告列表")
async def get_trash_reports(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: Optional[str] = Query("delete_time", description="排序字段")
):
    """
    获取回收站报告列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求回收站报告列表")

        # 调用服务层获取回收站报告
        result = await PinboardService.get_trash_reports(
            user_id=current_user.user.user_id,
            page=page,
            size=size,
            search=search,
            sort_by=sort_by
        )

        logger.info(f"成功获取回收站报告列表，共 {result['total']} 条记录")
        return ResponseUtil.success(data=result, msg="获取回收站报告列表成功")

    except Exception as e:
        logger.error(f"获取回收站报告列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取回收站报告列表失败: {str(e)}")

@router.post("/trash/{pinboard_uid}/restore", summary="恢复报告")
async def restore_report(
    pinboard_uid: str,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    从回收站恢复报告
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 恢复报告: {pinboard_uid}")

        # 调用服务层恢复报告
        result = await PinboardService.restore_report(
            pinboard_uid=pinboard_uid,
            user_id=current_user.user.user_id,
            restore_by=current_user.user.user_name
        )

        logger.info(f"成功恢复报告: {pinboard_uid}")
        return ResponseUtil.success(data=result, msg="恢复报告成功")

    except Exception as e:
        logger.error(f"恢复报告失败: {str(e)}")
        return ResponseUtil.error(msg=f"恢复报告失败: {str(e)}")
