{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\dataeye\\index.vue?vue&type=style&index=0&id=19045b40&lang=scss&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\dataeye\\index.vue", "mtime": 1748160488000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmRhdGFleWUtY29udGFpbmVyIHsKICBwYWRkaW5nOiAyMHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgbWluLWhlaWdodDogMTAwdmg7CgogIC5haS1xYS1zZWN0aW9uIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICBwYWRkaW5nOiAyMHB4OwogICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsKCiAgICAuc2VjdGlvbi10aXRsZSB7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgZm9udC1zaXplOiAyMHB4OwogICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgIH0KCiAgICAucWEtY29udGVudCB7CiAgICAgIC5xdWVzdGlvbi1pbnB1dCB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAgICAgLmlucHV0LWFyZWEgewogICAgICAgICAgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lciB7CiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7CiAgICAgICAgICAgIGhlaWdodDogNDBweDsKICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7CiAgICAgICAgICB9CgogICAgICAgICAgOjp2LWRlZXAgLmVsLWlucHV0LWdyb3VwX19hcHBlbmQgewogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwIDIwcHggMjBweCAwOwogICAgICAgICAgICBib3JkZXItbGVmdDogbm9uZTsKCiAgICAgICAgICAgIC5lbC1idXR0b24gewogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAgMjBweCAyMHB4IDA7CiAgICAgICAgICAgICAgaGVpZ2h0OiAzOHB4OwogICAgICAgICAgICAgIHdpZHRoOiA0MHB4OwogICAgICAgICAgICAgIHBhZGRpbmc6IDA7CiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAuaGlzdG9yeS1zZWN0aW9uIHsKICAgICAgICAuaGlzdG9yeS1oZWFkZXIgewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAgICAgICAgIC5oaXN0b3J5LXNlYXJjaC10aXRsZSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgICAgY29sb3I6ICM5MDkzOTk7CiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgICAgICB9CgogICAgICAgICAgLmNsZWFyLWJ0biB7CiAgICAgICAgICAgIGNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICBwYWRkaW5nOiAwOwogICAgICAgICAgICBmb250LXNpemU6IDEycHg7CgogICAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgICBjb2xvcjogIzY2YjFmZjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmhpc3RvcnktdGFncyB7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAgICAgICAgIC5oaXN0b3J5LXRhZyB7CiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOGY0ZmY7CiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7CiAgICAgICAgICAgIGNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNXB4OwogICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4OwogICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgICAgICAgICAgIHBhZGRpbmc6IDZweCAxMnB4OwogICAgICAgICAgICBmb250LXNpemU6IDEzcHg7CgogICAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICAgIGNvbG9yOiAjZmZmOwogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgOjp2LWRlZXAgLmVsLXRhZ19fY2xvc2UgewogICAgICAgICAgICAgIGNvbG9yOiAjNDA5RUZGOwoKICAgICAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICAgICAgICAgICAgICBjb2xvcjogI2Y1NmM2YzsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAudG9nZ2xlLWJ0biB7CiAgICAgICAgICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgICAgICAgICBwYWRkaW5nOiAwOwogICAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiA4cHg7CgogICAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmNvbW1vbi1xdWVzdGlvbnMtc2VjdGlvbiB7CiAgICAgICAgICAuc2VjdGlvbi1zdWJ0aXRsZSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgICAgY29sb3I6ICM5MDkzOTk7CiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgICAgICB9CgogICAgICAgICAgLnF1ZXN0aW9uLXRhZ3MgewogICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7CiAgICAgICAgICAgIGdhcDogOHB4OwoKICAgICAgICAgICAgLnF1ZXN0aW9uLXRhZyB7CiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjJmNTsKICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2OwogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE1cHg7CiAgICAgICAgICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgICAgICAgIHBhZGRpbmc6IDZweCAxMnB4OwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKCiAgICAgICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM0MDlFRkY7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQoKICAvLyDmlbDmja7ku6rooajmnb/moLflvI8KICAuZGFzaGJvYXJkLXNlY3Rpb24gewogICAgbWFyZ2luLXRvcDogMjBweDsKCiAgICAuZGF0YS1jYXJkcy1zZWN0aW9uIHsKICAgICAgZGlzcGxheTogZ3JpZDsKICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAzMDBweCAxZnI7CiAgICAgIGdhcDogMjBweDsKICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAgIC5kYXRhLWNhcmQgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgICAgICAgJi53aWRlLWNhcmQgewogICAgICAgICAgZ3JpZC1jb2x1bW46IHNwYW4gMTsKICAgICAgICB9CgogICAgICAgIC5jYXJkLWhlYWRlciB7CiAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgIHBhZGRpbmc6IDE2cHggMjBweDsKICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsKCiAgICAgICAgICAuY2FyZC10aXRsZSB7CiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgICAgICAgY29sb3I6ICMzMDMxMzM7CgogICAgICAgICAgICBpIHsKICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgICAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgfQogICAgICAgICAgfQoKICAgICAgICAgIC5jYXJkLWFjdGlvbnMgewogICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICBnYXA6IDRweDsKCiAgICAgICAgICAgIC5lbC1idXR0b24gewogICAgICAgICAgICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICAgICAgICAgICAgY29sb3I6ICM5MDkzOTk7CgogICAgICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICAgICAgY29sb3I6ICM0MDlFRkY7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuY2FyZC1jb250ZW50IHsKICAgICAgICAgIHBhZGRpbmc6IDIwcHg7CgogICAgICAgICAgLm1haW4tdmFsdWUgewogICAgICAgICAgICBmb250LXNpemU6IDMycHg7CiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4OwogICAgICAgICAgfQoKICAgICAgICAgIC5zdWItaW5mbyB7CiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAgICAgICAgIC5kYXRlLWluZm8gewogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgICAgICBjb2xvcjogIzkwOTM5OTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLnRyZW5kLWluZm8gewogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CgogICAgICAgICAgICAgICYucG9zaXRpdmUgewogICAgICAgICAgICAgICAgY29sb3I6ICM2N0MyM0E7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAmLm5lZ2F0aXZlIHsKICAgICAgICAgICAgICAgIGNvbG9yOiAjRjU2QzZDOwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgaSB7CiAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDJweDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAuZGF0ZS1pbmZvIHsKICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgICBjb2xvcjogIzkwOTM5OTsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsKICAgICAgICAgIH0KCiAgICAgICAgICAuY2hhcnQtY29udGFpbmVyIHsKICAgICAgICAgICAgLmNoYXJ0IHsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC5pbnNpZ2h0cy1zZWN0aW9uIHsKICAgICAgLmluc2lnaHRzLWNhcmQgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgICAgICAgLmNhcmQtaGVhZGVyIHsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgcGFkZGluZzogMTZweCAyMHB4OwogICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOwoKICAgICAgICAgIC5jYXJkLXRpdGxlIHsKICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKCiAgICAgICAgICAgIGkgewogICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4OwogICAgICAgICAgICAgIGNvbG9yOiAjRTZBMjNDOwogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgLmNhcmQtYWN0aW9ucyB7CiAgICAgICAgICAgIC5lbC1idXR0b24gewogICAgICAgICAgICAgIHBhZGRpbmc6IDRweCAxMnB4OwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKCiAgICAgICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNmNWZmOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmNhcmQtY29udGVudCB7CiAgICAgICAgICBwYWRkaW5nOiAyMHB4OwoKICAgICAgICAgIC5pbnNpZ2h0LWl0ZW0gewogICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsKCiAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLmluc2lnaHQtaWNvbiB7CiAgICAgICAgICAgICAgd2lkdGg6IDMycHg7CiAgICAgICAgICAgICAgaGVpZ2h0OiAzMnB4OwogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4OwogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNGREY2RUM7CiAgICAgICAgICAgICAgY29sb3I6ICNFNkEyM0M7CgogICAgICAgICAgICAgICYuc3VjY2VzcyB7CiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjBGOUZGOwogICAgICAgICAgICAgICAgY29sb3I6ICM2N0MyM0E7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICBpIHsKICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC5pbnNpZ2h0LWNvbnRlbnQgewogICAgICAgICAgICAgIGZsZXg6IDE7CgogICAgICAgICAgICAgIC5pbnNpZ2h0LXRpdGxlIHsKICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICAgICAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC5pbnNpZ2h0LWRlc2MgewogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS41OwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwZA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dataeye", "sourcesContent": ["<template>\n  <div class=\"dataeye-container\">\n    <!-- AI智能问数区域 -->\n    <div class=\"ai-qa-section\">\n      <div class=\"qa-container\">\n        <h2 class=\"section-title\">AI智能问数</h2>\n        <div class=\"qa-content\">\n          <!-- 问题输入框 -->\n          <div class=\"question-input\">\n            <el-input\n              v-model=\"question\"\n              placeholder=\"请输入您的问题，Shift + Enter 换行\"\n              class=\"input-area\"\n              @keyup.enter.native=\"handleSearch\"\n            >\n              <el-button\n                slot=\"append\"\n                icon=\"el-icon-s-promotion\"\n                @click=\"handleSearch\"\n              ></el-button>\n            </el-input>\n          </div>\n\n          <!-- 历史搜索区域 -->\n          <div class=\"history-section\">\n            <div class=\"history-header\">\n              <div class=\"history-search-title\">历史搜索</div>\n              <el-button\n                v-if=\"searchHistory.length > 0\"\n                type=\"text\"\n                size=\"small\"\n                @click=\"clearHistory\"\n                class=\"clear-btn\"\n              >\n                清空历史\n              </el-button>\n            </div>\n\n            <!-- 搜索历史标签 -->\n            <div v-if=\"searchHistory.length > 0\" class=\"history-tags\">\n              <el-tag\n                v-for=\"(item, index) in displayHistory\"\n                :key=\"index\"\n                class=\"history-tag\"\n                closable\n                @click=\"selectQuestion(item.content)\"\n                @close=\"removeHistoryItem(index)\"\n              >\n                {{ item.content }}\n              </el-tag>\n              <el-button\n                v-if=\"searchHistory.length > maxDisplayHistory\"\n                type=\"text\"\n                size=\"small\"\n                @click=\"showAllHistory = !showAllHistory\"\n                class=\"toggle-btn\"\n              >\n                {{ showAllHistory ? '收起' : `查看全部(${searchHistory.length})` }}\n              </el-button>\n            </div>\n\n            <!-- 常用问题标签 -->\n            <div class=\"common-questions-section\">\n              <div class=\"section-subtitle\">常用问题</div>\n              <div class=\"question-tags\">\n                <el-tag\n                  v-for=\"(tag, index) in commonQuestions\"\n                  :key=\"index\"\n                  class=\"question-tag\"\n                  @click=\"selectQuestion(tag)\"\n                >\n                  {{ tag }}\n                </el-tag>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据仪表板区域 -->\n    <div class=\"dashboard-section\">\n      <!-- 数据卡片区域 -->\n      <div class=\"data-cards-section\">\n        <!-- 营业额卡片 -->\n        <div class=\"data-card\">\n          <div class=\"card-header\">\n            <div class=\"card-title\">\n              <i class=\"el-icon-s-data\"></i>\n              营业额/万元\n            </div>\n            <div class=\"card-actions\">\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\n            </div>\n          </div>\n          <div class=\"card-content\">\n            <div class=\"main-value\">165.32</div>\n            <div class=\"sub-info\">\n              <span class=\"date-info\">2024-01-01 至 14:23</span>\n              <span class=\"trend-info positive\">\n                <i class=\"el-icon-top\"></i>\n                同比 +14.5%\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 门店营业额前十名卡片 -->\n        <div class=\"data-card wide-card\">\n          <div class=\"card-header\">\n            <div class=\"card-title\">\n              <i class=\"el-icon-s-shop\"></i>\n              门店营业额前十名\n            </div>\n            <div class=\"card-actions\">\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-refresh\"></el-button>\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-download\"></el-button>\n              <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\n            </div>\n          </div>\n          <div class=\"card-content\">\n            <div class=\"date-info\">2024-01-01 至 17:31</div>\n            <div class=\"chart-container\">\n              <div ref=\"storeChart\" class=\"chart\" style=\"height: 300px;\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能洞察区域 -->\n      <div class=\"insights-section\">\n        <div class=\"insights-card\">\n          <div class=\"card-header\">\n            <div class=\"card-title\">\n              <i class=\"el-icon-lightbulb\"></i>\n              智能洞察\n            </div>\n            <div class=\"card-actions\">\n              <el-button type=\"text\" size=\"mini\">智能分析</el-button>\n            </div>\n          </div>\n          <div class=\"card-content\">\n            <div class=\"insight-item\">\n              <div class=\"insight-icon\">\n                <i class=\"el-icon-warning-outline\"></i>\n              </div>\n              <div class=\"insight-content\">\n                <div class=\"insight-title\">营业额下降提醒</div>\n                <div class=\"insight-desc\">本周营业额相比上周下降了8.2%，建议关注门店运营情况</div>\n              </div>\n            </div>\n            <div class=\"insight-item\">\n              <div class=\"insight-icon success\">\n                <i class=\"el-icon-success\"></i>\n              </div>\n              <div class=\"insight-content\">\n                <div class=\"insight-title\">会员增长良好</div>\n                <div class=\"insight-desc\">新增会员数量环比增长15.3%，会员活跃度持续提升</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport cache from '@/plugins/cache'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'DataeyeIndex',\n  data() {\n    return {\n      activeIndex: '1',\n      question: '',\n      searchHistory: [],\n      showAllHistory: false,\n      maxDisplayHistory: 8, // 默认显示的历史记录数量\n      commonQuestions: [\n        '上季度营业额',\n        '今年Q1营业额每天',\n        '去年营业额前十的门店同比情况',\n        '今年Q1营业额每天品牌会员客流趋势上海北京',\n        '营业额每月',\n        '营业额降序的5个品牌',\n        '上季度营业额目标是多少',\n        '去年的营业额',\n        '去年门店是(三里屯)品牌是(耐克)门店面积营业额'\n      ],\n      storeChart: null,\n      chartData: {\n        stores: ['三里屯店', '王府井店', '西单店', '朝阳店', '海淀店', '丰台店', '石景山店', '通州店', '昌平店', '大兴店'],\n        revenue: [6892.192, 5482.192, 5200.007, 4702.477, 4200.477, 3962.577, 3425.547, 2894.477, 2638.849, 2114.669],\n        growth: [4.7, 3.2, -2.5, 1.8, -0.5, 2.1, -1.2, 0.8, 1.5, -0.3]\n      }\n    }\n  },\n  computed: {\n    displayHistory() {\n      if (this.showAllHistory) {\n        return this.searchHistory\n      }\n      return this.searchHistory.slice(0, this.maxDisplayHistory)\n    }\n  },\n  mounted() {\n    this.loadSearchHistory()\n    this.$nextTick(() => {\n      this.initStoreChart()\n    })\n  },\n  beforeDestroy() {\n    if (this.storeChart) {\n      this.storeChart.dispose()\n    }\n  },\n  methods: {\n    // 处理搜索\n    handleSearch() {\n      const query = this.question.trim()\n      if (!query) {\n        this.$message.warning('请输入搜索内容')\n        return\n      }\n\n      // 添加到搜索历史\n      this.addToHistory(query)\n\n      // 这里可以添加实际的搜索逻辑\n      this.$message.success(`正在搜索: ${query}`)\n\n      // 清空输入框\n      this.question = ''\n    },\n\n    // 选择问题\n    selectQuestion(question) {\n      this.question = question\n      // 可以选择是否立即搜索\n      // this.handleSearch()\n    },\n\n    // 添加到搜索历史\n    addToHistory(query) {\n      // 检查是否已存在\n      const existingIndex = this.searchHistory.findIndex(item => item.content === query)\n\n      if (existingIndex !== -1) {\n        // 如果已存在，移除旧的记录\n        this.searchHistory.splice(existingIndex, 1)\n      }\n\n      // 添加到开头\n      this.searchHistory.unshift({\n        content: query,\n        time: new Date().toLocaleString()\n      })\n\n      // 限制历史记录数量（最多保存50条）\n      if (this.searchHistory.length > 50) {\n        this.searchHistory = this.searchHistory.slice(0, 50)\n      }\n\n      // 保存到本地存储\n      this.saveSearchHistory()\n    },\n\n    // 移除单个历史记录\n    removeHistoryItem(index) {\n      const actualIndex = this.showAllHistory ? index : index\n      this.searchHistory.splice(actualIndex, 1)\n      this.saveSearchHistory()\n    },\n\n    // 清空历史记录\n    clearHistory() {\n      this.$confirm('确定要清空所有搜索历史吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.searchHistory = []\n        this.showAllHistory = false\n        this.saveSearchHistory()\n        this.$message.success('搜索历史已清空')\n      }).catch(() => {\n        // 用户取消\n      })\n    },\n\n    // 保存搜索历史到本地存储\n    saveSearchHistory() {\n      cache.local.setJSON('dataeye_search_history', this.searchHistory)\n    },\n\n    // 从本地存储加载搜索历史\n    loadSearchHistory() {\n      const history = cache.local.getJSON('dataeye_search_history')\n      if (history && Array.isArray(history)) {\n        this.searchHistory = history\n      }\n    },\n\n    // 初始化门店营业额图表\n    initStoreChart() {\n      if (!this.$refs.storeChart) return\n\n      this.storeChart = echarts.init(this.$refs.storeChart)\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          data: ['营业额', '增长率'],\n          top: 10\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.chartData.stores,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万)',\n            position: 'left',\n            axisLabel: {\n              formatter: '{value}'\n            }\n          },\n          {\n            type: 'value',\n            name: '增长率(%)',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额',\n            type: 'bar',\n            data: this.chartData.revenue,\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                { offset: 0, color: '#83bff6' },\n                { offset: 0.5, color: '#188df0' },\n                { offset: 1, color: '#188df0' }\n              ])\n            },\n            emphasis: {\n              itemStyle: {\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n                  { offset: 0, color: '#2378f7' },\n                  { offset: 0.7, color: '#2378f7' },\n                  { offset: 1, color: '#83bff6' }\n                ])\n              }\n            }\n          },\n          {\n            name: '增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.chartData.growth,\n            itemStyle: {\n              color: '#ffc658'\n            },\n            lineStyle: {\n              color: '#ffc658',\n              width: 2\n            }\n          }\n        ]\n      }\n\n      this.storeChart.setOption(option)\n\n      // 响应式处理\n      window.addEventListener('resize', () => {\n        if (this.storeChart) {\n          this.storeChart.resize()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dataeye-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n\n  .ai-qa-section {\n    background-color: #fff;\n    padding: 20px;\n    border-radius: 4px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n\n    .section-title {\n      text-align: center;\n      font-size: 20px;\n      color: #303133;\n      margin-bottom: 20px;\n    }\n\n    .qa-content {\n      .question-input {\n        margin-bottom: 20px;\n\n        .input-area {\n          ::v-deep .el-input__inner {\n            border-radius: 20px;\n            height: 40px;\n            line-height: 40px;\n          }\n\n          ::v-deep .el-input-group__append {\n            border-radius: 0 20px 20px 0;\n            border-left: none;\n\n            .el-button {\n              border-radius: 0 20px 20px 0;\n              height: 38px;\n              width: 40px;\n              padding: 0;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n            }\n          }\n        }\n      }\n\n      .history-section {\n        .history-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n\n          .history-search-title {\n            font-size: 14px;\n            color: #909399;\n            font-weight: 500;\n          }\n\n          .clear-btn {\n            color: #409EFF;\n            padding: 0;\n            font-size: 12px;\n\n            &:hover {\n              color: #66b1ff;\n            }\n          }\n        }\n\n        .history-tags {\n          margin-bottom: 20px;\n\n          .history-tag {\n            background-color: #e8f4ff;\n            border: 1px solid #b3d8ff;\n            color: #409EFF;\n            border-radius: 15px;\n            cursor: pointer;\n            margin-right: 8px;\n            margin-bottom: 8px;\n            padding: 6px 12px;\n            font-size: 13px;\n\n            &:hover {\n              background-color: #409EFF;\n              color: #fff;\n              border-color: #409EFF;\n            }\n\n            ::v-deep .el-tag__close {\n              color: #409EFF;\n\n              &:hover {\n                background-color: #fff;\n                color: #f56c6c;\n              }\n            }\n          }\n\n          .toggle-btn {\n            color: #909399;\n            padding: 0;\n            font-size: 12px;\n            margin-left: 8px;\n\n            &:hover {\n              color: #409EFF;\n            }\n          }\n        }\n\n        .common-questions-section {\n          .section-subtitle {\n            font-size: 14px;\n            color: #909399;\n            margin-bottom: 10px;\n            font-weight: 500;\n          }\n\n          .question-tags {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 8px;\n\n            .question-tag {\n              background-color: #f0f2f5;\n              border: 1px solid #dcdfe6;\n              border-radius: 15px;\n              color: #606266;\n              cursor: pointer;\n              padding: 6px 12px;\n              font-size: 13px;\n              transition: all 0.3s;\n\n              &:hover {\n                background-color: #409EFF;\n                color: #fff;\n                border-color: #409EFF;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  // 数据仪表板样式\n  .dashboard-section {\n    margin-top: 20px;\n\n    .data-cards-section {\n      display: grid;\n      grid-template-columns: 300px 1fr;\n      gap: 20px;\n      margin-bottom: 20px;\n\n      .data-card {\n        background-color: #fff;\n        border-radius: 8px;\n        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n        overflow: hidden;\n\n        &.wide-card {\n          grid-column: span 1;\n        }\n\n        .card-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 16px 20px;\n          border-bottom: 1px solid #f0f0f0;\n          background-color: #fafafa;\n\n          .card-title {\n            display: flex;\n            align-items: center;\n            font-size: 14px;\n            font-weight: 600;\n            color: #303133;\n\n            i {\n              margin-right: 8px;\n              color: #409EFF;\n            }\n          }\n\n          .card-actions {\n            display: flex;\n            gap: 4px;\n\n            .el-button {\n              padding: 4px 8px;\n              color: #909399;\n\n              &:hover {\n                color: #409EFF;\n              }\n            }\n          }\n        }\n\n        .card-content {\n          padding: 20px;\n\n          .main-value {\n            font-size: 32px;\n            font-weight: bold;\n            color: #303133;\n            margin-bottom: 8px;\n          }\n\n          .sub-info {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n\n            .date-info {\n              font-size: 12px;\n              color: #909399;\n            }\n\n            .trend-info {\n              font-size: 12px;\n              display: flex;\n              align-items: center;\n\n              &.positive {\n                color: #67C23A;\n              }\n\n              &.negative {\n                color: #F56C6C;\n              }\n\n              i {\n                margin-right: 2px;\n              }\n            }\n          }\n\n          .date-info {\n            font-size: 12px;\n            color: #909399;\n            margin-bottom: 16px;\n          }\n\n          .chart-container {\n            .chart {\n              width: 100%;\n            }\n          }\n        }\n      }\n    }\n\n    .insights-section {\n      .insights-card {\n        background-color: #fff;\n        border-radius: 8px;\n        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n        overflow: hidden;\n\n        .card-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 16px 20px;\n          border-bottom: 1px solid #f0f0f0;\n          background-color: #fafafa;\n\n          .card-title {\n            display: flex;\n            align-items: center;\n            font-size: 14px;\n            font-weight: 600;\n            color: #303133;\n\n            i {\n              margin-right: 8px;\n              color: #E6A23C;\n            }\n          }\n\n          .card-actions {\n            .el-button {\n              padding: 4px 12px;\n              font-size: 12px;\n              color: #409EFF;\n\n              &:hover {\n                background-color: #ecf5ff;\n              }\n            }\n          }\n        }\n\n        .card-content {\n          padding: 20px;\n\n          .insight-item {\n            display: flex;\n            align-items: flex-start;\n            margin-bottom: 16px;\n\n            &:last-child {\n              margin-bottom: 0;\n            }\n\n            .insight-icon {\n              width: 32px;\n              height: 32px;\n              border-radius: 50%;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              margin-right: 12px;\n              background-color: #FDF6EC;\n              color: #E6A23C;\n\n              &.success {\n                background-color: #F0F9FF;\n                color: #67C23A;\n              }\n\n              i {\n                font-size: 16px;\n              }\n            }\n\n            .insight-content {\n              flex: 1;\n\n              .insight-title {\n                font-size: 14px;\n                font-weight: 600;\n                color: #303133;\n                margin-bottom: 4px;\n              }\n\n              .insight-desc {\n                font-size: 12px;\n                color: #606266;\n                line-height: 1.5;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}