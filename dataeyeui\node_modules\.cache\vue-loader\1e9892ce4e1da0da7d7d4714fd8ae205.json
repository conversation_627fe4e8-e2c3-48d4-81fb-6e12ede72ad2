{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&scoped=true&lang=css", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\App.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKI2FwcCAudGhlbWUtcGlja2VyIHsKICBkaXNwbGF5OiBub25lOwp9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n    <theme-picker />\n  </div>\n</template>\n\n<script>\nimport ThemePicker from \"@/components/ThemePicker\";\n\nexport default {\n  name: \"App\",\n  components: { ThemePicker },\n  metaInfo() {\n    return {\n      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\n      titleTemplate: title => {\n        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\n      }\n    }\n  }\n};\n</script>\n<style scoped>\n#app .theme-picker {\n  display: none;\n}\n</style>\n"]}]}