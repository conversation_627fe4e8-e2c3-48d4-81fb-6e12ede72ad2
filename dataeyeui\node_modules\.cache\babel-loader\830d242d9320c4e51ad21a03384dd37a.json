{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\test-api.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\test-api.vue", "mtime": 1749638102000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "name", "data", "loading", "result", "error", "methods", "testConnection", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "n", "p", "axios", "get", "timeout", "headers", "v", "JSON", "stringify", "console", "log", "message", "code", "status", "statusText", "f", "a"], "sources": ["src/views/test-api.vue"], "sourcesContent": ["<template>\n  <div class=\"test-api\">\n    <h2>API连接测试</h2>\n    \n    <el-card class=\"test-card\">\n      <div slot=\"header\">\n        <span>测试后端连接</span>\n      </div>\n      \n      <el-button @click=\"testConnection\" type=\"primary\" :loading=\"loading\">\n        测试连接\n      </el-button>\n      \n      <div v-if=\"result\" class=\"result-area\">\n        <h4>测试结果：</h4>\n        <pre>{{ result }}</pre>\n      </div>\n      \n      <div v-if=\"error\" class=\"error-area\">\n        <h4>错误信息：</h4>\n        <pre>{{ error }}</pre>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios'\n\nexport default {\n  name: 'TestApi',\n  data() {\n    return {\n      loading: false,\n      result: null,\n      error: null\n    }\n  },\n  methods: {\n    async testConnection() {\n      this.loading = true\n      this.result = null\n      this.error = null\n      \n      try {\n        // 直接测试后端接口，不通过前端代理\n        const response = await axios.get('http://localhost:9099/pinboard/test', {\n          timeout: 5000,\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        })\n        \n        this.result = JSON.stringify(response.data, null, 2)\n        console.log('API测试成功:', response.data)\n        \n      } catch (error) {\n        this.error = {\n          message: error.message,\n          code: error.code,\n          response: error.response ? {\n            status: error.response.status,\n            statusText: error.response.statusText,\n            data: error.response.data\n          } : null\n        }\n        console.error('API测试失败:', error)\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-api {\n  padding: 20px;\n}\n\n.test-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.result-area, .error-area {\n  margin-top: 20px;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.result-area {\n  background-color: #f0f9ff;\n  border: 1px solid #67c23a;\n}\n\n.error-area {\n  background-color: #fef0f0;\n  border: 1px solid #f56c6c;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;AA2BA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAV,KAAA,CAAAL,OAAA;cACAK,KAAA,CAAAJ,MAAA;cACAI,KAAA,CAAAH,KAAA;cAAAY,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAIAE,cAAA,CAAAC,GAAA;gBACAC,OAAA;gBACAC,OAAA;kBACA;gBACA;cACA;YAAA;cALAT,QAAA,GAAAG,QAAA,CAAAO,CAAA;cAOAhB,KAAA,CAAAJ,MAAA,GAAAqB,IAAA,CAAAC,SAAA,CAAAZ,QAAA,CAAAZ,IAAA;cACAyB,OAAA,CAAAC,GAAA,aAAAd,QAAA,CAAAZ,IAAA;cAAAe,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAGAhB,KAAA,CAAAH,KAAA;gBACAwB,OAAA,EAAAd,EAAA,CAAAc,OAAA;gBACAC,IAAA,EAAAf,EAAA,CAAAe,IAAA;gBACAhB,QAAA,EAAAC,EAAA,CAAAD,QAAA;kBACAiB,MAAA,EAAAhB,EAAA,CAAAD,QAAA,CAAAiB,MAAA;kBACAC,UAAA,EAAAjB,EAAA,CAAAD,QAAA,CAAAkB,UAAA;kBACA9B,IAAA,EAAAa,EAAA,CAAAD,QAAA,CAAAZ;gBACA;cACA;cACAyB,OAAA,CAAAtB,KAAA,aAAAU,EAAA;YAAA;cAAAE,QAAA,CAAAE,CAAA;cAEAX,KAAA,CAAAL,OAAA;cAAA,OAAAc,QAAA,CAAAgB,CAAA;YAAA;cAAA,OAAAhB,QAAA,CAAAiB,CAAA;UAAA;QAAA,GAAArB,OAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}