{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\italic.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\italic.js", "mtime": 1749172158659}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9jcmVhdGVDbGFzczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L2pnc3QvZGF0YWV5ZXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY2FsbFN1cGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2FsbFN1cGVyLmpzIikpOwp2YXIgX2luaGVyaXRzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW5oZXJpdHMuanMiKSk7CnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIF9ib2xkID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2JvbGQuanMiKSk7CnZhciBJdGFsaWMgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKF9Cb2xkKSB7CiAgZnVuY3Rpb24gSXRhbGljKCkgewogICAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgSXRhbGljKTsKICAgIHJldHVybiAoMCwgX2NhbGxTdXBlcjIuZGVmYXVsdCkodGhpcywgSXRhbGljLCBhcmd1bWVudHMpOwogIH0KICAoMCwgX2luaGVyaXRzMi5kZWZhdWx0KShJdGFsaWMsIF9Cb2xkKTsKICByZXR1cm4gKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoSXRhbGljKTsKfShfYm9sZC5kZWZhdWx0KTsKKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoSXRhbGljLCAiYmxvdE5hbWUiLCAnaXRhbGljJyk7CigwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKEl0YWxpYywgInRhZ05hbWUiLCBbJ0VNJywgJ0knXSk7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IEl0YWxpYzs="}, {"version": 3, "names": ["_bold", "_interopRequireDefault", "require", "Italic", "_Bold", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "Bold", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/italic.ts"], "sourcesContent": ["import Bold from './bold.js';\n\nclass Italic extends Bold {\n  static blotName = 'italic';\n  static tagName = ['EM', 'I'];\n}\n\nexport default Italic;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA4B,IAEtBC,MAAM,0BAAAC,KAAA;EAAA,SAAAD,OAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,MAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,MAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,MAAA,EAAAC,KAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,MAAA;AAAA,EAASQ,aAAI;AAAA,IAAAC,gBAAA,CAAAN,OAAA,EAAnBH,MAAM,cACQ,QAAQ;AAAA,IAAAS,gBAAA,CAAAN,OAAA,EADtBH,MAAM,aAEO,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,IAAAU,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAGfH,MAAM", "ignoreList": []}]}