import Layout from '@/layout'
const datasearchRouter = {
  path: '/datasearch',
  component: Layout,
  name: 'DataSearch',
  meta: { title: '数据搜索', icon: 'search' },
  children: [
    {
      path: '',
      component: () => import('@/views/datasearch/home'),
      name: 'DataSearchHome',
      meta: { title: '数据搜索', icon: 'search', affix: false }
    },
    {
      path: 'index',
      component: () => import('@/views/datasearch/index'),
      name: 'DataSearchIndex',
      meta: { title: '数据搜索结果', icon: 'search', affix: false },
      hidden: true
    }
  ]
}
export default datasearchRouter