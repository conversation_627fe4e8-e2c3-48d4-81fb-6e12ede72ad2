import Layout from '@/layout'

const datasearchRouter = {
  path: '/datasearch',
  component: Layout,
  redirect: '/datasearch/home',
  name: 'DataSearch',
  meta: {
    title: '数据搜索',
    icon: 'search'
  },
  children: [
    {
      path: 'home',
      component: () => import('@/views/datasearch/home'),
      name: 'DataSearchHome',
      meta: {
        title: '数据搜索首页',
        icon: 'search',
        affix: false
      }
    },
    {
      path: 'index',
      component: () => import('@/views/datasearch/index'),
      name: 'DataSearchIndex',
      meta: {
        title: '搜索结果',
        icon: 'search',
        affix: false
      }
    }
  ]
}

export default datasearchRouter
