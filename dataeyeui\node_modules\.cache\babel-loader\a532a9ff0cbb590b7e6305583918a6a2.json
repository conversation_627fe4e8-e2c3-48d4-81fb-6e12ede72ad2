{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\table.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\table.js", "mtime": 1749172159203}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "_container", "TableCell", "exports", "_Block", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "cellOffset", "parent", "children", "indexOf", "format", "name", "blotName", "domNode", "setAttribute", "_superPropGet2", "row", "rowOffset", "table", "create", "node", "tableId", "formats", "hasAttribute", "getAttribute", "undefined", "Block", "_defineProperty2", "TableRow", "_Container", "checkMerge", "next", "head", "thisHead", "thisTail", "tail", "nextHead", "nextTail", "optimize", "context", "_this", "for<PERSON>ach", "child", "childFormats", "nextFormats", "splitAfter", "prev", "Container", "TableBody", "_Container2", "TableContainer", "_Container3", "balanceCells", "_this2", "rows", "descendants", "maxColumns", "reduce", "max", "Math", "length", "Array", "fill", "blot", "scroll", "append<PERSON><PERSON><PERSON>", "cells", "column", "map", "at", "deleteColumn", "index", "_this$descendant", "descendant", "_this$descendant2", "_slicedToArray2", "body", "cell", "remove", "insertColumn", "_this3", "_this$descendant3", "_this$descendant4", "ref", "insertBefore", "insertRow", "_this4", "_this$descendant5", "_this$descendant6", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "random", "toString", "slice", "concat"], "sources": ["../../src/formats/table.ts"], "sourcesContent": ["import type { LinkedList } from 'parchment';\nimport Block from '../blots/block.js';\nimport Container from '../blots/container.js';\n\nclass TableCell extends Block {\n  static blotName = 'table';\n  static tagName = 'TD';\n\n  static create(value: string) {\n    const node = super.create() as HTMLElement;\n    if (value) {\n      node.setAttribute('data-row', value);\n    } else {\n      node.setAttribute('data-row', tableId());\n    }\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    if (domNode.hasAttribute('data-row')) {\n      return domNode.getAttribute('data-row');\n    }\n    return undefined;\n  }\n\n  next: this | null;\n\n  cellOffset() {\n    if (this.parent) {\n      return this.parent.children.indexOf(this);\n    }\n    return -1;\n  }\n\n  format(name: string, value: string) {\n    if (name === TableCell.blotName && value) {\n      this.domNode.setAttribute('data-row', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  row(): TableRow {\n    return this.parent as TableRow;\n  }\n\n  rowOffset() {\n    if (this.row()) {\n      return this.row().rowOffset();\n    }\n    return -1;\n  }\n\n  table() {\n    return this.row() && this.row().table();\n  }\n}\n\nclass TableRow extends Container {\n  static blotName = 'table-row';\n  static tagName = 'TR';\n\n  children: LinkedList<TableCell>;\n  next: this | null;\n\n  checkMerge() {\n    // @ts-expect-error\n    if (super.checkMerge() && this.next.children.head != null) {\n      // @ts-expect-error\n      const thisHead = this.children.head.formats();\n      // @ts-expect-error\n      const thisTail = this.children.tail.formats();\n      // @ts-expect-error\n      const nextHead = this.next.children.head.formats();\n      // @ts-expect-error\n      const nextTail = this.next.children.tail.formats();\n      return (\n        thisHead.table === thisTail.table &&\n        thisHead.table === nextHead.table &&\n        thisHead.table === nextTail.table\n      );\n    }\n    return false;\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    this.children.forEach((child) => {\n      if (child.next == null) return;\n      const childFormats = child.formats();\n      const nextFormats = child.next.formats();\n      if (childFormats.table !== nextFormats.table) {\n        const next = this.splitAfter(child);\n        if (next) {\n          // @ts-expect-error TODO: parameters of optimize() should be a optional\n          next.optimize();\n        }\n        // We might be able to merge with prev now\n        if (this.prev) {\n          // @ts-expect-error TODO: parameters of optimize() should be a optional\n          this.prev.optimize();\n        }\n      }\n    });\n  }\n\n  rowOffset() {\n    if (this.parent) {\n      return this.parent.children.indexOf(this);\n    }\n    return -1;\n  }\n\n  table() {\n    return this.parent && this.parent.parent;\n  }\n}\n\nclass TableBody extends Container {\n  static blotName = 'table-body';\n  static tagName = 'TBODY';\n\n  children: LinkedList<TableRow>;\n}\n\nclass TableContainer extends Container {\n  static blotName = 'table-container';\n  static tagName = 'TABLE';\n\n  children: LinkedList<TableBody>;\n\n  balanceCells() {\n    const rows = this.descendants(TableRow);\n    const maxColumns = rows.reduce((max, row) => {\n      return Math.max(row.children.length, max);\n    }, 0);\n    rows.forEach((row) => {\n      new Array(maxColumns - row.children.length).fill(0).forEach(() => {\n        let value;\n        if (row.children.head != null) {\n          value = TableCell.formats(row.children.head.domNode);\n        }\n        const blot = this.scroll.create(TableCell.blotName, value);\n        row.appendChild(blot);\n        // @ts-expect-error TODO: parameters of optimize() should be a optional\n        blot.optimize(); // Add break blot\n      });\n    });\n  }\n\n  cells(column: number) {\n    return this.rows().map((row) => row.children.at(column));\n  }\n\n  deleteColumn(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    body.children.forEach((row) => {\n      const cell = row.children.at(index);\n      if (cell != null) {\n        cell.remove();\n      }\n    });\n  }\n\n  insertColumn(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    body.children.forEach((row) => {\n      const ref = row.children.at(index);\n      // @ts-expect-error\n      const value = TableCell.formats(row.children.head.domNode);\n      const cell = this.scroll.create(TableCell.blotName, value);\n      row.insertBefore(cell, ref);\n    });\n  }\n\n  insertRow(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    const id = tableId();\n    const row = this.scroll.create(TableRow.blotName) as TableRow;\n    body.children.head.children.forEach(() => {\n      const cell = this.scroll.create(TableCell.blotName, id);\n      row.appendChild(cell);\n    });\n    const ref = body.children.at(index);\n    body.insertBefore(row, ref);\n  }\n\n  rows() {\n    const body = this.children.head;\n    if (body == null) return [];\n    return body.children.map((row) => row);\n  }\n}\n\nTableContainer.allowedChildren = [TableBody];\nTableBody.requiredContainer = TableContainer;\n\nTableBody.allowedChildren = [TableRow];\nTableRow.requiredContainer = TableBody;\n\nTableRow.allowedChildren = [TableCell];\nTableCell.requiredContainer = TableRow;\n\nfunction tableId() {\n  const id = Math.random().toString(36).slice(2, 6);\n  return `row-${id}`;\n}\n\nexport { TableCell, TableRow, TableBody, TableContainer, tableId };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA6C,IAEvCE,SAAS,GAAAC,OAAA,CAAAD,SAAA,0BAAAE,MAAA;EAAA,SAAAF,UAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,SAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,SAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,SAAA,EAAAE,MAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,SAAA;IAAAS,GAAA;IAAAC,KAAA,EAuBb,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAI,IAAI,CAACC,MAAM,EAAE;QACf,OAAO,IAAI,CAACA,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC;MAC3C;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEA,SAAAK,MAAMA,CAACC,IAAY,EAAEN,KAAa,EAAE;MAClC,IAAIM,IAAI,KAAKhB,SAAS,CAACiB,QAAQ,IAAIP,KAAK,EAAE;QACxC,IAAI,CAACQ,OAAO,CAACC,YAAY,CAAC,UAAU,EAAET,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL,IAAAU,cAAA,CAAAhB,OAAA,EAAAJ,SAAA,sBAAagB,IAAI,EAAEN,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAW,GAAGA,CAAA,EAAa;MACd,OAAO,IAAI,CAACT,MAAM;IACpB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAY,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;QACd,OAAO,IAAI,CAACA,GAAG,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;MAC/B;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAb,GAAA;IAAAC,KAAA,EAEA,SAAAa,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACF,GAAG,CAAC,CAAC,IAAI,IAAI,CAACA,GAAG,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;IACzC;EAAA;IAAAd,GAAA;IAAAC,KAAA,EA/CA,SAAOc,MAAMA,CAACd,KAAa,EAAE;MAC3B,IAAMe,IAAI,OAAAL,cAAA,CAAAhB,OAAA,EAAAJ,SAAA,wBAAgC;MAC1C,IAAIU,KAAK,EAAE;QACTe,IAAI,CAACN,YAAY,CAAC,UAAU,EAAET,KAAK,CAAC;MACtC,CAAC,MAAM;QACLe,IAAI,CAACN,YAAY,CAAC,UAAU,EAAEO,OAAO,CAAC,CAAC,CAAC;MAC1C;MACA,OAAOD,IAAI;IACb;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEA,SAAOiB,OAAOA,CAACT,OAAoB,EAAE;MACnC,IAAIA,OAAO,CAACU,YAAY,CAAC,UAAU,CAAC,EAAE;QACpC,OAAOV,OAAO,CAACW,YAAY,CAAC,UAAU,CAAC;MACzC;MACA,OAAOC,SAAS;IAClB;EAAA;AAAA,EAnBsBC,cAAK;AAAA,IAAAC,gBAAA,CAAA5B,OAAA,EAAvBJ,SAAS,cACK,OAAO;AAAA,IAAAgC,gBAAA,CAAA5B,OAAA,EADrBJ,SAAS,aAEI,IAAI;AAAA,IAoDjBiC,QAAQ,GAAAhC,OAAA,CAAAgC,QAAA,0BAAAC,UAAA;EAAA,SAAAD,SAAA;IAAA,IAAA9B,gBAAA,CAAAC,OAAA,QAAA6B,QAAA;IAAA,WAAA5B,WAAA,CAAAD,OAAA,QAAA6B,QAAA,EAAA3B,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAA6B,QAAA,EAAAC,UAAA;EAAA,WAAA1B,aAAA,CAAAJ,OAAA,EAAA6B,QAAA;IAAAxB,GAAA;IAAAC,KAAA,EAOZ,SAAAyB,UAAUA,CAAA,EAAG;MACX;MACA,IAAI,IAAAf,cAAA,CAAAhB,OAAA,EAAA6B,QAAA,gCAAsB,IAAI,CAACG,IAAI,CAACvB,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;QACzD;QACA,IAAMC,QAAQ,GAAG,IAAI,CAACzB,QAAQ,CAACwB,IAAI,CAACV,OAAO,CAAC,CAAC;QAC7C;QACA,IAAMY,QAAQ,GAAG,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,CAACb,OAAO,CAAC,CAAC;QAC7C;QACA,IAAMc,QAAQ,GAAG,IAAI,CAACL,IAAI,CAACvB,QAAQ,CAACwB,IAAI,CAACV,OAAO,CAAC,CAAC;QAClD;QACA,IAAMe,QAAQ,GAAG,IAAI,CAACN,IAAI,CAACvB,QAAQ,CAAC2B,IAAI,CAACb,OAAO,CAAC,CAAC;QAClD,OACEW,QAAQ,CAACf,KAAK,KAAKgB,QAAQ,CAAChB,KAAK,IACjCe,QAAQ,CAACf,KAAK,KAAKkB,QAAQ,CAAClB,KAAK,IACjCe,QAAQ,CAACf,KAAK,KAAKmB,QAAQ,CAACnB,KAAK;MAErC;MACA,OAAO,KAAK;IACd;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAAiC,QAAQA,CAACC,OAA+B,EAAE;MAAA,IAAAC,KAAA;MACxC,IAAAzB,cAAA,CAAAhB,OAAA,EAAA6B,QAAA,wBAAeW,OAAO;MACtB,IAAI,CAAC/B,QAAQ,CAACiC,OAAO,CAAE,UAAAC,KAAK,EAAK;QAC/B,IAAIA,KAAK,CAACX,IAAI,IAAI,IAAI,EAAE;QACxB,IAAMY,YAAY,GAAGD,KAAK,CAACpB,OAAO,CAAC,CAAC;QACpC,IAAMsB,WAAW,GAAGF,KAAK,CAACX,IAAI,CAACT,OAAO,CAAC,CAAC;QACxC,IAAIqB,YAAY,CAACzB,KAAK,KAAK0B,WAAW,CAAC1B,KAAK,EAAE;UAC5C,IAAMa,IAAI,GAAGS,KAAI,CAACK,UAAU,CAACH,KAAK,CAAC;UACnC,IAAIX,IAAI,EAAE;YACR;YACAA,IAAI,CAACO,QAAQ,CAAC,CAAC;UACjB;UACA;UACA,IAAIE,KAAI,CAACM,IAAI,EAAE;YACb;YACAN,KAAI,CAACM,IAAI,CAACR,QAAQ,CAAC,CAAC;UACtB;QACF;MACF,CAAC,CAAC;IACJ;EAAA;IAAAlC,GAAA;IAAAC,KAAA,EAEA,SAAAY,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACV,MAAM,EAAE;QACf,OAAO,IAAI,CAACA,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC;MAC3C;MACA,OAAO,CAAC,CAAC;IACX;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEA,SAAAa,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACX,MAAM,IAAI,IAAI,CAACA,MAAM,CAACA,MAAM;IAC1C;EAAA;AAAA,EAzDqBwC,kBAAS;AAAA,IAAApB,gBAAA,CAAA5B,OAAA,EAA1B6B,QAAQ,cACM,WAAW;AAAA,IAAAD,gBAAA,CAAA5B,OAAA,EADzB6B,QAAQ,aAEK,IAAI;AAAA,IA0DjBoB,SAAS,GAAApD,OAAA,CAAAoD,SAAA,0BAAAC,WAAA;EAAA,SAAAD,UAAA;IAAA,IAAAlD,gBAAA,CAAAC,OAAA,QAAAiD,SAAA;IAAA,WAAAhD,WAAA,CAAAD,OAAA,QAAAiD,SAAA,EAAA/C,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAiD,SAAA,EAAAC,WAAA;EAAA,WAAA9C,aAAA,CAAAJ,OAAA,EAAAiD,SAAA;AAAA,EAASD,kBAAS;AAAA,IAAApB,gBAAA,CAAA5B,OAAA,EAA3BiD,SAAS,cACK,YAAY;AAAA,IAAArB,gBAAA,CAAA5B,OAAA,EAD1BiD,SAAS,aAEI,OAAO;AAAA,IAKpBE,cAAc,GAAAtD,OAAA,CAAAsD,cAAA,0BAAAC,WAAA;EAAA,SAAAD,eAAA;IAAA,IAAApD,gBAAA,CAAAC,OAAA,QAAAmD,cAAA;IAAA,WAAAlD,WAAA,CAAAD,OAAA,QAAAmD,cAAA,EAAAjD,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAmD,cAAA,EAAAC,WAAA;EAAA,WAAAhD,aAAA,CAAAJ,OAAA,EAAAmD,cAAA;IAAA9C,GAAA;IAAAC,KAAA,EAMlB,SAAA+C,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACb,IAAMC,IAAI,GAAG,IAAI,CAACC,WAAW,CAAC3B,QAAQ,CAAC;MACvC,IAAM4B,UAAU,GAAGF,IAAI,CAACG,MAAM,CAAC,UAACC,GAAG,EAAE1C,GAAG,EAAK;QAC3C,OAAO2C,IAAI,CAACD,GAAG,CAAC1C,GAAG,CAACR,QAAQ,CAACoD,MAAM,EAAEF,GAAG,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC;MACLJ,IAAI,CAACb,OAAO,CAAE,UAAAzB,GAAG,EAAK;QACpB,IAAI6C,KAAK,CAACL,UAAU,GAAGxC,GAAG,CAACR,QAAQ,CAACoD,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAACrB,OAAO,CAAC,YAAM;UAChE,IAAIpC,KAAK;UACT,IAAIW,GAAG,CAACR,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;YAC7B3B,KAAK,GAAGV,SAAS,CAAC2B,OAAO,CAACN,GAAG,CAACR,QAAQ,CAACwB,IAAI,CAACnB,OAAO,CAAC;UACtD;UACA,IAAMkD,IAAI,GAAGV,MAAI,CAACW,MAAM,CAAC7C,MAAM,CAACxB,SAAS,CAACiB,QAAQ,EAAEP,KAAK,CAAC;UAC1DW,GAAG,CAACiD,WAAW,CAACF,IAAI,CAAC;UACrB;UACAA,IAAI,CAACzB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAA;IAAAlC,GAAA;IAAAC,KAAA,EAEA,SAAA6D,KAAKA,CAACC,MAAc,EAAE;MACpB,OAAO,IAAI,CAACb,IAAI,CAAC,CAAC,CAACc,GAAG,CAAE,UAAApD,GAAG;QAAA,OAAKA,GAAG,CAACR,QAAQ,CAAC6D,EAAE,CAACF,MAAM,CAAC;MAAA,EAAC;IAC1D;EAAA;IAAA/D,GAAA;IAAAC,KAAA,EAEA,SAAAiE,YAAYA,CAACC,KAAa,EAAE;MAC1B;MACA,IAAAC,gBAAA,GAAe,IAAI,CAACC,UAAU,CAACzB,SAAS,CAAgB;QAAA0B,iBAAA,OAAAC,eAAA,CAAA5E,OAAA,EAAAyE,gBAAA;QAAjDI,IAAI,GAAAF,iBAAA;MACX,IAAIE,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACpE,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;MAChD4C,IAAI,CAACpE,QAAQ,CAACiC,OAAO,CAAE,UAAAzB,GAAG,EAAK;QAC7B,IAAM6D,IAAI,GAAG7D,GAAG,CAACR,QAAQ,CAAC6D,EAAE,CAACE,KAAK,CAAC;QACnC,IAAIM,IAAI,IAAI,IAAI,EAAE;UAChBA,IAAI,CAACC,MAAM,CAAC,CAAC;QACf;MACF,CAAC,CAAC;IACJ;EAAA;IAAA1E,GAAA;IAAAC,KAAA,EAEA,SAAA0E,YAAYA,CAACR,KAAa,EAAE;MAAA,IAAAS,MAAA;MAC1B;MACA,IAAAC,iBAAA,GAAe,IAAI,CAACR,UAAU,CAACzB,SAAS,CAAgB;QAAAkC,iBAAA,OAAAP,eAAA,CAAA5E,OAAA,EAAAkF,iBAAA;QAAjDL,IAAI,GAAAM,iBAAA;MACX,IAAIN,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACpE,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;MAChD4C,IAAI,CAACpE,QAAQ,CAACiC,OAAO,CAAE,UAAAzB,GAAG,EAAK;QAC7B,IAAMmE,GAAG,GAAGnE,GAAG,CAACR,QAAQ,CAAC6D,EAAE,CAACE,KAAK,CAAC;QAClC;QACA,IAAMlE,KAAK,GAAGV,SAAS,CAAC2B,OAAO,CAACN,GAAG,CAACR,QAAQ,CAACwB,IAAI,CAACnB,OAAO,CAAC;QAC1D,IAAMgE,IAAI,GAAGG,MAAI,CAAChB,MAAM,CAAC7C,MAAM,CAACxB,SAAS,CAACiB,QAAQ,EAAEP,KAAK,CAAC;QAC1DW,GAAG,CAACoE,YAAY,CAACP,IAAI,EAAEM,GAAG,CAAC;MAC7B,CAAC,CAAC;IACJ;EAAA;IAAA/E,GAAA;IAAAC,KAAA,EAEA,SAAAgF,SAASA,CAACd,KAAa,EAAE;MAAA,IAAAe,MAAA;MACvB;MACA,IAAAC,iBAAA,GAAe,IAAI,CAACd,UAAU,CAACzB,SAAS,CAAgB;QAAAwC,iBAAA,OAAAb,eAAA,CAAA5E,OAAA,EAAAwF,iBAAA;QAAjDX,IAAI,GAAAY,iBAAA;MACX,IAAIZ,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACpE,QAAQ,CAACwB,IAAI,IAAI,IAAI,EAAE;MAChD,IAAMyD,EAAE,GAAGpE,OAAO,CAAC,CAAC;MACpB,IAAML,GAAG,GAAG,IAAI,CAACgD,MAAM,CAAC7C,MAAM,CAACS,QAAQ,CAAChB,QAAQ,CAAa;MAC7DgE,IAAI,CAACpE,QAAQ,CAACwB,IAAI,CAACxB,QAAQ,CAACiC,OAAO,CAAC,YAAM;QACxC,IAAMoC,IAAI,GAAGS,MAAI,CAACtB,MAAM,CAAC7C,MAAM,CAACxB,SAAS,CAACiB,QAAQ,EAAE6E,EAAE,CAAC;QACvDzE,GAAG,CAACiD,WAAW,CAACY,IAAI,CAAC;MACvB,CAAC,CAAC;MACF,IAAMM,GAAG,GAAGP,IAAI,CAACpE,QAAQ,CAAC6D,EAAE,CAACE,KAAK,CAAC;MACnCK,IAAI,CAACQ,YAAY,CAACpE,GAAG,EAAEmE,GAAG,CAAC;IAC7B;EAAA;IAAA/E,GAAA;IAAAC,KAAA,EAEA,SAAAiD,IAAIA,CAAA,EAAG;MACL,IAAMsB,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACwB,IAAI;MAC/B,IAAI4C,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE;MAC3B,OAAOA,IAAI,CAACpE,QAAQ,CAAC4D,GAAG,CAAE,UAAApD,GAAG;QAAA,OAAKA,GAAG;MAAA,EAAC;IACxC;EAAA;AAAA,EAxE2B+B,kBAAS;AAAA,IAAApB,gBAAA,CAAA5B,OAAA,EAAhCmD,cAAc,cACA,iBAAiB;AAAA,IAAAvB,gBAAA,CAAA5B,OAAA,EAD/BmD,cAAc,aAED,OAAO;AAyE1BA,cAAc,CAACwC,eAAe,GAAG,CAAC1C,SAAS,CAAC;AAC5CA,SAAS,CAAC2C,iBAAiB,GAAGzC,cAAc;AAE5CF,SAAS,CAAC0C,eAAe,GAAG,CAAC9D,QAAQ,CAAC;AACtCA,QAAQ,CAAC+D,iBAAiB,GAAG3C,SAAS;AAEtCpB,QAAQ,CAAC8D,eAAe,GAAG,CAAC/F,SAAS,CAAC;AACtCA,SAAS,CAACgG,iBAAiB,GAAG/D,QAAQ;AAEtC,SAASP,OAAOA,CAAA,EAAG;EACjB,IAAMoE,EAAE,GAAG9B,IAAI,CAACiC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD,cAAAC,MAAA,CAAcN,EAAG;AACnB", "ignoreList": []}]}