#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import os

def execute_sql_file(cursor, file_path):
    """执行SQL文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 分割SQL语句
        statements = sql_content.split(';')

        for statement in statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    cursor.execute(statement)
                    print(f"✅ 执行成功: {statement[:50]}...")
                except Exception as e:
                    if "Duplicate entry" in str(e):
                        print(f"⚠️  数据已存在: {statement[:50]}...")
                    else:
                        print(f"❌ 执行失败: {statement[:50]}...")
                        print(f"   错误: {e}")
    except Exception as e:
        print(f"❌ 读取文件失败 {file_path}: {e}")

def main():
    # 数据库连接配置
    config = {
        'host': 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com',
        'user': 'dataeye',
        'password': 'jg@050877',
        'database': 'jgdataeye',
        'charset': 'utf8mb4'
    }

    # SQL文件列表
    sql_files = [
        '../account_projects.sql',
        '../pinboards.sql',
        '../pinboard_tags.sql',
        '../pinboard_tag_relations.sql',
        '../pinboard_shares.sql',
        '../pinboard_stars.sql'
    ]

    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()

        print("🔗 数据库连接成功")

        # 执行每个SQL文件
        for sql_file in sql_files:
            if os.path.exists(sql_file):
                print(f"\n📄 执行文件: {sql_file}")
                execute_sql_file(cursor, sql_file)
            else:
                print(f"❌ 文件不存在: {sql_file}")

        # 提交事务
        connection.commit()
        print("\n✅ 所有数据插入完成")

        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM account_projects")
        project_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM pinboards")
        pinboard_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM pinboard_tags")
        tag_count = cursor.fetchone()[0]

        print(f"\n📊 数据统计:")
        print(f"   项目数量: {project_count}")
        print(f"   Pinboard数量: {pinboard_count}")
        print(f"   标签数量: {tag_count}")

    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
