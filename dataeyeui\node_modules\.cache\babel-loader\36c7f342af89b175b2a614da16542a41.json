{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\direction.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\direction.js", "mtime": 1749172158038}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLkRpcmVjdGlvblN0eWxlID0gZXhwb3J0cy5EaXJlY3Rpb25DbGFzcyA9IGV4cG9ydHMuRGlyZWN0aW9uQXR0cmlidXRlID0gdm9pZCAwOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgY29uZmlnID0gewogIHNjb3BlOiBfcGFyY2htZW50LlNjb3BlLkJMT0NLLAogIHdoaXRlbGlzdDogWydydGwnXQp9Owp2YXIgRGlyZWN0aW9uQXR0cmlidXRlID0gZXhwb3J0cy5EaXJlY3Rpb25BdHRyaWJ1dGUgPSBuZXcgX3BhcmNobWVudC5BdHRyaWJ1dG9yKCdkaXJlY3Rpb24nLCAnZGlyJywgY29uZmlnKTsKdmFyIERpcmVjdGlvbkNsYXNzID0gZXhwb3J0cy5EaXJlY3Rpb25DbGFzcyA9IG5ldyBfcGFyY2htZW50LkNsYXNzQXR0cmlidXRvcignZGlyZWN0aW9uJywgJ3FsLWRpcmVjdGlvbicsIGNvbmZpZyk7CnZhciBEaXJlY3Rpb25TdHlsZSA9IGV4cG9ydHMuRGlyZWN0aW9uU3R5bGUgPSBuZXcgX3BhcmNobWVudC5TdHlsZUF0dHJpYnV0b3IoJ2RpcmVjdGlvbicsICdkaXJlY3Rpb24nLCBjb25maWcpOw=="}, {"version": 3, "names": ["_parchment", "require", "config", "scope", "<PERSON><PERSON>", "BLOCK", "whitelist", "DirectionAttribute", "exports", "Attributor", "DirectionClass", "ClassAttributor", "DirectionStyle", "StyleAttributor"], "sources": ["../../src/formats/direction.ts"], "sourcesContent": ["import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['rtl'],\n};\n\nconst DirectionAttribute = new Attributor('direction', 'dir', config);\nconst DirectionClass = new ClassAttributor('direction', 'ql-direction', config);\nconst DirectionStyle = new StyleAttributor('direction', 'direction', config);\n\nexport { DirectionAttribute, DirectionClass, DirectionStyle };\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAMC,MAAM,GAAG;EACbC,KAAK,EAAEC,gBAAK,CAACC,KAAK;EAClBC,SAAS,EAAE,CAAC,KAAK;AACnB,CAAC;AAED,IAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,IAAIE,qBAAU,CAAC,WAAW,EAAE,KAAK,EAAEP,MAAM,CAAC;AACrE,IAAMQ,cAAc,GAAAF,OAAA,CAAAE,cAAA,GAAG,IAAIC,0BAAe,CAAC,WAAW,EAAE,cAAc,EAAET,MAAM,CAAC;AAC/E,IAAMU,cAAc,GAAAJ,OAAA,CAAAI,cAAA,GAAG,IAAIC,0BAAe,CAAC,WAAW,EAAE,WAAW,EAAEX,MAAM,CAAC", "ignoreList": []}]}