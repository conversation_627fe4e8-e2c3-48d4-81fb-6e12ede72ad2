from fastapi import APIRouter, Query, Depends, Body, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from config.get_db import get_async_db
from cards_model import Card
import uuid
import datetime

router = APIRouter()

@router.get("/api/cards")
async def get_cards(
    pageNum: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db)
):
    offset = (pageNum - 1) * pageSize
    stmt = select(Card).offset(offset).limit(pageSize)
    total_stmt = select(func.count(Card.uid))
    result = await db.execute(stmt)
    items = result.scalars().all()
    total_result = await db.execute(total_stmt)
    total = total_result.scalar()
    # 简单序列化
    def serialize(card):
        return {
            'uid': card.uid,
            'pinboard_uid': card.pinboard_uid,
            'title': card.title,
            'card_type': card.card_type,
            'sentence_type': card.sentence_type,
            'result_type': card.result_type,
            'y_axis_scale': card.y_axis_scale,
            'datetime_anchor': card.datetime_anchor,
            'position_x': card.position_x,
            'position_y': card.position_y,
            'width': card.width,
            'height': card.height,
            'created_at': card.created_at,
            'updated_at': card.updated_at,
            'create_by': card.create_by,
            'update_by': card.update_by,
        }
    return {
        'items': [serialize(card) for card in items],
        'total': total,
        'pageNum': pageNum,
        'pageSize': pageSize
    }

@router.post("/api/cards")
async def add_card(
    card_data: dict = Body(...),
    db: AsyncSession = Depends(get_async_db)
):
    # 校验必填字段
    required_fields = ["title", "pinboard_uid", "card_type"]
    for field in required_fields:
        if field not in card_data or not card_data[field]:
            raise HTTPException(status_code=400, detail=f"Missing required field: {field}")
    # 生成唯一uid
    card_uid = str(uuid.uuid4())
    now = datetime.datetime.utcnow()
    card = Card(
        uid=card_uid,
        pinboard_uid=card_data["pinboard_uid"],
        title=card_data["title"],
        card_type=card_data["card_type"],
        sentence_type=card_data.get("sentence_type", 0),
        result_type=card_data.get("result_type", 0),
        y_axis_scale=card_data.get("y_axis_scale", 1),
        datetime_anchor=card_data.get("datetime_anchor"),
        position_x=card_data.get("position_x", 0),
        position_y=card_data.get("position_y", 0),
        width=card_data.get("width", 4),
        height=card_data.get("height", 3),
        created_at=now,
        updated_at=now,
        create_by=card_data.get("create_by"),
        update_by=card_data.get("update_by"),
    )
    db.add(card)
    try:
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    return {"message": "卡片创建成功", "uid": card_uid}
