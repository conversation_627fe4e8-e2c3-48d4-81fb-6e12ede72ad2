<template>
  <div class="app-container">
    <div class="page-header">
      <h2 class="page-title">已关注报告</h2>
      <p class="page-description">查看您关注的报告和更新动态</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button icon="el-icon-refresh" @click="refreshList">刷新</el-button>
        <el-button icon="el-icon-bell" @click="manageNotifications">通知设置</el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索关注的报告..."
          prefix-icon="el-icon-search"
          style="width: 300px;"
          clearable
        />
        <el-select v-model="filterCategory" placeholder="分类筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="数据分析" value="analysis" />
          <el-option label="财务报告" value="finance" />
          <el-option label="市场研究" value="market" />
          <el-option label="用户研究" value="user" />
        </el-select>
      </div>
    </div>

    <!-- 关注报告列表 -->
    <div class="followed-list" v-loading="loading">
      <el-row :gutter="20">
        <el-col
          v-for="report in filteredReports"
          :key="report.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          class="report-item-col"
        >
          <div class="report-card" @click="openReport(report)">
            <div class="card-header">
              <div class="report-icon">
                <i class="el-icon-star-on"></i>
              </div>
              <div class="follow-actions">
                <el-tooltip content="取消关注" placement="top">
                  <el-button
                    type="text"
                    icon="el-icon-star-off"
                    @click.stop="unfollowReport(report)"
                    class="unfollow-btn"
                  />
                </el-tooltip>
              </div>
            </div>

            <div class="card-content">
              <h3 class="report-title">{{ report.title }}</h3>
              <p class="report-description">{{ report.description }}</p>

              <div class="author-info">
                <el-avatar :size="24" :src="report.author.avatar">
                  {{ report.author.name.charAt(0) }}
                </el-avatar>
                <span class="author-name">{{ report.author.name }}</span>
              </div>

              <div class="update-info">
                <div class="last-update">
                  <i class="el-icon-time"></i>
                  <span>{{ report.lastUpdate }}</span>
                </div>
                <div v-if="report.hasNewUpdate" class="new-update-badge">
                  <el-badge is-dot class="update-dot">
                    <span class="update-text">有更新</span>
                  </el-badge>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <div class="report-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ report.viewCount }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-chat-dot-round"></i>
                  {{ report.commentCount }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-star-on"></i>
                  {{ report.followCount }}
                </span>
              </div>

              <div class="quick-actions">
                <el-button type="text" size="small" @click.stop="shareReport(report)">
                  <i class="el-icon-share"></i>
                </el-button>
                <el-button type="text" size="small" @click.stop="downloadReport(report)">
                  <i class="el-icon-download"></i>
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredReports.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="el-icon-star-off"></i>
      </div>
      <h3>暂无关注的报告</h3>
      <p>您还没有关注任何报告，去<a href="#" @click="exploreReports">探索</a>一些有趣的报告吧！</p>
    </div>

    <!-- 通知设置对话框 -->
    <el-dialog title="通知设置" :visible.sync="notificationDialogVisible" width="500px">
      <el-form :model="notificationForm" label-width="120px">
        <el-form-item label="邮件通知">
          <el-switch v-model="notificationForm.emailEnabled" />
          <div class="form-help">当关注的报告有更新时发送邮件通知</div>
        </el-form-item>

        <el-form-item label="站内通知">
          <el-switch v-model="notificationForm.inAppEnabled" />
          <div class="form-help">在系统内显示通知消息</div>
        </el-form-item>

        <el-form-item label="通知频率">
          <el-select v-model="notificationForm.frequency" style="width: 100%;">
            <el-option label="立即通知" value="immediate" />
            <el-option label="每日汇总" value="daily" />
            <el-option label="每周汇总" value="weekly" />
          </el-select>
        </el-form-item>

        <el-form-item label="通知类型">
          <el-checkbox-group v-model="notificationForm.types">
            <el-checkbox label="content_update">内容更新</el-checkbox>
            <el-checkbox label="new_comment">新评论</el-checkbox>
            <el-checkbox label="author_post">作者新发布</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="notificationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFollowedReports } from '@/api/pinboard'

export default {
  name: 'ReportFollowed',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      filterCategory: '',
      notificationDialogVisible: false,
      notificationForm: {
        emailEnabled: true,
        inAppEnabled: true,
        frequency: 'immediate',
        types: ['content_update', 'new_comment']
      },
      reports: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0
      }
    }
  },
  mounted() {
    this.loadReports()
  },
  computed: {
    filteredReports() {
      let filtered = this.reports

      // 分类筛选
      if (this.filterCategory) {
        filtered = filtered.filter(report => report.category === this.filterCategory)
      }

      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(report =>
          report.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          report.description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          report.author.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }

      return filtered
    }
  },
  methods: {
    // 加载关注报告列表
    async loadReports() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          search: this.searchKeyword,
          category: this.filterCategory
        }

        const response = await getFollowedReports(params)
        if (response.code === 200) {
          const data = response.data
          this.reports = data.items.map(item => ({
            id: item.uid,
            title: item.title,
            description: item.description,
            category: item.category,
            author: {
              id: 1,
              name: item.author,
              avatar: item.avatar
            },
            lastUpdate: item.last_update,
            hasNewUpdate: item.has_update,
            viewCount: Math.floor(Math.random() * 1000) + 100,
            commentCount: Math.floor(Math.random() * 50) + 5,
            followCount: Math.floor(Math.random() * 100) + 10,
            followTime: item.follow_time
          }))
          this.pagination.total = data.total
        }
      } catch (error) {
        console.error('加载关注报告失败:', error)
        this.$message.error('加载关注报告失败')
      } finally {
        this.loading = false
      }
    },

    refreshList() {
      this.loadReports()
      this.$message.success('列表已刷新')
    },
    manageNotifications() {
      this.notificationDialogVisible = true
    },
    openReport(report) {
      // 标记为已读
      if (report.hasNewUpdate) {
        report.hasNewUpdate = false
      }
      this.$message.info(`打开报告: ${report.title}`)
    },
    unfollowReport(report) {
      this.$confirm(`确定要取消关注"${report.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reports.findIndex(r => r.id === report.id)
        if (index > -1) {
          this.reports.splice(index, 1)
          this.$message.success('已取消关注')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    shareReport(report) {
      this.$message.info(`分享报告: ${report.title}`)
    },
    downloadReport(report) {
      this.$message.info(`下载报告: ${report.title}`)
    },
    exploreReports() {
      this.$message.info('跳转到报告探索页面')
    },
    saveNotificationSettings() {
      // 这里应该调用API保存设置
      this.$message.success('通知设置已保存')
      this.notificationDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;

  .page-title {
    font-size: 24px;
    color: #303133;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #909399;
    margin: 0;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
  }
}

.followed-list {
  .report-item-col {
    margin-bottom: 20px;
  }

  .report-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    height: 300px;
    display: flex;
    flex-direction: column;
    position: relative;

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 16px 0 16px;

      .report-icon {
        width: 40px;
        height: 40px;
        background: #F56C6C;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 18px;
      }

      .follow-actions {
        .unfollow-btn {
          color: #909399;
          padding: 4px;

          &:hover {
            color: #f56c6c;
          }
        }
      }
    }

    .card-content {
      padding: 16px;
      flex: 1;

      .report-title {
        font-size: 16px;
        color: #303133;
        margin: 0 0 8px 0;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .report-description {
        color: #606266;
        font-size: 14px;
        margin: 0 0 16px 0;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .author-info {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .author-name {
          margin-left: 8px;
          font-size: 14px;
          color: #606266;
        }
      }

      .update-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-update {
          font-size: 12px;
          color: #909399;
          display: flex;
          align-items: center;

          i {
            margin-right: 4px;
          }
        }

        .new-update-badge {
          .update-dot {
            ::v-deep .el-badge__content {
              background-color: #f56c6c;
            }
          }

          .update-text {
            font-size: 12px;
            color: #f56c6c;
            margin-left: 4px;
          }
        }
      }
    }

    .card-footer {
      padding: 12px 16px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .report-stats {
        display: flex;
        gap: 16px;

        .stat-item {
          font-size: 12px;
          color: #909399;
          display: flex;
          align-items: center;

          i {
            margin-right: 4px;
          }
        }
      }

      .quick-actions {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 4px;
          color: #909399;

          &:hover {
            color: #409EFF;
          }
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .empty-icon {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    color: #303133;
    margin: 0 0 8px 0;
  }

  p {
    color: #909399;
    margin: 0;

    a {
      color: #409EFF;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
