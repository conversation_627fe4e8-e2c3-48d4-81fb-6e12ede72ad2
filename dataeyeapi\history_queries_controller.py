from fastapi import APIRouter, Query, Depends, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from config.get_db import get_db
from history_queries_model import HistoryQuery
import datetime

router = APIRouter()

@router.get("/api/history_queries")
async def get_history_queries(pageNum: int = Query(1), pageSize: int = Query(10), db: AsyncSession = Depends(get_db)):
    offset = (pageNum - 1) * pageSize
    stmt = select(HistoryQuery).order_by(HistoryQuery.created_at.desc()).offset(offset).limit(pageSize)
    result = await db.execute(stmt)
    items = result.scalars().all()
    total_result = await db.execute(select(HistoryQuery))
    total = len(total_result.scalars().all())
    return {
        "items": [
            {"id": i.id, "question": i.tokens or i.identity or ""} for i in items
        ],
        "total": total,
        "pageNum": pageNum,
        "pageSize": pageSize
    }

@router.post("/api/history_queries")
async def add_history_query(
    project_id: int = Body(...),
    user_id: int = Body(...),
    identity: str = Body(...),
    tokens: str = Body(None),
    db: AsyncSession = Depends(get_db)
):
    new_query = HistoryQuery(
        project_id=project_id,
        user_id=user_id,
        identity=identity,
        tokens=tokens,
        created_at=datetime.datetime.utcnow()
    )
    db.add(new_query)
    await db.commit()
    await db.refresh(new_query)
    return {"success": True, "id": new_query.id}
