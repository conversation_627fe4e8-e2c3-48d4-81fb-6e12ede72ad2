from fastapi import APIRouter, Query, Depends, Body, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from config.get_db import get_db
from history_queries_model import HistoryQuery
import datetime

router = APIRouter()

@router.get("/api/history_queries")
async def get_history_queries(pageNum: int = Query(1), pageSize: int = Query(10), db: AsyncSession = Depends(get_db)):
    try:
        offset = (pageNum - 1) * pageSize
        stmt = select(HistoryQuery).order_by(HistoryQuery.created_at.desc()).offset(offset).limit(pageSize)
        result = await db.execute(stmt)
        items = result.scalars().all()

        # 获取总数的优化查询
        from sqlalchemy import func
        total_stmt = select(func.count(HistoryQuery.id))
        total_result = await db.execute(total_stmt)
        total = total_result.scalar()

        def serialize(item):
            def format_datetime(dt):
                if not dt:
                    return ''
                if isinstance(dt, str):
                    return dt.replace('T', ' ').split('.')[0]
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            return {
                "id": item.id,
                "content": item.tokens or item.identity or "",
                "time": format_datetime(item.created_at)
            }

        return {
            "items": [serialize(i) for i in items],
            "total": total,
            "pageNum": pageNum,
            "pageSize": pageSize
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史查询失败: {str(e)}")

@router.post("/api/history_queries")
async def add_history_query(
    project_id: int = Body(...),
    user_id: int = Body(...),
    identity: str = Body(...),
    tokens: str = Body(None),
    db: AsyncSession = Depends(get_db)
):
    try:
        new_query = HistoryQuery(
            project_id=project_id,
            user_id=user_id,
            identity=identity,
            tokens=tokens,
            created_at=datetime.datetime.utcnow()
        )
        db.add(new_query)
        await db.commit()
        await db.refresh(new_query)
        return {"success": True, "id": new_query.id}
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"添加历史查询失败: {str(e)}")

@router.delete("/api/history_queries/{id}")
async def delete_history_query(id: int, db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(select(HistoryQuery).where(HistoryQuery.id == id))
        obj = result.scalar_one_or_none()
        if not obj:
            raise HTTPException(status_code=404, detail="历史查询不存在")
        await db.delete(obj)
        await db.commit()
        return {"success": True}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除历史查询失败: {str(e)}")
