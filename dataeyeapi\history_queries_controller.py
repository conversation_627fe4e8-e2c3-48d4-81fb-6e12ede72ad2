from fastapi import APIRouter, Query, Depends, Body, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from config.get_db import get_db
from history_queries_model import HistoryQuery
import datetime

router = APIRouter()

@router.get("/api/history_queries")
async def get_history_queries(pageNum: int = Query(1), pageSize: int = Query(10), db: AsyncSession = Depends(get_db)):
    offset = (pageNum - 1) * pageSize
    stmt = select(HistoryQuery).order_by(HistoryQuery.created_at.desc()).offset(offset).limit(pageSize)
    result = await db.execute(stmt)
    items = result.scalars().all()
    total_result = await db.execute(select(HistoryQuery))
    total = len(total_result.scalars().all())
    def serialize(item):
        def format_datetime(dt):
            if not dt:
                return ''
            if isinstance(dt, str):
                return dt.replace('T', ' ').split('.')[0]
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        return {
            "id": item.id,
            "content": item.tokens or item.identity or "",
            "time": format_datetime(item.created_at)
        }
    return {
        "items": [serialize(i) for i in items],
        "total": total,
        "pageNum": pageNum,
        "pageSize": pageSize
    }

@router.post("/api/history_queries")
async def add_history_query(
    project_id: int = Body(...),
    user_id: int = Body(...),
    identity: str = Body(...),
    tokens: str = Body(None),
    db: AsyncSession = Depends(get_db)
):
    new_query = HistoryQuery(
        project_id=project_id,
        user_id=user_id,
        identity=identity,
        tokens=tokens,
        created_at=datetime.datetime.utcnow()
    )
    db.add(new_query)
    await db.commit()
    await db.refresh(new_query)
    return {"success": True, "id": new_query.id}

@router.delete("/api/history_queries/{id}")
async def delete_history_query(id: int, db: AsyncSession = Depends(get_db)):
    result = await db.execute(select(HistoryQuery).where(HistoryQuery.id == id))
    obj = result.scalar_one_or_none()
    if not obj:
        raise HTTPException(status_code=404, detail="Not Found")
    await db.delete(obj)
    await db.commit()
    return {"success": True}
