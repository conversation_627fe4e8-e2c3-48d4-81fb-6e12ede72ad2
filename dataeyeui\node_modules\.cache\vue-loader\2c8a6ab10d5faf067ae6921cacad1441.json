{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\App.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUaGVtZVBpY2tlciBmcm9tICJAL2NvbXBvbmVudHMvVGhlbWVQaWNrZXIiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJBcHAiLAogIGNvbXBvbmVudHM6IHsgVGhlbWVQaWNrZXIgfSwKICBtZXRhSW5mbygpIHsKICAgIHJldHVybiB7CiAgICAgIHRpdGxlOiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5keW5hbWljVGl0bGUgJiYgdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudGl0bGUsCiAgICAgIHRpdGxlVGVtcGxhdGU6IHRpdGxlID0+IHsKICAgICAgICByZXR1cm4gdGl0bGUgPyBgJHt0aXRsZX0gLSAke3Byb2Nlc3MuZW52LlZVRV9BUFBfVElUTEV9YCA6IHByb2Nlc3MuZW52LlZVRV9BUFBfVElUTEUKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;;AAQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n    <theme-picker />\n  </div>\n</template>\n\n<script>\nimport ThemePicker from \"@/components/ThemePicker\";\n\nexport default {\n  name: \"App\",\n  components: { ThemePicker },\n  metaInfo() {\n    return {\n      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\n      titleTemplate: title => {\n        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\n      }\n    }\n  }\n};\n</script>\n<style scoped>\n#app .theme-picker {\n  display: none;\n}\n</style>\n"]}]}