{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\api\\monitor\\jobLog.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\api\\monitor\\jobLog.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuY2xlYW5Kb2JMb2cgPSBjbGVhbkpvYkxvZzsKZXhwb3J0cy5kZWxKb2JMb2cgPSBkZWxKb2JMb2c7CmV4cG9ydHMubGlzdEpvYkxvZyA9IGxpc3RKb2JMb2c7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LosIPluqbml6Xlv5fliJfooagKZnVuY3Rpb24gbGlzdEpvYkxvZyhxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21vbml0b3Ivam9iTG9nL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5Yig6Zmk6LCD5bqm5pel5b+XCmZ1bmN0aW9uIGRlbEpvYkxvZyhqb2JMb2dJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21vbml0b3Ivam9iTG9nLycgKyBqb2JMb2dJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5riF56m66LCD5bqm5pel5b+XCmZ1bmN0aW9uIGNsZWFuSm9iTG9nKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21vbml0b3Ivam9iTG9nL2NsZWFuJywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listJobLog", "query", "request", "url", "method", "params", "delJobLog", "jobLogId", "cleanJobLog"], "sources": ["D:/jgst/dataeyeui/src/api/monitor/jobLog.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询调度日志列表\nexport function listJobLog(query) {\n  return request({\n    url: '/monitor/jobLog/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 删除调度日志\nexport function delJobLog(jobLogId) {\n  return request({\n    url: '/monitor/jobLog/' + jobLogId,\n    method: 'delete'\n  })\n}\n\n// 清空调度日志\nexport function cleanJobLog() {\n  return request({\n    url: '/monitor/jobLog/clean',\n    method: 'delete'\n  })\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,QAAQ;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAAA,EAAG;EAC5B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}