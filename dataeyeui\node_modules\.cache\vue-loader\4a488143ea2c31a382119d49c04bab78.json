{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\report\\collaborate\\index.vue?vue&type=template&id=28568364&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\report\\collaborate\\index.vue", "mtime": 1750000678000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749172157834}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}