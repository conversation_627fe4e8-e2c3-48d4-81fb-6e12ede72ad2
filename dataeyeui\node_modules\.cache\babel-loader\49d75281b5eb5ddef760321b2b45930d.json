{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\monitor\\job\\log.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\monitor\\job\\log.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_job", "require", "_jobLog", "name", "dicts", "data", "loading", "ids", "multiple", "showSearch", "total", "jobLogList", "open", "date<PERSON><PERSON><PERSON>", "form", "queryParams", "pageNum", "pageSize", "job<PERSON>ame", "undefined", "jobGroup", "status", "created", "_this", "jobId", "$route", "params", "get<PERSON>ob", "then", "response", "getList", "methods", "_this2", "listJobLog", "addDateRange", "rows", "jobGroupFormat", "row", "column", "selectDictLabel", "dict", "type", "sys_job_group", "jobExecutorFormat", "sys_job_executor", "jobExecutor", "handleClose", "obj", "path", "$tab", "closeOpenPage", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "jobLogId", "length", "handleView", "handleDelete", "_this3", "jobLogIds", "$modal", "confirm", "delJobLog", "msgSuccess", "catch", "handleClean", "_this4", "cleanJobLog", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/monitor/job/log.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"任务名称\" prop=\"jobName\">\n        <el-input\n          v-model=\"queryParams.jobName\"\n          placeholder=\"请输入任务名称\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务组名\" prop=\"jobGroup\">\n        <el-select\n          v-model=\"queryParams.jobGroup\"\n          placeholder=\"请选择任务组名\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_job_group\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"执行状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"请选择执行状态\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_common_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"执行时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['monitor:job:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          @click=\"handleClean\"\n          v-hasPermi=\"['monitor:job:remove']\"\n        >清空</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['monitor:job:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-close\"\n          size=\"mini\"\n          @click=\"handleClose\"\n        >关闭</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"jobLogList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"日志编号\" width=\"80\" align=\"center\" prop=\"jobLogId\" />\n      <el-table-column label=\"任务名称\" align=\"center\" prop=\"jobName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"任务组名\" align=\"center\" prop=\"jobGroup\" :show-overflow-tooltip=\"true\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_job_group\" :value=\"scope.row.jobGroup\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"调用目标字符串\" align=\"center\" prop=\"invokeTarget\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"日志信息\" align=\"center\" prop=\"jobMessage\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"执行状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"执行时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['monitor:job:query']\"\n          >详细</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 调度日志详细 -->\n    <el-dialog title=\"调度日志详细\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"日志序号：\">{{ form.jobLogId }}</el-form-item>\n            <el-form-item label=\"任务名称：\">{{ form.jobName }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务分组：\">{{ jobGroupFormat(form) }}</el-form-item>\n            <el-form-item label=\"执行时间：\">{{ parseTime(form.createTime) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务执行器：\">{{ jobExecutorFormat(form) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务触发器：\">{{ form.jobTrigger }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"调用方法：\">{{ form.invokeTarget }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"位置参数：\">{{ form.jobArgs }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"关键字参数：\">{{ form.jobKwargs }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"日志信息：\">{{ form.jobMessage }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"执行状态：\">\n              <div v-if=\"form.status == 0\">正常</div>\n              <div v-else-if=\"form.status == 1\">失败</div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"异常信息：\" v-if=\"form.status == 1\">{{ form.exceptionInfo }}</el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"open = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getJob} from \"@/api/monitor/job\";\nimport { listJobLog, delJobLog, cleanJobLog } from \"@/api/monitor/jobLog\";\n\nexport default {\n  name: \"JobLog\",\n  dicts: ['sys_common_status', 'sys_job_group', 'sys_job_executor'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 调度日志表格数据\n      jobLogList: [],\n      // 是否显示弹出层\n      open: false,\n      // 日期范围\n      dateRange: [],\n      // 表单参数\n      form: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        jobName: undefined,\n        jobGroup: undefined,\n        status: undefined\n      }\n    };\n  },\n  created() {\n    const jobId = this.$route.params && this.$route.params.jobId;\n    if (jobId !== undefined && jobId != 0) {\n      getJob(jobId).then(response => {\n        this.queryParams.jobName = response.data.jobName;\n        this.queryParams.jobGroup = response.data.jobGroup;\n        this.getList();\n      });\n    } else {\n      this.getList();\n    }\n  },\n  methods: {\n    /** 查询调度日志列表 */\n    getList() {\n      this.loading = true;\n      listJobLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.jobLogList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 任务组名字典翻译\n    jobGroupFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);\n    },\n    // 任务执行器名字典翻译\n    jobExecutorFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_executor, row.jobExecutor);\n    },\n    // 返回按钮\n    handleClose() {\n      const obj = { path: \"/monitor/job\" };\n      this.$tab.closeOpenPage(obj);\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobLogId);\n      this.multiple = !selection.length;\n    },\n    /** 详细按钮操作 */\n    handleView(row) {\n      this.open = true;\n      this.form = row;\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const jobLogIds = this.ids;\n      this.$modal.confirm('是否确认删除调度日志编号为\"' + jobLogIds + '\"的数据项？').then(function() {\n        return delJobLog(jobLogIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 清空按钮操作 */\n    handleClean() {\n      this.$modal.confirm('是否确认清空所有调度日志数据项？').then(function() {\n        return cleanJobLog();\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"清空成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('/monitor/jobLog/export', {\n        ...this.queryParams\n      }, `log_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AAkMA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,KAAA;IACA,IAAAA,KAAA,KAAAL,SAAA,IAAAK,KAAA;MACA,IAAAG,WAAA,EAAAH,KAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAR,WAAA,CAAAG,OAAA,GAAAW,QAAA,CAAAxB,IAAA,CAAAa,OAAA;QACAK,KAAA,CAAAR,WAAA,CAAAK,QAAA,GAAAS,QAAA,CAAAxB,IAAA,CAAAe,QAAA;QACAG,KAAA,CAAAO,OAAA;MACA;IACA;MACA,KAAAA,OAAA;IACA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,kBAAA,OAAAC,YAAA,MAAAnB,WAAA,OAAAF,SAAA,GAAAe,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAArB,UAAA,GAAAkB,QAAA,CAAAM,IAAA;QACAH,MAAA,CAAAtB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAsB,MAAA,CAAA1B,OAAA;MACA,CACA;IACA;IACA;IACA8B,cAAA,WAAAA,eAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAC,IAAA,CAAAC,IAAA,CAAAC,aAAA,EAAAL,GAAA,CAAAjB,QAAA;IACA;IACA;IACAuB,iBAAA,WAAAA,kBAAAN,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAC,IAAA,CAAAC,IAAA,CAAAG,gBAAA,EAAAP,GAAA,CAAAQ,WAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;IACA,aACAI,WAAA,WAAAA,YAAA;MACA,KAAApC,WAAA,CAAAC,OAAA;MACA,KAAAc,OAAA;IACA;IACA,aACAsB,UAAA,WAAAA,WAAA;MACA,KAAAvC,SAAA;MACA,KAAAwC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhD,GAAA,GAAAgD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA;MACA,KAAAlD,QAAA,IAAA+C,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAvB,GAAA;MACA,KAAAzB,IAAA;MACA,KAAAE,IAAA,GAAAuB,GAAA;IACA;IACA,aACAwB,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,SAAA,QAAAxD,GAAA;MACA,KAAAyD,MAAA,CAAAC,OAAA,oBAAAF,SAAA,aAAAnC,IAAA;QACA,WAAAsC,iBAAA,EAAAH,SAAA;MACA,GAAAnC,IAAA;QACAkC,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAE,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAN,MAAA,CAAAC,OAAA,qBAAArC,IAAA;QACA,WAAA2C,mBAAA;MACA,GAAA3C,IAAA;QACA0C,MAAA,CAAAxC,OAAA;QACAwC,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAI,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,+BAAAC,cAAA,CAAAC,OAAA,MACA,KAAA5D,WAAA,UAAA6D,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}