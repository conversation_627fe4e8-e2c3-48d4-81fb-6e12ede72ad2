{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\Pagination\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Pagination", "sourcesContent": ["<template>\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\n    <el-pagination\n      :background=\"background\"\n      :current-page.sync=\"currentPage\"\n      :page-size.sync=\"pageSize\"\n      :layout=\"layout\"\n      :page-sizes=\"pageSizes\"\n      :pager-count=\"pagerCount\"\n      :total=\"total\"\n      v-bind=\"$attrs\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"handleCurrentChange\"\n    />\n  </div>\n</template>\n\n<script>\nimport { scrollTo } from '@/utils/scroll-to'\n\nexport default {\n  name: 'Pagination',\n  props: {\n    total: {\n      required: true,\n      type: Number\n    },\n    page: {\n      type: Number,\n      default: 1\n    },\n    limit: {\n      type: Number,\n      default: 20\n    },\n    pageSizes: {\n      type: Array,\n      default() {\n        return [10, 20, 30, 50]\n      }\n    },\n    // 移动端页码按钮的数量端默认值5\n    pagerCount: {\n      type: Number,\n      default: document.body.clientWidth < 992 ? 5 : 7\n    },\n    layout: {\n      type: String,\n      default: 'total, sizes, prev, pager, next, jumper'\n    },\n    background: {\n      type: Boolean,\n      default: true\n    },\n    autoScroll: {\n      type: Boolean,\n      default: true\n    },\n    hidden: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n    };\n  },\n  computed: {\n    currentPage: {\n      get() {\n        return this.page\n      },\n      set(val) {\n        this.$emit('update:page', val)\n      }\n    },\n    pageSize: {\n      get() {\n        return this.limit\n      },\n      set(val) {\n        this.$emit('update:limit', val)\n      }\n    }\n  },\n  methods: {\n    handleSizeChange(val) {\n      if (this.currentPage * val > this.total) {\n        this.currentPage = 1\n      }\n      this.$emit('pagination', { page: this.currentPage, limit: val })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    },\n    handleCurrentChange(val) {\n      this.$emit('pagination', { page: val, limit: this.pageSize })\n      if (this.autoScroll) {\n        scrollTo(0, 800)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  background: #fff;\n}\n.pagination-container.hidden {\n  display: none;\n}\n</style>\n"]}]}