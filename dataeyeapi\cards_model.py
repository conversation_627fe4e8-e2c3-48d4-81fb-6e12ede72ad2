from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text, BigInteger, SmallInteger, func
from config.database import Base
import datetime

class Card(Base):
    __tablename__ = 'cards'
    uid = Column(String(36), primary_key=True, comment='卡片唯一标识符')
    pinboard_uid = Column(String(36), ForeignKey('pinboards.uid'), nullable=False, comment='所属Pinboard ID')
    title = Column(String(200), nullable=False, comment='卡片标题')
    card_type = Column(Integer, nullable=False, comment='卡片类型(1-分析卡片 2-监控卡片 3-报表卡片 4-仪表盘卡片)')
    sentence_type = Column(Integer, default=0, comment='语句类型')
    result_type = Column(Integer, default=0, comment='结果类型')
    y_axis_scale = Column(SmallInteger, default=1, comment='纵轴缩放开关')
    datetime_anchor = Column(String(30), comment='时间锚点')
    position_x = Column(Integer, default=0, comment='X坐标位置')
    position_y = Column(Integer, default=0, comment='Y坐标位置')
    width = Column(Integer, default=4, comment='卡片宽度')
    height = Column(Integer, default=3, comment='卡片高度')
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
