{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\break.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\blots\\break.js", "mtime": 1749172157164}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "Break", "_EmbedBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "optimize", "prev", "next", "remove", "length", "undefined", "EmbedBlot", "blotName", "tagName", "_default", "exports"], "sources": ["../../src/blots/break.ts"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\n\nclass Break extends EmbedBlot {\n  static value() {\n    return undefined;\n  }\n\n  optimize() {\n    if (this.prev || this.next) {\n      this.remove();\n    }\n  }\n\n  length() {\n    return 0;\n  }\n\n  value() {\n    return '';\n  }\n}\nBreak.blotName = 'break';\nBreak.tagName = 'BR';\n\nexport default Break;\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAqC,IAE/BC,KAAK,0BAAAC,UAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,KAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,KAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,KAAA,EAAAC,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,KAAA;IAAAQ,GAAA;IAAAC,KAAA,EAKT,SAAAC,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACC,IAAI,IAAI,IAAI,CAACC,IAAI,EAAE;QAC1B,IAAI,CAACC,MAAM,CAAC,CAAC;MACf;IACF;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEA,SAAAK,MAAMA,CAAA,EAAG;MACP,OAAO,CAAC;IACV;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,EAAE;IACX;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAhBA,SAAOA,KAAKA,CAAA,EAAG;MACb,OAAOM,SAAS;IAClB;EAAA;AAAA,EAHkBC,oBAAS;AAmB7BhB,KAAK,CAACiB,QAAQ,GAAG,OAAO;AACxBjB,KAAK,CAACkB,OAAO,GAAG,IAAI;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAjB,OAAA,GAELH,KAAK", "ignoreList": []}]}