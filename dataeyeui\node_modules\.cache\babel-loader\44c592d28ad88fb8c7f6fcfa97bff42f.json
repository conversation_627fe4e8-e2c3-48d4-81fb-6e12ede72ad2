{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\login.vue", "mtime": 1747038934000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_js<PERSON><PERSON>ie", "_interopRequireDefault", "_jsencrypt", "name", "data", "codeUrl", "loginForm", "username", "password", "rememberMe", "code", "uuid", "loginRules", "required", "trigger", "message", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "redirect", "undefined", "watch", "$route", "handler", "route", "query", "immediate", "created", "getCode", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this", "getCodeImg", "then", "res", "registerEnabled", "img", "Cookies", "get", "decrypt", "Boolean", "handleLogin", "_this2", "$refs", "validate", "valid", "set", "expires", "encrypt", "remove", "$store", "dispatch", "$router", "push", "path", "catch"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\n  <div class=\"login\">\n    <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\n      <h3 class=\"title\">金刚数瞳</h3>\n      <el-form-item prop=\"username\">\n        <el-input\n          v-model=\"loginForm.username\"\n          type=\"text\"\n          auto-complete=\"off\"\n          placeholder=\"账号\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"loginForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"loginForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleLogin\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"login-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"login-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-checkbox v-model=\"loginForm.rememberMe\" style=\"margin:0px 0px 25px 0px;\">记住密码</el-checkbox>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleLogin\"\n        >\n          <span v-if=\"!loading\">登 录</span>\n          <span v-else>登 录 中...</span>\n        </el-button>\n        <div style=\"float: right;\" v-if=\"register\">\n          <router-link class=\"link-type\" :to=\"'/register'\">立即注册</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-login-footer\">\n      <span>Copyright © 2024 insistence.tech All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\";\nimport Cookies from \"js-cookie\";\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\n\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      codeUrl: \"\",\n      loginForm: {\n        username: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\"\n      },\n      loginRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      // 验证码开关\n      captchaEnabled: true,\n      // 注册开关\n      register: false,\n      redirect: undefined\n    };\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect;\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getCode();\n    this.getCookie();\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;\n        this.register = res.registerEnabled === undefined ? false : res.registerEnabled;\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\n          this.loginForm.uuid = res.uuid;\n        }\n      });\n    },\n    getCookie() {\n      const username = Cookies.get(\"username\");\n      const password = Cookies.get(\"password\");\n      const rememberMe = Cookies.get('rememberMe')\n      this.loginForm = {\n        username: username === undefined ? this.loginForm.username : username,\n        password: password === undefined ? this.loginForm.password : decrypt(password),\n        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)\n      };\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          if (this.loginForm.rememberMe) {\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 });\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 });\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });\n          } else {\n            Cookies.remove(\"username\");\n            Cookies.remove(\"password\");\n            Cookies.remove('rememberMe');\n          }\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\n            this.$router.push({ path: this.redirect || \"/\" }).catch(()=>{});\n          }).catch(() => {\n            this.loading = false;\n            if (this.captchaEnabled) {\n              this.getCode();\n            }\n          });\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.login {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.login-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.login-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.login-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-login-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.login-code-img {\n  height: 38px;\n}\n</style>\n"], "mappings": ";;;;;;;;AAgEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,UAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,IAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,OAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,QAAA,EAAAC;IACA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,KAAA;QACA,KAAAL,QAAA,GAAAK,KAAA,CAAAC,KAAA,IAAAD,KAAA,CAAAC,KAAA,CAAAN,QAAA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAd,cAAA,GAAAiB,GAAA,CAAAjB,cAAA,KAAAG,SAAA,UAAAc,GAAA,CAAAjB,cAAA;QACAc,KAAA,CAAAb,QAAA,GAAAgB,GAAA,CAAAC,eAAA,KAAAf,SAAA,WAAAc,GAAA,CAAAC,eAAA;QACA,IAAAJ,KAAA,CAAAd,cAAA;UACAc,KAAA,CAAA1B,OAAA,8BAAA6B,GAAA,CAAAE,GAAA;UACAL,KAAA,CAAAzB,SAAA,CAAAK,IAAA,GAAAuB,GAAA,CAAAvB,IAAA;QACA;MACA;IACA;IACAkB,SAAA,WAAAA,UAAA;MACA,IAAAtB,QAAA,GAAA8B,iBAAA,CAAAC,GAAA;MACA,IAAA9B,QAAA,GAAA6B,iBAAA,CAAAC,GAAA;MACA,IAAA7B,UAAA,GAAA4B,iBAAA,CAAAC,GAAA;MACA,KAAAhC,SAAA;QACAC,QAAA,EAAAA,QAAA,KAAAa,SAAA,QAAAd,SAAA,CAAAC,QAAA,GAAAA,QAAA;QACAC,QAAA,EAAAA,QAAA,KAAAY,SAAA,QAAAd,SAAA,CAAAE,QAAA,OAAA+B,kBAAA,EAAA/B,QAAA;QACAC,UAAA,EAAAA,UAAA,KAAAW,SAAA,WAAAoB,OAAA,CAAA/B,UAAA;MACA;IACA;IACAgC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAArC,SAAA,CAAAsC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA1B,OAAA;UACA,IAAA0B,MAAA,CAAApC,SAAA,CAAAG,UAAA;YACA4B,iBAAA,CAAAS,GAAA,aAAAJ,MAAA,CAAApC,SAAA,CAAAC,QAAA;cAAAwC,OAAA;YAAA;YACAV,iBAAA,CAAAS,GAAA,iBAAAE,kBAAA,EAAAN,MAAA,CAAApC,SAAA,CAAAE,QAAA;cAAAuC,OAAA;YAAA;YACAV,iBAAA,CAAAS,GAAA,eAAAJ,MAAA,CAAApC,SAAA,CAAAG,UAAA;cAAAsC,OAAA;YAAA;UACA;YACAV,iBAAA,CAAAY,MAAA;YACAZ,iBAAA,CAAAY,MAAA;YACAZ,iBAAA,CAAAY,MAAA;UACA;UACAP,MAAA,CAAAQ,MAAA,CAAAC,QAAA,UAAAT,MAAA,CAAApC,SAAA,EAAA2B,IAAA;YACAS,MAAA,CAAAU,OAAA,CAAAC,IAAA;cAAAC,IAAA,EAAAZ,MAAA,CAAAvB,QAAA;YAAA,GAAAoC,KAAA;UACA,GAAAA,KAAA;YACAb,MAAA,CAAA1B,OAAA;YACA,IAAA0B,MAAA,CAAAzB,cAAA;cACAyB,MAAA,CAAAd,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}