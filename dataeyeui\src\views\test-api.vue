<template>
  <div class="test-api">
    <h2>API连接测试</h2>
    
    <el-card class="test-card">
      <div slot="header">
        <span>测试后端连接</span>
      </div>
      
      <el-button @click="testConnection" type="primary" :loading="loading">
        测试连接
      </el-button>
      
      <div v-if="result" class="result-area">
        <h4>测试结果：</h4>
        <pre>{{ result }}</pre>
      </div>
      
      <div v-if="error" class="error-area">
        <h4>错误信息：</h4>
        <pre>{{ error }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'TestApi',
  data() {
    return {
      loading: false,
      result: null,
      error: null
    }
  },
  methods: {
    async testConnection() {
      this.loading = true
      this.result = null
      this.error = null
      
      try {
        // 直接测试后端接口，不通过前端代理
        const response = await axios.get('http://localhost:9099/pinboard/test', {
          timeout: 5000,
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        this.result = JSON.stringify(response.data, null, 2)
        console.log('API测试成功:', response.data)
        
      } catch (error) {
        this.error = {
          message: error.message,
          code: error.code,
          response: error.response ? {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data
          } : null
        }
        console.error('API测试失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.test-api {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 0 auto;
}

.result-area, .error-area {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
}

.result-area {
  background-color: #f0f9ff;
  border: 1px solid #67c23a;
}

.error-area {
  background-color: #fef0f0;
  border: 1px solid #f56c6c;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
