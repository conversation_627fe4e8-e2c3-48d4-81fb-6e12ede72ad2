from sqlalchemy import Column, String, BigInteger, DateTime, Text, Integer, SmallInteger
from config.database import Base
import datetime

class TopQuestion(Base):
    __tablename__ = 'top_questions'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='问题ID')
    question = Column(Text, nullable=False, comment='问题内容')
    search_count = Column(Integer, default=0, comment='搜索次数')
    category = Column(String(50), comment='问题分类')
    tags = Column(String(200), comment='标签(JSON格式)')
    is_active = Column(SmallInteger, default=1, comment='是否激活')  # 使用SmallInteger匹配TINYINT(1)
    sort_order = Column(Integer, default=0, comment='排序权重')
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='更新时间')
