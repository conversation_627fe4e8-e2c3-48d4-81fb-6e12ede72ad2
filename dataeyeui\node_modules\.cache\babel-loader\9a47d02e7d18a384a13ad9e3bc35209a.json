{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\directive\\permission\\hasRole.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\directive\\permission\\hasRole.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmVycm9yLmNhdXNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5zb21lLmpzIik7CnZhciBfc3RvcmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvc3RvcmUiKSk7Ci8qKgoqIHYtaGFzUm9sZSDop5LoibLmnYPpmZDlpITnkIYKKiBDb3B5cmlnaHQgKGMpIDIwMTkgcnVveWkKKi8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGluc2VydGVkOiBmdW5jdGlvbiBpbnNlcnRlZChlbCwgYmluZGluZywgdm5vZGUpIHsKICAgIHZhciB2YWx1ZSA9IGJpbmRpbmcudmFsdWU7CiAgICB2YXIgc3VwZXJfYWRtaW4gPSAiYWRtaW4iOwogICAgdmFyIHJvbGVzID0gX3N0b3JlLmRlZmF1bHQuZ2V0dGVycyAmJiBfc3RvcmUuZGVmYXVsdC5nZXR0ZXJzLnJvbGVzOwogICAgaWYgKHZhbHVlICYmIHZhbHVlIGluc3RhbmNlb2YgQXJyYXkgJiYgdmFsdWUubGVuZ3RoID4gMCkgewogICAgICB2YXIgcm9sZUZsYWcgPSB2YWx1ZTsKICAgICAgdmFyIGhhc1JvbGUgPSByb2xlcy5zb21lKGZ1bmN0aW9uIChyb2xlKSB7CiAgICAgICAgcmV0dXJuIHN1cGVyX2FkbWluID09PSByb2xlIHx8IHJvbGVGbGFnLmluY2x1ZGVzKHJvbGUpOwogICAgICB9KTsKICAgICAgaWYgKCFoYXNSb2xlKSB7CiAgICAgICAgZWwucGFyZW50Tm9kZSAmJiBlbC5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGVsKTsKICAgICAgfQogICAgfSBlbHNlIHsKICAgICAgdGhyb3cgbmV3IEVycm9yKCJcdThCRjdcdThCQkVcdTdGNkVcdTg5RDJcdTgyNzJcdTY3NDNcdTk2NTBcdTY4MDdcdTdCN0VcdTUwM0NcIiIpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_default", "exports", "default", "inserted", "el", "binding", "vnode", "value", "super_admin", "roles", "store", "getters", "Array", "length", "roleFlag", "hasRole", "some", "role", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["D:/jgst/dataeyeui/src/directive/permission/hasRole.js"], "sourcesContent": [" /**\n * v-hasRole 角色权限处理\n * Copyright (c) 2019 ruoyi\n */\n\nimport store from '@/store'\n\nexport default {\n  inserted(el, binding, vnode) {\n    const { value } = binding\n    const super_admin = \"admin\";\n    const roles = store.getters && store.getters.roles\n\n    if (value && value instanceof Array && value.length > 0) {\n      const roleFlag = value\n\n      const hasRole = roles.some(role => {\n        return super_admin === role || roleFlag.includes(role)\n      })\n\n      if (!hasRole) {\n        el.parentNode && el.parentNode.removeChild(el)\n      }\n    } else {\n      throw new Error(`请设置角色权限标签值\"`)\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AALC;AACD;AACA;AACA;AAHC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOc;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,WAAW,GAAG,OAAO;IAC3B,IAAMC,KAAK,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,KAAK;IAElD,IAAIF,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,QAAQ,GAAGP,KAAK;MAEtB,IAAMQ,OAAO,GAAGN,KAAK,CAACO,IAAI,CAAC,UAAAC,IAAI,EAAI;QACjC,OAAOT,WAAW,KAAKS,IAAI,IAAIH,QAAQ,CAACI,QAAQ,CAACD,IAAI,CAAC;MACxD,CAAC,CAAC;MAEF,IAAI,CAACF,OAAO,EAAE;QACZX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,iEAAc,CAAC;IAChC;EACF;AACF,CAAC", "ignoreList": []}]}