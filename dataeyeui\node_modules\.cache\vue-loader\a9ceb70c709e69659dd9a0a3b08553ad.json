{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\Editor\\index.vue?vue&type=style&index=0&id=7480c5e0&lang=css", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\Editor\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Editor", "sourcesContent": ["<template>\n  <div>\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n      v-if=\"this.type == 'url'\"\n    >\n    </el-upload>\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\n  </div>\n</template>\n\n<script>\nimport Quill from \"quill\";\nimport \"quill/dist/quill.core.css\";\nimport \"quill/dist/quill.snow.css\";\nimport \"quill/dist/quill.bubble.css\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"Editor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 高度 */\n    height: {\n      type: Number,\n      default: null,\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: null,\n    },\n    /* 只读 */\n    readOnly: {\n      type: Boolean,\n      default: false,\n    },\n    /* 上传文件大小限制(MB) */\n    fileSize: {\n      type: Number,\n      default: 5,\n    },\n    /* 类型（base64格式、url格式） */\n    type: {\n      type: String,\n      default: \"url\",\n    }\n  },\n  data() {\n    return {\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      Quill: null,\n      currentValue: \"\",\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [{ align: [] }],                                 // 对齐方式\n            [\"clean\"],                                       // 清除文本格式\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\n          ],\n        },\n        placeholder: \"请输入内容\",\n        readOnly: this.readOnly,\n      },\n    };\n  },\n  computed: {\n    styles() {\n      let style = {};\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`;\n      }\n      if (this.height) {\n        style.height = `${this.height}px`;\n      }\n      return style;\n    },\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val;\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init();\n  },\n  beforeDestroy() {\n    this.Quill = null;\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor;\n      this.Quill = new Quill(editor, this.options);\n      // 如果设置了上传地址则自定义图片上传事件\n      if (this.type == 'url') {\n        let toolbar = this.Quill.getModule(\"toolbar\");\n        toolbar.addHandler(\"image\", (value) => {\n          if (value) {\n            this.$refs.upload.$children[0].$refs.input.click();\n          } else {\n            this.quill.format(\"image\", false);\n          }\n        });\n      }\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue);\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML;\n        const text = this.Quill.getText();\n        const quill = this.Quill;\n        this.currentValue = html;\n        this.$emit(\"input\", html);\n        this.$emit(\"on-change\", { html, text, quill });\n      });\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        this.$emit(\"on-text-change\", delta, oldDelta, source);\n      });\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        this.$emit(\"on-selection-change\", range, oldRange, source);\n      });\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\n        this.$emit(\"on-editor-change\", eventName, ...args);\n      });\n    },\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"];\n      const isJPG = type.includes(file.type);\n      // 检验文件格式\n      if (!isJPG) {\n        this.$message.error(`图片格式错误!`);\n        return false;\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize;\n        if (!isLt) {\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);\n          return false;\n        }\n      }\n      return true;\n    },\n    handleUploadSuccess(res, file) {\n      // 如果上传成功\n      if (res.code == 200) {\n        // 获取富文本组件实例\n        let quill = this.Quill;\n        // 获取光标所在位置\n        let length = quill.getSelection().index;\n        // 插入图片  res.url为服务器返回的图片地址\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName);\n        // 调整光标到最后\n        quill.setSelection(length + 1);\n      } else {\n        this.$message.error(\"图片插入失败\");\n      }\n    },\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\");\n    },\n  },\n};\n</script>\n\n<style>\n.editor, .ql-toolbar {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n.quill-img {\n  display: none;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>\n"]}]}