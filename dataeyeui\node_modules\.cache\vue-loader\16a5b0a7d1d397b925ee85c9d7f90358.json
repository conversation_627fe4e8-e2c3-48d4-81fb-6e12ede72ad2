{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\mybutton\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\mybutton\\index.vue", "mtime": 1750058108898}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVzZXJTZXR0aW5ncywgc2F2ZVVzZXJTZXR0aW5ncyB9IGZyb20gJ0AvYXBpL3VzZXJfc2V0dGluZ3MnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ015QnV0dG9uJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlVGFiOiAncGVyc29uYWwnLCAvLyDpu5jorqTmmL7npLrkuKrkurrorr7nva7moIfnrb4KICAgICAgdXNlcklkOiAnJywKICAgICAgcGFzc3dvcmQ6ICcqKioqKioqKicsCiAgICAgIHVzZXJJZExlbmd0aDogMCwKICAgICAgY3VycmVudFVzZXJJZDogMSwgLy8g5b2T5YmN55m75b2V55So5oi3SUTvvIzlrp7pmYXlupTor6Xku47nmbvlvZXnirbmgIHojrflj5YKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNlYXJjaFNldHRpbmdzOiB7CiAgICAgICAgc21hcnRUaXBzOiB0cnVlLAogICAgICAgIHRyeVN1Z2dlc3Rpb25zOiB0cnVlLAogICAgICAgIHJlbGF0ZWRTZWFyY2g6IHRydWUKICAgICAgfSwKICAgICAgYW5hbHlzaXNTZXR0aW5nczogewogICAgICAgIGFub21hbHk6IHRydWUsCiAgICAgICAgbWF4aW11bTogdHJ1ZSwKICAgICAgICBtaW5pbXVtOiB0cnVlLAogICAgICAgIGF2ZXJhZ2U6IHRydWUsCiAgICAgICAgZGlzY3JldGU6IHRydWUsCiAgICAgICAgYmlvVGFnOiB0cnVlLAogICAgICAgIGNoYWluQW5vbWFseTogdHJ1ZSwKICAgICAgICBjeWNsaWNhbEZsdWN0dWF0aW9uOiB0cnVlCiAgICAgIH0sCiAgICAgIHByZWRpY3RNb2RlOiAnZm9sbG93JywKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB1c2VySWQodmFsKSB7CiAgICAgIHRoaXMudXNlcklkTGVuZ3RoID0gdmFsLmxlbmd0aDsKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmZldGNoVXNlclNldHRpbmdzKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGZldGNoVXNlclNldHRpbmdzKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRVc2VyU2V0dGluZ3ModGhpcy5jdXJyZW50VXNlcklkKQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIGNvbnN0IHsgdXNlciwgc2VhcmNoU2V0dGluZ3MsIGFuYWx5c2lzU2V0dGluZ3MgfSA9IHJlc3BvbnNlLmRhdGEKICAgICAgICAgIC8vIOabtOaWsOeUqOaIt+S/oeaBrwogICAgICAgICAgdGhpcy51c2VySWQgPSB1c2VyLnVzZXJJZCB8fCB1c2VyLm5pY2tOYW1lIHx8ICcnCiAgICAgICAgICB0aGlzLnVzZXJJZExlbmd0aCA9IHRoaXMudXNlcklkLmxlbmd0aAogICAgICAgICAgLy8g5pu05paw6K6+572uCiAgICAgICAgICB0aGlzLnNlYXJjaFNldHRpbmdzID0geyAuLi50aGlzLnNlYXJjaFNldHRpbmdzLCAuLi5zZWFyY2hTZXR0aW5ncyB9CiAgICAgICAgICB0aGlzLmFuYWx5c2lzU2V0dGluZ3MgPSB7IC4uLnRoaXMuYW5hbHlzaXNTZXR0aW5ncywgLi4uYW5hbHlzaXNTZXR0aW5ncyB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+iuvue9ruWksei0pe+8micgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIGFzeW5jIHNhdmVTZWFyY2hTZXR0aW5ncygpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc2F2ZVVzZXJTZXR0aW5ncyh0aGlzLmN1cnJlbnRVc2VySWQsIHsKICAgICAgICAgIHNlYXJjaFNldHRpbmdzOiB0aGlzLnNlYXJjaFNldHRpbmdzCiAgICAgICAgfSkKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogJ+aQnOe0ouaPkOekuuiuvue9ruS/neWtmOaIkOWKnycsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOaQnOe0ouiuvue9ruWksei0pe+8micgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIGFzeW5jIHNhdmVBbmFseXNpc1NldHRpbmdzKCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzYXZlVXNlclNldHRpbmdzKHRoaXMuY3VycmVudFVzZXJJZCwgewogICAgICAgICAgYW5hbHlzaXNTZXR0aW5nczogdGhpcy5hbmFseXNpc1NldHRpbmdzCiAgICAgICAgfSkKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogJ+aZuuiDveino+aekOiuvue9ruS/neWtmOaIkOWKnycsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOWIhuaekOiuvue9ruWksei0pe+8micgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIHNhdmVTZXR0aW5nKCkgewogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+iuvue9ruW3suS/neWtmCcpCiAgICB9LAogICAgYXN5bmMgcmVzZXRUb0RlZmF1bHQoKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgemHjee9ruS4uum7mOiupOiuvue9ruWQl++8n+i/meWwhuimhuebluaCqOW9k+WJjeeahOaJgOacieS4quaAp+WMluiuvue9ruOAgicsICfnoa7orqTph43nva4nLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgICAgdHJ5IHsKICAgICAgICAgIC8vIOmHjee9ruS4uum7mOiupOiuvue9rgogICAgICAgICAgY29uc3QgZGVmYXVsdFNlYXJjaFNldHRpbmdzID0gewogICAgICAgICAgICBzbWFydFRpcHM6IHRydWUsCiAgICAgICAgICAgIHRyeVN1Z2dlc3Rpb25zOiB0cnVlLAogICAgICAgICAgICByZWxhdGVkU2VhcmNoOiB0cnVlCiAgICAgICAgICB9CiAgICAgICAgICBjb25zdCBkZWZhdWx0QW5hbHlzaXNTZXR0aW5ncyA9IHsKICAgICAgICAgICAgYW5vbWFseTogdHJ1ZSwKICAgICAgICAgICAgbWF4aW11bTogdHJ1ZSwKICAgICAgICAgICAgbWluaW11bTogdHJ1ZSwKICAgICAgICAgICAgYXZlcmFnZTogdHJ1ZSwKICAgICAgICAgICAgZGlzY3JldGU6IHRydWUsCiAgICAgICAgICAgIGJpb1RhZzogdHJ1ZSwKICAgICAgICAgICAgY2hhaW5Bbm9tYWx5OiB0cnVlLAogICAgICAgICAgICBjeWNsaWNhbEZsdWN0dWF0aW9uOiB0cnVlCiAgICAgICAgICB9CgogICAgICAgICAgLy8g5L+d5a2Y6buY6K6k6K6+572u5Yiw5pWw5o2u5bqTCiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNhdmVVc2VyU2V0dGluZ3ModGhpcy5jdXJyZW50VXNlcklkLCB7CiAgICAgICAgICAgIHNlYXJjaFNldHRpbmdzOiBkZWZhdWx0U2VhcmNoU2V0dGluZ3MsCiAgICAgICAgICAgIGFuYWx5c2lzU2V0dGluZ3M6IGRlZmF1bHRBbmFseXNpc1NldHRpbmdzCiAgICAgICAgICB9KQoKICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgLy8g5pu05paw5pys5Zyw5pWw5o2uCiAgICAgICAgICAgIHRoaXMuc2VhcmNoU2V0dGluZ3MgPSB7IC4uLmRlZmF1bHRTZWFyY2hTZXR0aW5ncyB9CiAgICAgICAgICAgIHRoaXMuYW5hbHlzaXNTZXR0aW5ncyA9IHsgLi4uZGVmYXVsdEFuYWx5c2lzU2V0dGluZ3MgfQoKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+W3sumHjee9ruS4uum7mOiupOiuvue9ricsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mHjee9ruiuvue9ruWksei0pe+8micgKyAoZXJyb3IubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpCiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g55So5oi35Y+W5raI5pON5L2cCiAgICAgIH0pCiAgICB9LAogICAgZ29Ub0xvZ2luKCkgewogICAgICAvLyDot7PovazliLDnmbvlvZXpobXpnaIKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9sb2dpbicpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8JA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/mybutton", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"tab-container\">\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'personal' }\" @click=\"activeTab = 'personal'\">个人设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'workspace' }\" @click=\"activeTab = 'workspace'\">工作区设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'analysis' }\" @click=\"activeTab = 'analysis'\">智能解析设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'search' }\" @click=\"activeTab = 'search'\">搜索提示设置</div>\n    </div>\n\n    <!-- 加载状态 -->\n    <el-skeleton v-if=\"loading\" :rows=\"6\" animated />\n\n    <!-- 个人设置 -->\n    <div v-if=\"activeTab === 'personal' && !loading\" class=\"form-container\">\n      <div class=\"form-item\">\n        <div class=\"form-label\">用户ID：</div>\n        <div class=\"form-input\">\n          <el-input v-model=\"userId\" placeholder=\"请输入用户ID\"></el-input>\n        </div>\n        <div class=\"form-count\">{{ userIdLength }} / 20</div>\n      </div>\n\n      <div class=\"form-item\">\n        <div class=\"form-label\">密码：</div>\n        <div class=\"form-input\">\n          <el-input v-model=\"password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\n        </div>\n      </div>\n\n      <div class=\"form-item\">\n        <div class=\"form-label\"></div>\n        <div class=\"form-input\">\n          <a href=\"javascript:;\" class=\"login-link\" @click=\"goToLogin\">返回登录 ></a>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"resetToDefault\" :loading=\"loading\">设为默认</el-button>\n      </div>\n    </div>\n\n    <!-- 搜索提示设置 -->\n    <div v-if=\"activeTab === 'search' && !loading\" class=\"form-container\">\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">智能提示</div>\n          <el-switch v-model=\"searchSettings.smartTips\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">根据您的搜索行为切换不同的提示词</div>\n      </div>\n\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">默认字段提示</div>\n          <el-switch v-model=\"searchSettings.trySuggestions\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">任何情况对焦搜索框都给出字段提示，可手动切换到问题提示</div>\n      </div>\n\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">默认问题提示</div>\n          <el-switch v-model=\"searchSettings.relatedSearch\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">任何情况对焦搜索框都给出问题提示，可手动切换到字段提示</div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"saveSearchSettings\" :loading=\"loading\">保存设置</el-button>\n      </div>\n    </div>\n\n    <!-- 其他标签页的内容可以根据需要添加 -->\n    <div v-if=\"activeTab === 'workspace' && !loading\" class=\"form-container\">\n      <div class=\"setting-container\">\n        <div v-if=\"activeTab === 'workspace'\" class=\"workspace-setting-content\">\n          <div class=\"setting-label\">预测分析</div>\n          <el-radio-group v-model=\"predictMode\">\n            <el-radio :label=\"'follow'\">跟随工作区设置</el-radio>\n            <el-radio :label=\"'alwaysOn'\">始终打开</el-radio>\n            <el-radio :label=\"'alwaysOff'\">始终关闭</el-radio>\n          </el-radio-group>\n        </div>\n        <el-button class=\"save-btn\" type=\"primary\" @click=\"saveSetting\">保存设置</el-button>\n      </div>\n    </div>\n\n    <div v-if=\"activeTab === 'analysis' && !loading\" class=\"form-container\">\n      <!-- 基础解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">基础解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">异常值</div>\n          <el-switch v-model=\"analysisSettings.anomaly\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.anomaly\">展示数据中显著高于或低于其他值的点</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">最大值</div>\n          <el-switch v-model=\"analysisSettings.maximum\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">最小值</div>\n          <el-switch v-model=\"analysisSettings.minimum\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">平均值</div>\n          <el-switch v-model=\"analysisSettings.average\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">离散统计</div>\n          <el-switch v-model=\"analysisSettings.discrete\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n      </div>\n\n      <!-- 维度相关解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">维度相关解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">80/20构成</div>\n          <el-switch v-model=\"analysisSettings.bioTag\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.bioTag\">\n          判断数据是否符合二八分布,即这组数据中,最重要的维度只占其中一小部分,约20%,其余80%尽管是多数,却是次要的\n        </div>\n      </div>\n\n      <!-- 趋势类解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">趋势类解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">环比异常值</div>\n          <el-switch v-model=\"analysisSettings.chainAnomaly\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.chainAnomaly\">\n          展示数据中增速或降幅显著高于其他时间的点\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">周期性波动</div>\n          <el-switch v-model=\"analysisSettings.cyclicalFluctuation\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"saveAnalysisSettings\" :loading=\"loading\">保存设置</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getUserSettings, saveUserSettings } from '@/api/user_settings'\n\nexport default {\n  name: 'MyButton',\n  data() {\n    return {\n      activeTab: 'personal', // 默认显示个人设置标签\n      userId: '',\n      password: '********',\n      userIdLength: 0,\n      currentUserId: 1, // 当前登录用户ID，实际应该从登录状态获取\n      loading: false,\n      searchSettings: {\n        smartTips: true,\n        trySuggestions: true,\n        relatedSearch: true\n      },\n      analysisSettings: {\n        anomaly: true,\n        maximum: true,\n        minimum: true,\n        average: true,\n        discrete: true,\n        bioTag: true,\n        chainAnomaly: true,\n        cyclicalFluctuation: true\n      },\n      predictMode: 'follow',\n    }\n  },\n  watch: {\n    userId(val) {\n      this.userIdLength = val.length;\n    }\n  },\n  created() {\n    this.fetchUserSettings()\n  },\n  methods: {\n    async fetchUserSettings() {\n      this.loading = true\n      try {\n        const response = await getUserSettings(this.currentUserId)\n        if (response.code === 200) {\n          const { user, searchSettings, analysisSettings } = response.data\n          // 更新用户信息\n          this.userId = user.userId || user.nickName || ''\n          this.userIdLength = this.userId.length\n          // 更新设置\n          this.searchSettings = { ...this.searchSettings, ...searchSettings }\n          this.analysisSettings = { ...this.analysisSettings, ...analysisSettings }\n        }\n      } catch (error) {\n        this.$message.error('获取用户设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    async saveSearchSettings() {\n      this.loading = true\n      try {\n        const response = await saveUserSettings(this.currentUserId, {\n          searchSettings: this.searchSettings\n        })\n        if (response.code === 200) {\n          this.$message({\n            message: '搜索提示设置保存成功',\n            type: 'success'\n          });\n        }\n      } catch (error) {\n        this.$message.error('保存搜索设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    async saveAnalysisSettings() {\n      this.loading = true\n      try {\n        const response = await saveUserSettings(this.currentUserId, {\n          analysisSettings: this.analysisSettings\n        })\n        if (response.code === 200) {\n          this.$message({\n            message: '智能解析设置保存成功',\n            type: 'success'\n          });\n        }\n      } catch (error) {\n        this.$message.error('保存分析设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    saveSetting() {\n      this.$message.success('设置已保存')\n    },\n    async resetToDefault() {\n      this.$confirm('确定要重置为默认设置吗？这将覆盖您当前的所有个性化设置。', '确认重置', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        this.loading = true\n        try {\n          // 重置为默认设置\n          const defaultSearchSettings = {\n            smartTips: true,\n            trySuggestions: true,\n            relatedSearch: true\n          }\n          const defaultAnalysisSettings = {\n            anomaly: true,\n            maximum: true,\n            minimum: true,\n            average: true,\n            discrete: true,\n            bioTag: true,\n            chainAnomaly: true,\n            cyclicalFluctuation: true\n          }\n\n          // 保存默认设置到数据库\n          const response = await saveUserSettings(this.currentUserId, {\n            searchSettings: defaultSearchSettings,\n            analysisSettings: defaultAnalysisSettings\n          })\n\n          if (response.code === 200) {\n            // 更新本地数据\n            this.searchSettings = { ...defaultSearchSettings }\n            this.analysisSettings = { ...defaultAnalysisSettings }\n\n            this.$message({\n              message: '已重置为默认设置',\n              type: 'success'\n            })\n          }\n        } catch (error) {\n          this.$message.error('重置设置失败：' + (error.message || '未知错误'))\n        } finally {\n          this.loading = false\n        }\n      }).catch(() => {\n        // 用户取消操作\n      })\n    },\n    goToLogin() {\n      // 跳转到登录页面\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  background-color: #fff;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.tab-container {\n  display: flex;\n  border-bottom: 1px solid #e6e6e6;\n  margin-bottom: 20px;\n}\n\n.tab-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  margin-right: 20px;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  font-weight: bold;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  width: 100%;\n  height: 2px;\n  background-color: #409EFF;\n}\n\n.form-container {\n  width: 100%;\n  max-width: 600px;\n}\n\n.form-item {\n  display: flex;\n  margin-bottom: 20px;\n  align-items: center;\n}\n\n.form-label {\n  width: 80px;\n  text-align: right;\n  padding-right: 10px;\n}\n\n.form-input {\n  flex: 1;\n}\n\n.form-count {\n  width: 60px;\n  text-align: right;\n  color: #999;\n  font-size: 12px;\n}\n\n.login-link {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.footer {\n  margin-top: 40px;\n  text-align: center;\n}\n\n.default-btn {\n  padding: 8px 20px;\n  border-radius: 4px;\n}\n\n/* 搜索提示设置样式 */\n.setting-section {\n  margin-bottom: 25px;\n}\n\n.setting-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.setting-title {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.setting-desc {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.placeholder {\n  color: #999;\n  font-size: 16px;\n  padding: 20px 0;\n}\n\n/* 智能解析设置样式 */\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.setting-item-title {\n  font-size: 14px;\n}\n\n.setting-item-desc {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-bottom: 15px;\n  margin-left: 10px;\n}\n\n/* 工作区设置样式 */\n.setting-container {\n  min-height: 100vh;\n  background: #fff;\n  padding: 40px 0 0 0;\n  position: relative;\n}\n\n.setting-tabs {\n  margin-left: 60px;\n}\n\n.workspace-setting-content {\n  margin-left: 60px;\n  margin-top: 32px;\n}\n\n.setting-label {\n  font-size: 16px;\n  margin-bottom: 18px;\n  font-weight: 400;\n}\n\n.el-radio-group {\n  display: flex;\n  flex-direction: column;\n  margin-top: 12px;\n}\n\n.el-radio {\n  margin-bottom: 12px;\n  font-size: 15px;\n}\n\n.save-btn {\n  position: fixed;\n  bottom: 32px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 120px;\n  height: 40px;\n  border-radius: 20px;\n  font-size: 16px;\n}\n</style>\n"]}]}