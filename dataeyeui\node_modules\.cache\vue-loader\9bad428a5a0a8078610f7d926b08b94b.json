{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\report\\recent\\index.vue?vue&type=template&id=6e1dc26f&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\report\\recent\\index.vue", "mtime": 1749633386000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749172157834}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}