{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\core\\theme.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\core\\theme.js", "mtime": 1749172159549}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jcmVhdGVDbGFzcy5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L2pnc3QvZGF0YWV5ZXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2RlZmluZVByb3BlcnR5LmpzIikpOwp2YXIgX1RoZW1lOwp2YXIgVGhlbWUgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkgewogIGZ1bmN0aW9uIFRoZW1lKHF1aWxsLCBvcHRpb25zKSB7CiAgICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBUaGVtZSk7CiAgICAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSh0aGlzLCAibW9kdWxlcyIsIHt9KTsKICAgIHRoaXMucXVpbGwgPSBxdWlsbDsKICAgIHRoaXMub3B0aW9ucyA9IG9wdGlvbnM7CiAgfQogIHJldHVybiAoMCwgX2NyZWF0ZUNsYXNzMi5kZWZhdWx0KShUaGVtZSwgW3sKICAgIGtleTogImluaXQiLAogICAgdmFsdWU6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIE9iamVjdC5rZXlzKHRoaXMub3B0aW9ucy5tb2R1bGVzKS5mb3JFYWNoKGZ1bmN0aW9uIChuYW1lKSB7CiAgICAgICAgaWYgKF90aGlzLm1vZHVsZXNbbmFtZV0gPT0gbnVsbCkgewogICAgICAgICAgX3RoaXMuYWRkTW9kdWxlKG5hbWUpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfSwgewogICAga2V5OiAiYWRkTW9kdWxlIiwKICAgIHZhbHVlOiBmdW5jdGlvbiBhZGRNb2R1bGUobmFtZSkgewogICAgICAvLyBAdHMtZXhwZWN0LWVycm9yCiAgICAgIHZhciBNb2R1bGVDbGFzcyA9IHRoaXMucXVpbGwuY29uc3RydWN0b3IuaW1wb3J0KCJtb2R1bGVzLyIuY29uY2F0KG5hbWUpKTsKICAgICAgdGhpcy5tb2R1bGVzW25hbWVdID0gbmV3IE1vZHVsZUNsYXNzKHRoaXMucXVpbGwsIHRoaXMub3B0aW9ucy5tb2R1bGVzW25hbWVdIHx8IHt9KTsKICAgICAgcmV0dXJuIHRoaXMubW9kdWxlc1tuYW1lXTsKICAgIH0KICB9XSk7Cn0oKTsKX1RoZW1lID0gVGhlbWU7CigwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKFRoZW1lLCAiREVGQVVMVFMiLCB7CiAgbW9kdWxlczoge30KfSk7CigwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKFRoZW1lLCAidGhlbWVzIiwgewogIGRlZmF1bHQ6IF9UaGVtZQp9KTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gVGhlbWU7"}, {"version": 3, "names": ["Theme", "quill", "options", "_classCallCheck2", "default", "_defineProperty2", "_createClass2", "key", "value", "init", "_this", "Object", "keys", "modules", "for<PERSON>ach", "name", "addModule", "ModuleClass", "constructor", "import", "concat", "_Theme", "_default", "exports"], "sources": ["../../src/core/theme.ts"], "sourcesContent": ["import type Quill from '../core.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type { ToolbarProps } from '../modules/toolbar.js';\nimport type Uploader from '../modules/uploader.js';\n\nexport interface ThemeOptions {\n  modules: Record<string, unknown> & {\n    toolbar?: null | ToolbarProps;\n  };\n}\n\nclass Theme {\n  static DEFAULTS: ThemeOptions = {\n    modules: {},\n  };\n\n  static themes = {\n    default: Theme,\n  };\n\n  modules: ThemeOptions['modules'] = {};\n\n  constructor(\n    protected quill: Quill,\n    protected options: ThemeOptions,\n  ) {}\n\n  init() {\n    Object.keys(this.options.modules).forEach((name) => {\n      if (this.modules[name] == null) {\n        this.addModule(name);\n      }\n    });\n  }\n\n  addModule(name: 'clipboard'): Clipboard;\n  addModule(name: 'keyboard'): Keyboard;\n  addModule(name: 'uploader'): Uploader;\n  addModule(name: 'history'): History;\n  addModule(name: string): unknown;\n  addModule(name: string) {\n    // @ts-expect-error\n    const ModuleClass = this.quill.constructor.import(`modules/${name}`);\n    this.modules[name] = new ModuleClass(\n      this.quill,\n      this.options.modules[name] || {},\n    );\n    return this.modules[name];\n  }\n}\n\nexport interface ThemeConstructor {\n  new (quill: Quill, options: unknown): Theme;\n  DEFAULTS: ThemeOptions;\n}\n\nexport default Theme;\n"], "mappings": ";;;;;;;;;;;;;;;;IAaMA,KAAK;EAWT,SAAAA,MACYC,KAAY,EACZC,OAAqB,EAC/B;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,KAAA;IAAA,IAAAK,gBAAA,CAAAD,OAAA,mBALiC,CAAC,CAAC;IAKnC,KAFUH,KAAY,GAAZA,KAAY;IAAA,KACZC,OAAqB,GAArBA,OAAqB;EAC9B;EAAA,WAAAI,aAAA,CAAAF,OAAA,EAAAJ,KAAA;IAAAO,GAAA;IAAAC,KAAA,EAEH,SAAAC,IAAIA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACLC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACV,OAAO,CAACW,OAAO,CAAC,CAACC,OAAO,CAAE,UAAAC,IAAI,EAAK;QAClD,IAAIL,KAAI,CAACG,OAAO,CAACE,IAAI,CAAC,IAAI,IAAI,EAAE;UAC9BL,KAAI,CAACM,SAAS,CAACD,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAOA,SAAAQ,SAASA,CAACD,IAAY,EAAE;MACtB;MACA,IAAME,WAAW,GAAG,IAAI,CAAChB,KAAK,CAACiB,WAAW,CAACC,MAAM,YAAAC,MAAA,CAAYL,IAAK,CAAC,CAAC;MACpE,IAAI,CAACF,OAAO,CAACE,IAAI,CAAC,GAAG,IAAIE,WAAW,CAClC,IAAI,CAAChB,KAAK,EACV,IAAI,CAACC,OAAO,CAACW,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;MACD,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI,CAAC;IAC3B;EAAA;AAAA;AAAAM,MAAA,GArCIrB,KAAK;AAAA,IAAAK,gBAAA,CAAAD,OAAA,EAALJ,KAAK,cACuB;EAC9Ba,OAAO,EAAE,CAAC;AACZ,CAAC;AAAA,IAAAR,gBAAA,CAAAD,OAAA,EAHGJ,KAAK,YAKO;EACdI,OAAO,EAAEJ;AACX,CAAC;AAAA,IAAAsB,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GAsCYJ,KAAK", "ignoreList": []}]}