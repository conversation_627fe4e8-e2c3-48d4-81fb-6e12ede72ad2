{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\router\\datasearch.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\router\\datasearch.js", "mtime": 1750054784417}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVXaWxkY2FyZC5qcyIpKTsKdmFyIF9sYXlvdXQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvbGF5b3V0IikpOwp2YXIgZGF0YXNlYXJjaFJvdXRlciA9IHsKICBwYXRoOiAnL2RhdGFzZWFyY2gnLAogIGNvbXBvbmVudDogX2xheW91dC5kZWZhdWx0LAogIHJlZGlyZWN0OiAnL2RhdGFzZWFyY2gvaG9tZScsCiAgbmFtZTogJ0RhdGFTZWFyY2gnLAogIG1ldGE6IHsKICAgIHRpdGxlOiAn5pWw5o2u5pCc57SiJywKICAgIGljb246ICdzZWFyY2gnCiAgfSwKICBjaGlsZHJlbjogW3sKICAgIHBhdGg6ICdob21lJywKICAgIGNvbXBvbmVudDogZnVuY3Rpb24gY29tcG9uZW50KCkgewogICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZDIuZGVmYXVsdCkocmVxdWlyZSgnQC92aWV3cy9kYXRhc2VhcmNoL2hvbWUnKSk7CiAgICAgIH0pOwogICAgfSwKICAgIG5hbWU6ICdEYXRhU2VhcmNoSG9tZScsCiAgICBtZXRhOiB7CiAgICAgIHRpdGxlOiAn5pWw5o2u5pCc57Si6aaW6aG1JywKICAgICAgaWNvbjogJ3NlYXJjaCcsCiAgICAgIGFmZml4OiBmYWxzZQogICAgfQogIH0sIHsKICAgIHBhdGg6ICdpbmRleCcsCiAgICBjb21wb25lbnQ6IGZ1bmN0aW9uIGNvbXBvbmVudCgpIHsKICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQyLmRlZmF1bHQpKHJlcXVpcmUoJ0Avdmlld3MvZGF0YXNlYXJjaC9pbmRleCcpKTsKICAgICAgfSk7CiAgICB9LAogICAgbmFtZTogJ0RhdGFTZWFyY2hJbmRleCcsCiAgICBoaWRkZW46IHRydWUsCiAgICAvLyDpmpDol4/mraTot6/nlLHvvIzkuI3lnKjoj5zljZXkuK3mmL7npLoKICAgIG1ldGE6IHsKICAgICAgdGl0bGU6ICfmkJzntKLnu5PmnpwnLAogICAgICBpY29uOiAnc2VhcmNoJywKICAgICAgYWZmaXg6IGZhbHNlCiAgICB9CiAgfV0KfTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gZGF0YXNlYXJjaFJvdXRlcjs="}, {"version": 3, "names": ["_layout", "_interopRequireDefault", "require", "datasearchRouter", "path", "component", "Layout", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "affix", "hidden", "_default", "exports"], "sources": ["D:/jgst/dataeyeui/src/router/datasearch.js"], "sourcesContent": ["import Layout from '@/layout'\n\nconst datasearchRouter = {\n  path: '/datasearch',\n  component: Layout,\n  redirect: '/datasearch/home',\n  name: 'DataSearch',\n  meta: {\n    title: '数据搜索',\n    icon: 'search'\n  },\n  children: [\n    {\n      path: 'home',\n      component: () => import('@/views/datasearch/home'),\n      name: 'DataSearchHome',\n      meta: {\n        title: '数据搜索首页',\n        icon: 'search',\n        affix: false\n      }\n    },\n    {\n      path: 'index',\n      component: () => import('@/views/datasearch/index'),\n      name: 'DataSearchIndex',\n      hidden: true,  // 隐藏此路由，不在菜单中显示\n      meta: {\n        title: '搜索结果',\n        icon: 'search',\n        affix: false\n      }\n    }\n  ]\n}\n\nexport default datasearchRouter\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,gBAAgB,GAAG;EACvBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEC,eAAM;EACjBC,QAAQ,EAAE,kBAAkB;EAC5BC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDM,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MACJC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,QAAQ;MACdO,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEd,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDM,IAAI,EAAE,iBAAiB;IACvBW,MAAM,EAAE,IAAI;IAAG;IACfV,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,QAAQ;MACdO,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAJ,OAAA,GAEcd,gBAAgB", "ignoreList": []}]}