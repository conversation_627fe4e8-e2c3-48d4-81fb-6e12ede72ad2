{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\store\\modules\\permission.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\store\\modules\\permission.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "_interopRequireDefault", "require", "_router", "_interopRequireWildcard3", "_menu", "_index", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_InnerLink", "permission", "state", "routes", "addRoutes", "defaultRoutes", "topbarRouters", "sidebarRouters", "mutations", "SET_ROUTES", "constantRoutes", "concat", "SET_DEFAULT_ROUTES", "SET_TOPBAR_ROUTES", "SET_SIDEBAR_ROUTERS", "actions", "GenerateRoutes", "_ref", "commit", "Promise", "resolve", "getRouters", "then", "res", "sdata", "JSON", "parse", "stringify", "data", "rdata", "sidebarRoutes", "filterAsyncRouter", "rewriteRoutes", "asyncRoutes", "filterDynamicRoutes", "dynamicRoutes", "push", "path", "redirect", "hidden", "router", "asyncRouterMap", "lastRouter", "arguments", "length", "undefined", "type", "filter", "route", "children", "filterChildren", "component", "Layout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "InnerLink", "loadView", "childrenMap", "for<PERSON>ach", "el", "index", "c", "permissions", "auth", "hasPermiOr", "roles", "hasRoleOr", "exports", "view", "process", "env", "NODE_ENV", "specifier", "r", "s", "_interopRequireWildcard2", "default", "_default"], "sources": ["D:/jgst/dataeyeui/src/store/modules/permission.js"], "sourcesContent": ["import auth from '@/plugins/auth'\nimport router, { constantRoutes, dynamicRoutes } from '@/router'\nimport { getRouters } from '@/api/menu'\nimport Layout from '@/layout/index'\nimport ParentView from '@/components/ParentView'\nimport InnerLink from '@/layout/components/InnerLink'\n\nconst permission = {\n  state: {\n    routes: [],\n    addRoutes: [],\n    defaultRoutes: [],\n    topbarRouters: [],\n    sidebarRouters: []\n  },\n  mutations: {\n    SET_ROUTES: (state, routes) => {\n      state.addRoutes = routes\n      state.routes = constantRoutes.concat(routes)\n    },\n    SET_DEFAULT_ROUTES: (state, routes) => {\n      state.defaultRoutes = constantRoutes.concat(routes)\n    },\n    SET_TOPBAR_ROUTES: (state, routes) => {\n      state.topbarRouters = routes\n    },\n    SET_SIDEBAR_ROUTERS: (state, routes) => {\n      state.sidebarRouters = routes\n    },\n  },\n  actions: {\n    // 生成路由\n    GenerateRoutes({ commit }) {\n      return new Promise(resolve => {\n        // 向后端请求路由数据\n        getRouters().then(res => {\n          const sdata = JSON.parse(JSON.stringify(res.data))\n          const rdata = JSON.parse(JSON.stringify(res.data))\n          const sidebarRoutes = filterAsyncRouter(sdata)\n          const rewriteRoutes = filterAsyncRouter(rdata, false, true)\n          const asyncRoutes = filterDynamicRoutes(dynamicRoutes);\n          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })\n          router.addRoutes(asyncRoutes);\n          commit('SET_ROUTES', rewriteRoutes)\n          commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))\n          commit('SET_DEFAULT_ROUTES', sidebarRoutes)\n          commit('SET_TOPBAR_ROUTES', sidebarRoutes)\n          resolve(rewriteRoutes)\n        })\n      })\n    }\n  }\n}\n\n// 遍历后台传来的路由字符串，转换为组件对象\nfunction filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {\n  return asyncRouterMap.filter(route => {\n    if (type && route.children) {\n      route.children = filterChildren(route.children)\n    }\n    if (route.component) {\n      // Layout ParentView 组件特殊处理\n      if (route.component === 'Layout') {\n        route.component = Layout\n      } else if (route.component === 'ParentView') {\n        route.component = ParentView\n      } else if (route.component === 'InnerLink') {\n        route.component = InnerLink\n      } else {\n        route.component = loadView(route.component)\n      }\n    }\n    if (route.children != null && route.children && route.children.length) {\n      route.children = filterAsyncRouter(route.children, route, type)\n    } else {\n      delete route['children']\n      delete route['redirect']\n    }\n    return true\n  })\n}\n\nfunction filterChildren(childrenMap, lastRouter = false) {\n  var children = []\n  childrenMap.forEach((el, index) => {\n    if (el.children && el.children.length) {\n      if (el.component === 'ParentView' && !lastRouter) {\n        el.children.forEach(c => {\n          c.path = el.path + '/' + c.path\n          if (c.children && c.children.length) {\n            children = children.concat(filterChildren(c.children, c))\n            return\n          }\n          children.push(c)\n        })\n        return\n      }\n    }\n    if (lastRouter) {\n      el.path = lastRouter.path + '/' + el.path\n      if (el.children && el.children.length) {\n        children = children.concat(filterChildren(el.children, el))\n        return\n      }\n    }\n    children = children.concat(el)\n  })\n  return children\n}\n\n// 动态路由遍历，验证是否具备权限\nexport function filterDynamicRoutes(routes) {\n  const res = []\n  routes.forEach(route => {\n    if (route.permissions) {\n      if (auth.hasPermiOr(route.permissions)) {\n        res.push(route)\n      }\n    } else if (route.roles) {\n      if (auth.hasRoleOr(route.roles)) {\n        res.push(route)\n      }\n    }\n  })\n  return res\n}\n\nexport const loadView = (view) => {\n  if (process.env.NODE_ENV === 'development') {\n    return (resolve) => require([`@/views/${view}`], resolve)\n  } else {\n    // 使用 import 实现生产环境的路由懒加载\n    return () => import(`@/views/${view}`)\n  }\n}\n\nexport default permission\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,wBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AAEA,IAAMO,UAAU,GAAG;EACjBC,KAAK,EAAE;IACLC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACTC,UAAU,EAAE,SAAZA,UAAUA,CAAGP,KAAK,EAAEC,MAAM,EAAK;MAC7BD,KAAK,CAACE,SAAS,GAAGD,MAAM;MACxBD,KAAK,CAACC,MAAM,GAAGO,sBAAc,CAACC,MAAM,CAACR,MAAM,CAAC;IAC9C,CAAC;IACDS,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGV,KAAK,EAAEC,MAAM,EAAK;MACrCD,KAAK,CAACG,aAAa,GAAGK,sBAAc,CAACC,MAAM,CAACR,MAAM,CAAC;IACrD,CAAC;IACDU,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGX,KAAK,EAAEC,MAAM,EAAK;MACpCD,KAAK,CAACI,aAAa,GAAGH,MAAM;IAC9B,CAAC;IACDW,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGZ,KAAK,EAAEC,MAAM,EAAK;MACtCD,KAAK,CAACK,cAAc,GAAGJ,MAAM;IAC/B;EACF,CAAC;EACDY,OAAO,EAAE;IACP;IACAC,cAAc,WAAdA,cAAcA,CAAAC,IAAA,EAAa;MAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;MACrB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5B;QACA,IAAAC,gBAAU,EAAC,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG,EAAI;UACvB,IAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC;UAClD,IAAMC,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC;UAClD,IAAME,aAAa,GAAGC,iBAAiB,CAACP,KAAK,CAAC;UAC9C,IAAMQ,aAAa,GAAGD,iBAAiB,CAACF,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;UAC3D,IAAMI,WAAW,GAAGC,mBAAmB,CAACC,qBAAa,CAAC;UACtDH,aAAa,CAACI,IAAI,CAAC;YAAEC,IAAI,EAAE,GAAG;YAAEC,QAAQ,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC;UACjEC,eAAM,CAACpC,SAAS,CAAC6B,WAAW,CAAC;UAC7Bf,MAAM,CAAC,YAAY,EAAEc,aAAa,CAAC;UACnCd,MAAM,CAAC,qBAAqB,EAAER,sBAAc,CAACC,MAAM,CAACmB,aAAa,CAAC,CAAC;UACnEZ,MAAM,CAAC,oBAAoB,EAAEY,aAAa,CAAC;UAC3CZ,MAAM,CAAC,mBAAmB,EAAEY,aAAa,CAAC;UAC1CV,OAAO,CAACY,aAAa,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;AACF,CAAC;;AAED;AACA,SAASD,iBAAiBA,CAACU,cAAc,EAAoC;EAAA,IAAlCC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAAEG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACzE,OAAOF,cAAc,CAACM,MAAM,CAAC,UAAAC,KAAK,EAAI;IACpC,IAAIF,IAAI,IAAIE,KAAK,CAACC,QAAQ,EAAE;MAC1BD,KAAK,CAACC,QAAQ,GAAGC,cAAc,CAACF,KAAK,CAACC,QAAQ,CAAC;IACjD;IACA,IAAID,KAAK,CAACG,SAAS,EAAE;MACnB;MACA,IAAIH,KAAK,CAACG,SAAS,KAAK,QAAQ,EAAE;QAChCH,KAAK,CAACG,SAAS,GAAGC,cAAM;MAC1B,CAAC,MAAM,IAAIJ,KAAK,CAACG,SAAS,KAAK,YAAY,EAAE;QAC3CH,KAAK,CAACG,SAAS,GAAGE,mBAAU;MAC9B,CAAC,MAAM,IAAIL,KAAK,CAACG,SAAS,KAAK,WAAW,EAAE;QAC1CH,KAAK,CAACG,SAAS,GAAGG,kBAAS;MAC7B,CAAC,MAAM;QACLN,KAAK,CAACG,SAAS,GAAGI,QAAQ,CAACP,KAAK,CAACG,SAAS,CAAC;MAC7C;IACF;IACA,IAAIH,KAAK,CAACC,QAAQ,IAAI,IAAI,IAAID,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACL,MAAM,EAAE;MACrEI,KAAK,CAACC,QAAQ,GAAGlB,iBAAiB,CAACiB,KAAK,CAACC,QAAQ,EAAED,KAAK,EAAEF,IAAI,CAAC;IACjE,CAAC,MAAM;MACL,OAAOE,KAAK,CAAC,UAAU,CAAC;MACxB,OAAOA,KAAK,CAAC,UAAU,CAAC;IAC1B;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AAEA,SAASE,cAAcA,CAACM,WAAW,EAAsB;EAAA,IAApBd,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrD,IAAIM,QAAQ,GAAG,EAAE;EACjBO,WAAW,CAACC,OAAO,CAAC,UAACC,EAAE,EAAEC,KAAK,EAAK;IACjC,IAAID,EAAE,CAACT,QAAQ,IAAIS,EAAE,CAACT,QAAQ,CAACL,MAAM,EAAE;MACrC,IAAIc,EAAE,CAACP,SAAS,KAAK,YAAY,IAAI,CAACT,UAAU,EAAE;QAChDgB,EAAE,CAACT,QAAQ,CAACQ,OAAO,CAAC,UAAAG,CAAC,EAAI;UACvBA,CAAC,CAACvB,IAAI,GAAGqB,EAAE,CAACrB,IAAI,GAAG,GAAG,GAAGuB,CAAC,CAACvB,IAAI;UAC/B,IAAIuB,CAAC,CAACX,QAAQ,IAAIW,CAAC,CAACX,QAAQ,CAACL,MAAM,EAAE;YACnCK,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACuC,cAAc,CAACU,CAAC,CAACX,QAAQ,EAAEW,CAAC,CAAC,CAAC;YACzD;UACF;UACAX,QAAQ,CAACb,IAAI,CAACwB,CAAC,CAAC;QAClB,CAAC,CAAC;QACF;MACF;IACF;IACA,IAAIlB,UAAU,EAAE;MACdgB,EAAE,CAACrB,IAAI,GAAGK,UAAU,CAACL,IAAI,GAAG,GAAG,GAAGqB,EAAE,CAACrB,IAAI;MACzC,IAAIqB,EAAE,CAACT,QAAQ,IAAIS,EAAE,CAACT,QAAQ,CAACL,MAAM,EAAE;QACrCK,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAACuC,cAAc,CAACQ,EAAE,CAACT,QAAQ,EAAES,EAAE,CAAC,CAAC;QAC3D;MACF;IACF;IACAT,QAAQ,GAAGA,QAAQ,CAACtC,MAAM,CAAC+C,EAAE,CAAC;EAChC,CAAC,CAAC;EACF,OAAOT,QAAQ;AACjB;;AAEA;AACO,SAASf,mBAAmBA,CAAC/B,MAAM,EAAE;EAC1C,IAAMoB,GAAG,GAAG,EAAE;EACdpB,MAAM,CAACsD,OAAO,CAAC,UAAAT,KAAK,EAAI;IACtB,IAAIA,KAAK,CAACa,WAAW,EAAE;MACrB,IAAIC,aAAI,CAACC,UAAU,CAACf,KAAK,CAACa,WAAW,CAAC,EAAE;QACtCtC,GAAG,CAACa,IAAI,CAACY,KAAK,CAAC;MACjB;IACF,CAAC,MAAM,IAAIA,KAAK,CAACgB,KAAK,EAAE;MACtB,IAAIF,aAAI,CAACG,SAAS,CAACjB,KAAK,CAACgB,KAAK,CAAC,EAAE;QAC/BzC,GAAG,CAACa,IAAI,CAACY,KAAK,CAAC;MACjB;IACF;EACF,CAAC,CAAC;EACF,OAAOzB,GAAG;AACZ;AAEO,IAAMgC,QAAQ,GAAAW,OAAA,CAAAX,QAAA,GAAG,SAAXA,QAAQA,CAAIY,IAAI,EAAK;EAChC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,OAAO,UAAClD,OAAO;MAAA,OAAK1B,OAAO,CAAC,YAAAiB,MAAA,CAAYwD,IAAI,EAAG,EAAE/C,OAAO,CAAC;IAAA;EAC3D,CAAC,MAAM;IACL;IACA,OAAO;MAAA,iBAAAmD,SAAA;QAAA,WAAApD,OAAA,WAAAqD,CAAA;UAAA,OAAAA,CAAA,CAAAD,SAAA;QAAA,GAAAjD,IAAA,WAAAmD,CAAA;UAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjF,OAAA,CAAA+E,CAAA;QAAA;MAAA,aAAA9D,MAAA,CAAwBwD,IAAI;IAAA,CAAG;EACxC;AACF,CAAC;AAAA,IAAAS,QAAA,GAAAV,OAAA,CAAAS,OAAA,GAEc1E,UAAU", "ignoreList": []}]}