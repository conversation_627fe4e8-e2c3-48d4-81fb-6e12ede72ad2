{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\my\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\my\\index.vue", "mtime": 1750042562793}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENhcmRMaXN0LCByZW1vdmVDYXJkIH0gZnJvbSAnQC9hcGkvY2FyZCcKaW1wb3J0IHsgZ2V0QWxlcnRMaXN0LCByZW1vdmVBbGVydCB9IGZyb20gJ0AvYXBpL2FsZXJ0JwppbXBvcnQgeyBnZXRRdWVzdGlvblN0YXJMaXN0LCByZW1vdmVRdWVzdGlvblN0YXIgfSBmcm9tICdAL2FwaS9xdWVzdGlvbicKaW1wb3J0IHsgZ2V0SGlzdG9yeUxpc3QsIHJlbW92ZUhpc3RvcnkgfSBmcm9tICdAL2FwaS9oaXN0b3J5JwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNeUNlbnRlcicsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZU1lbnU6ICdhZGRyZXNzJywKICAgICAgc2VhcmNoS2V5d29yZDogJycsCiAgICAgIGNhcmRTZWFyY2hLZXl3b3JkOiAnJywKICAgICAgcXVlc3Rpb25TZWFyY2hLZXl3b3JkOiAnJywKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOaVsOaNruWMugogICAgICBjYXJkTGlzdDogW10sCiAgICAgIGNhcmRTaG93TGlzdDogW10sCiAgICAgIGFsZXJ0TGlzdDogW10sCiAgICAgIHF1ZXN0aW9uTGlzdDogW10sCiAgICAgIGhpc3RvcnlMaXN0OiBbXSwKICAgICAgLy8g5Yqg6L295LiO5byC5bi454q25oCBCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBlcnJvcjogbnVsbAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIG1lbnVDb3VudHMoKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgYWRkcmVzczogdGhpcy5hbGVydExpc3QubGVuZ3RoLAogICAgICAgIGYyY29kZTogdGhpcy5jYXJkU2hvd0xpc3QubGVuZ3RoLAogICAgICAgIHF1ZXN0aW9uczogdGhpcy5xdWVzdGlvbkxpc3QubGVuZ3RoLAogICAgICAgIGVkaXQ6IHRoaXMuY2FyZExpc3QubGVuZ3RoLAogICAgICAgIHRhZ3M6IHRoaXMuaGlzdG9yeUxpc3QubGVuZ3RoCiAgICAgIH0KICAgIH0sCiAgICBmaWx0ZXJlZENhcmRMaXN0KCkgewogICAgICBpZiAoIXRoaXMuY2FyZFNlYXJjaEtleXdvcmQpIHJldHVybiB0aGlzLmNhcmRMaXN0CiAgICAgIHJldHVybiB0aGlzLmNhcmRMaXN0LmZpbHRlcihpdGVtID0+IChpdGVtLm5hbWUgfHwgJycpLmluY2x1ZGVzKHRoaXMuY2FyZFNlYXJjaEtleXdvcmQpKQogICAgfSwKICAgIGZpbHRlcmVkQ2FyZFNob3dMaXN0KCkgewogICAgICBpZiAoIXRoaXMuY2FyZFNlYXJjaEtleXdvcmQpIHJldHVybiB0aGlzLmNhcmRTaG93TGlzdAogICAgICByZXR1cm4gdGhpcy5jYXJkU2hvd0xpc3QuZmlsdGVyKGl0ZW0gPT4gKGl0ZW0ubmFtZSB8fCAnJykuaW5jbHVkZXModGhpcy5jYXJkU2VhcmNoS2V5d29yZCkpCiAgICB9LAogICAgZmlsdGVyZWRRdWVzdGlvbkxpc3QoKSB7CiAgICAgIGlmICghdGhpcy5xdWVzdGlvblNlYXJjaEtleXdvcmQpIHJldHVybiB0aGlzLnF1ZXN0aW9uTGlzdAogICAgICByZXR1cm4gdGhpcy5xdWVzdGlvbkxpc3QuZmlsdGVyKGl0ZW0gPT4gKGl0ZW0ucXVlc3Rpb24gfHwgJycpLmluY2x1ZGVzKHRoaXMucXVlc3Rpb25TZWFyY2hLZXl3b3JkKSkKICAgIH0sCiAgICBmaWx0ZXJlZEhpc3RvcnlMaXN0KCkgewogICAgICBpZiAoIXRoaXMuc2VhcmNoS2V5d29yZCkgcmV0dXJuIHRoaXMuaGlzdG9yeUxpc3QKICAgICAgcmV0dXJuIHRoaXMuaGlzdG9yeUxpc3QuZmlsdGVyKGl0ZW0gPT4gKGl0ZW0uY29udGVudCB8fCAnJykuaW5jbHVkZXModGhpcy5zZWFyY2hLZXl3b3JkKSkKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmZldGNoQWxsRGF0YSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBhc3luYyBmZXRjaEFsbERhdGEoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgdGhpcy5lcnJvciA9IG51bGwKICAgICAgdHJ5IHsKICAgICAgICAvLyDmjIfmoIflkYroraYKICAgICAgICBjb25zdCBhbGVydFJlcyA9IGF3YWl0IGdldEFsZXJ0TGlzdCh7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAgfSkKICAgICAgICB0aGlzLmFsZXJ0TGlzdCA9IGFsZXJ0UmVzLml0ZW1zIHx8IGFsZXJ0UmVzLnJvd3MgfHwgW10KICAgICAgICAvLyDljaHniYfmj5DphpIv5L+d5a2Y55qE5Y2h54mHCiAgICAgICAgLy8g5q2k5aSEY2FyZExpc3TljbPkuLrmlbDmja7lupNjYXJkc+ihqOeahOaVsOaNrgogICAgICAgIGNvbnN0IGNhcmRSZXMgPSBhd2FpdCBnZXRDYXJkTGlzdCh7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAgfSkKICAgICAgICB0aGlzLmNhcmRMaXN0ID0gY2FyZFJlcy5pdGVtcyB8fCBjYXJkUmVzLnJvd3MgfHwgW10KICAgICAgICB0aGlzLmNhcmRTaG93TGlzdCA9IHRoaXMuY2FyZExpc3QgLy8g6KeG5Zu+5YiG56a75ZCO5Y+v5YGa562b6YCJCiAgICAgICAgLy8g5YWz5rOo55qE6Zeu6aKYCiAgICAgICAgY29uc3QgcXVlc3Rpb25SZXMgPSBhd2FpdCBnZXRRdWVzdGlvblN0YXJMaXN0KHsgcGFnZU51bTogMSwgcGFnZVNpemU6IDEwMCB9KQogICAgICAgIHRoaXMucXVlc3Rpb25MaXN0ID0gcXVlc3Rpb25SZXMuaXRlbXMgfHwgcXVlc3Rpb25SZXMucm93cyB8fCBbXQogICAgICAgIC8vIOaQnOe0ouWOhuWPsgogICAgICAgIGNvbnN0IGhpc3RvcnlSZXMgPSBhd2FpdCBnZXRIaXN0b3J5TGlzdCh7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAgfSkKICAgICAgICB0aGlzLmhpc3RvcnlMaXN0ID0gaGlzdG9yeVJlcy5pdGVtcyB8fCBoaXN0b3J5UmVzLnJvd3MgfHwgW10KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuZXJyb3IgPSBlLm1lc3NhZ2UgfHwgJ+aVsOaNruWKoOi9veWksei0pScKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbAogICAgICB0aGlzLmZldGNoQWxsRGF0YSgpCiAgICB9LAogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IHZhbAogICAgICB0aGlzLmZldGNoQWxsRGF0YSgpCiAgICB9LAogICAgYXN5bmMgcmVtb3ZlQ2FyZFJvdyhyb3cpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIGF3YWl0IHJlbW92ZUNhcmQocm93LmlkIHx8IHJvdy5jYXJkSWQpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgIHRoaXMuZmV0Y2hBbGxEYXRhKCkKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZS5tZXNzYWdlIHx8ICfliKDpmaTlpLHotKUnKQogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0KICAgIH0sCiAgICBhc3luYyByZW1vdmVBbGVydFJvdyhyb3cpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIGF3YWl0IHJlbW92ZUFsZXJ0KHJvdy5pZCB8fCByb3cuYWxlcnRJZCkKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpCiAgICAgICAgdGhpcy5mZXRjaEFsbERhdGEoKQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlLm1lc3NhZ2UgfHwgJ+WIoOmZpOWksei0pScpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIGFzeW5jIHJlbW92ZVF1ZXN0aW9uUm93KHJvdykgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIHRyeSB7CiAgICAgICAgYXdhaXQgcmVtb3ZlUXVlc3Rpb25TdGFyKHJvdy5pZCB8fCByb3cuc3RhcklkKQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Y+W5raI5YWz5rOo5oiQ5YqfJykKICAgICAgICB0aGlzLmZldGNoQWxsRGF0YSgpCiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGUubWVzc2FnZSB8fCAn5pON5L2c5aSx6LSlJykKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgYXN5bmMgcmVtb3ZlSGlzdG9yeVJvdyhyb3cpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIGF3YWl0IHJlbW92ZUhpc3Rvcnkocm93LmlkIHx8IHJvdy5oaXN0b3J5SWQpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgIHRoaXMuZmV0Y2hBbGxEYXRhKCkKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZS5tZXNzYWdlIHx8ICfliKDpmaTlpLHotKUnKQogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgIH0KICAgIH0KICB9LAo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiJA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/my", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"my-container\">\n      <!-- 左侧菜单 -->\n      <div class=\"left-menu\">\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'address' }\" @click=\"activeMenu = 'address'\">\n          <i class=\"el-icon-data-analysis\"></i>\n          <span>指标告警({{ menuCounts.address }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'f2code' }\" @click=\"activeMenu = 'f2code'\">\n          <i class=\"el-icon-document\"></i>\n          <span>卡片提醒({{ menuCounts.f2code }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'questions' }\" @click=\"activeMenu = 'questions'\">\n          <i class=\"el-icon-question\"></i>\n          <span>关注的问题({{ menuCounts.questions }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'edit' }\" @click=\"activeMenu = 'edit'\">\n          <i class=\"el-icon-star-off\"></i>\n          <span>保存的卡片({{ menuCounts.edit }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'tags' }\" @click=\"activeMenu = 'tags'\">\n          <i class=\"el-icon-time\"></i>\n          <span>搜索历史({{ menuCounts.tags }})</span>\n        </div>\n      </div>\n\n      <!-- 右侧内容区 -->\n      <div class=\"content-area\">\n        <el-skeleton v-if=\"loading\" rows=\"6\" animated />\n        <el-alert v-if=\"error\" :title=\"error\" type=\"error\" show-icon style=\"margin-bottom:16px\" />\n\n        <!-- 指标告警内容 -->\n        <template v-if=\"activeMenu === 'address' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">指标告警</div>\n          </div>\n          <el-table :data=\"alertList\" style=\"width:100%\" border v-if=\"alertList.length\">\n            <el-table-column prop=\"name\" label=\"告警名称\" min-width=\"180\" />\n            <el-table-column prop=\"level\" label=\"级别\" width=\"100\" />\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeAlertRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无数据</div></div>\n        </template>\n\n        <!-- 卡片提醒内容 -->\n        <template v-if=\"activeMenu === 'f2code' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">卡片记录</div>\n          </div>\n          <el-table :data=\"filteredCardShowList\" style=\"width:100%\" border v-if=\"filteredCardShowList.length\">\n            <el-table-column prop=\"name\" label=\"监控卡片\" min-width=\"180\" />\n            <el-table-column prop=\"createTime\" label=\"触发条件\" width=\"180\" />\n            <el-table-column prop=\"updateTime\" label=\"计算频率\" width=\"180\" />\n            <el-table-column prop=\"creator\" label=\"提醒接受人\" width=\"120\" />\n            <el-table-column prop=\"lastViewTime\" label=\"最近触发时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeCardRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无数据</div></div>\n        </template>\n\n        <!-- 关注的问题内容 -->\n        <template v-if=\"activeMenu === 'questions' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">关注的问题</div>\n            <div class=\"search-box\">\n              <el-input placeholder=\"在关注的问题中查找\" prefix-icon=\"el-icon-search\" v-model=\"questionSearchKeyword\" clearable />\n            </div>\n          </div>\n          <el-table :data=\"filteredQuestionList\" style=\"width:100%\" border v-if=\"filteredQuestionList.length\">\n            <el-table-column prop=\"question\" label=\"问题\" min-width=\"200\" />\n            <el-table-column prop=\"starTime\" label=\"关注时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeQuestionRow(scope.row)\">取消关注</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无数据</div></div>\n        </template>\n\n        <!-- 保存的卡片内容 -->\n        <template v-if=\"activeMenu === 'edit' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">保存的卡片</div>\n            <div class=\"search-box\">\n              <el-input placeholder=\"在保存的卡片中查找\" prefix-icon=\"el-icon-search\" v-model=\"cardSearchKeyword\" clearable />\n            </div>\n          </div>\n          <el-table :data=\"filteredCardList\" style=\"width:100%\" border v-if=\"filteredCardList.length\">\n            <el-table-column prop=\"name\" label=\"卡片名称\" min-width=\"180\" />\n            <el-table-column prop=\"createTime\" label=\"保存时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeCardRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂未保存任何卡片</div></div>\n        </template>\n\n        <!-- 搜索历史内容 -->\n        <template v-if=\"activeMenu === 'tags' && !loading && !error\">\n          <div class=\"search-history-header-new\">\n            <div class=\"search-history-title\">搜索历史 <span class=\"desc\">保留最近1个月的记录</span></div>\n            <el-input class=\"search-history-input\" placeholder=\"在搜索历史中检索\" prefix-icon=\"el-icon-search\" v-model=\"searchKeyword\" clearable />\n          </div>\n          <el-table :data=\"filteredHistoryList\" class=\"search-history-table\" style=\"width:100%\" border v-if=\"filteredHistoryList.length\">\n            <el-table-column label=\"全部\" min-width=\"70%\">\n              <template slot-scope=\"scope\">\n                <div class=\"history-item\">{{ scope.row.content }}</div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"修改时间\" align=\"right\" min-width=\"30%\">\n              <template slot-scope=\"scope\">\n                <div class=\"history-time\">{{ scope.row.time }}</div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\" @click=\"removeHistoryRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无历史记录</div></div>\n        </template>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCardList, removeCard } from '@/api/card'\nimport { getAlertList, removeAlert } from '@/api/alert'\nimport { getQuestionStarList, removeQuestionStar } from '@/api/question'\nimport { getHistoryList, removeHistory } from '@/api/history'\n\nexport default {\n  name: 'MyCenter',\n  data() {\n    return {\n      activeMenu: 'address',\n      searchKeyword: '',\n      cardSearchKeyword: '',\n      questionSearchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      // 数据区\n      cardList: [],\n      cardShowList: [],\n      alertList: [],\n      questionList: [],\n      historyList: [],\n      // 加载与异常状态\n      loading: false,\n      error: null\n    }\n  },\n  computed: {\n    menuCounts() {\n      return {\n        address: this.alertList.length,\n        f2code: this.cardShowList.length,\n        questions: this.questionList.length,\n        edit: this.cardList.length,\n        tags: this.historyList.length\n      }\n    },\n    filteredCardList() {\n      if (!this.cardSearchKeyword) return this.cardList\n      return this.cardList.filter(item => (item.name || '').includes(this.cardSearchKeyword))\n    },\n    filteredCardShowList() {\n      if (!this.cardSearchKeyword) return this.cardShowList\n      return this.cardShowList.filter(item => (item.name || '').includes(this.cardSearchKeyword))\n    },\n    filteredQuestionList() {\n      if (!this.questionSearchKeyword) return this.questionList\n      return this.questionList.filter(item => (item.question || '').includes(this.questionSearchKeyword))\n    },\n    filteredHistoryList() {\n      if (!this.searchKeyword) return this.historyList\n      return this.historyList.filter(item => (item.content || '').includes(this.searchKeyword))\n    }\n  },\n  created() {\n    this.fetchAllData()\n  },\n  methods: {\n    async fetchAllData() {\n      this.loading = true\n      this.error = null\n      try {\n        // 指标告警\n        const alertRes = await getAlertList({ pageNum: 1, pageSize: 100 })\n        this.alertList = alertRes.items || alertRes.rows || []\n        // 卡片提醒/保存的卡片\n        // 此处cardList即为数据库cards表的数据\n        const cardRes = await getCardList({ pageNum: 1, pageSize: 100 })\n        this.cardList = cardRes.items || cardRes.rows || []\n        this.cardShowList = this.cardList // 视图分离后可做筛选\n        // 关注的问题\n        const questionRes = await getQuestionStarList({ pageNum: 1, pageSize: 100 })\n        this.questionList = questionRes.items || questionRes.rows || []\n        // 搜索历史\n        const historyRes = await getHistoryList({ pageNum: 1, pageSize: 100 })\n        this.historyList = historyRes.items || historyRes.rows || []\n      } catch (e) {\n        this.error = e.message || '数据加载失败'\n      } finally {\n        this.loading = false\n      }\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.fetchAllData()\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.fetchAllData()\n    },\n    async removeCardRow(row) {\n      this.loading = true\n      try {\n        await removeCard(row.id || row.cardId)\n        this.$message.success('删除成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '删除失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async removeAlertRow(row) {\n      this.loading = true\n      try {\n        await removeAlert(row.id || row.alertId)\n        this.$message.success('删除成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '删除失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async removeQuestionRow(row) {\n      this.loading = true\n      try {\n        await removeQuestionStar(row.id || row.starId)\n        this.$message.success('取消关注成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '操作失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async removeHistoryRow(row) {\n      this.loading = true\n      try {\n        await removeHistory(row.id || row.historyId)\n        this.$message.success('删除成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '删除失败')\n      } finally {\n        this.loading = false\n      }\n    }\n  },\n</script>\n\n<style scoped>\n.app-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.my-container {\n  display: flex;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  min-height: calc(100vh - 40px);\n}\n\n/* 左侧菜单样式 */\n.left-menu {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  padding: 20px 0;\n}\n\n.menu-item {\n  padding: 12px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  color: #606266;\n  transition: all 0.3s;\n}\n\n.menu-item:hover {\n  background-color: #f5f7fa;\n}\n\n.menu-item.active {\n  color: #409EFF;\n  background-color: #ecf5ff;\n}\n\n.menu-item i {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n/* 右侧内容区样式 */\n.content-area {\n  flex: 1;\n  padding: 20px;\n  position: relative;\n}\n\n.header {\n  margin-bottom: 20px;\n}\n\n.add-button {\n  border-radius: 20px;\n  padding: 8px 20px;\n}\n\n.empty-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  color: #909399;\n}\n\n.empty-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n}\n\n.empty-icon i {\n  font-size: 24px;\n  color: #909399;\n}\n\n.empty-text {\n  font-size: 14px;\n}\n\n/* 搜索历史样式 */\n.search-history-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.username {\n  font-size: 16px;\n  font-weight: bold;\n  margin-right: 5px;\n}\n\n.user-desc {\n  font-size: 14px;\n  color: #909399;\n}\n\n.search-box {\n  width: 300px;\n}\n\n.search-history-list {\n  margin-bottom: 20px;\n}\n\n.history-item {\n  font-size: 14px;\n  color: #303133;\n  line-height: 1.5;\n  padding: 10px 0;\n}\n\n.history-time {\n  font-size: 14px;\n  color: #909399;\n  padding: 10px 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20px;\n}\n\n/* 保存的卡片样式 */\n.saved-cards-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.card-table-container {\n  position: relative;\n  min-height: 300px;\n}\n\n.empty-table {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  text-align: center;\n  color: #909399;\n  font-size: 14px;\n  padding: 20px 0;\n}\n\n.link-text {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.link-text:hover {\n  text-decoration: underline;\n}\n\n/* 新搜索历史样式 */\n.search-history-header-new {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.search-history-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.desc {\n  font-size: 14px;\n  color: #909399;\n  margin-left: 5px;\n}\n\n.search-history-input {\n  width: 300px;\n}\n\n.search-history-table {\n  margin-top: 10px;\n}\n\n.search-history-table .el-table__header {\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.search-history-table .el-table__header th {\n  font-size: 14px;\n  color: #606266;\n  padding: 12px 0;\n  text-align: left;\n}\n\n.search-history-table .el-table__body td {\n  font-size: 14px;\n  color: #303133;\n  padding: 10px 0;\n}\n\n.search-history-table .el-table__body tr:hover {\n  background-color: #f5f7fa;\n}\n</style>\n"]}]}