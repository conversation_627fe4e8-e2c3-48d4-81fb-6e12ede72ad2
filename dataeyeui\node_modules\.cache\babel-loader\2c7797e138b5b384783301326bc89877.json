{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\ui\\picker.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\ui\\picker.js", "mtime": 1749172158825}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["optionsCounter", "toggleAriaAttribute", "element", "attribute", "setAttribute", "concat", "getAttribute", "Picker", "select", "_this", "_classCallCheck2", "default", "container", "document", "createElement", "buildPicker", "style", "display", "parentNode", "insertBefore", "label", "addEventListener", "togglePicker", "event", "key", "escape", "preventDefault", "update", "bind", "_createClass2", "value", "classList", "toggle", "options", "buildItem", "option", "_this2", "item", "tabIndex", "add", "textContent", "selectItem", "buildLabel", "innerHTML", "DropdownIcon", "append<PERSON><PERSON><PERSON>", "buildOptions", "_this3", "id", "Array", "from", "for<PERSON>ach", "selected", "_this4", "attributes", "name", "_this5", "close", "setTimeout", "focus", "remove", "trigger", "arguments", "length", "undefined", "querySelector", "selectedIndex", "children", "indexOf", "hasAttribute", "removeAttribute", "dispatchEvent", "Event", "isActive", "_default", "exports"], "sources": ["../../src/ui/picker.ts"], "sourcesContent": ["import DropdownIcon from '../assets/icons/dropdown.svg';\n\nlet optionsCounter = 0;\n\nfunction toggleAriaAttribute(element: HTMLElement, attribute: string) {\n  element.setAttribute(\n    attribute,\n    `${!(element.getAttribute(attribute) === 'true')}`,\n  );\n}\n\nclass Picker {\n  select: HTMLSelectElement;\n  container: HTMLElement;\n  label: HTMLElement;\n\n  constructor(select: HTMLSelectElement) {\n    this.select = select;\n    this.container = document.createElement('span');\n    this.buildPicker();\n    this.select.style.display = 'none';\n    // @ts-expect-error Fix me later\n    this.select.parentNode.insertBefore(this.container, this.select);\n\n    this.label.addEventListener('mousedown', () => {\n      this.togglePicker();\n    });\n    this.label.addEventListener('keydown', (event) => {\n      switch (event.key) {\n        case 'Enter':\n          this.togglePicker();\n          break;\n        case 'Escape':\n          this.escape();\n          event.preventDefault();\n          break;\n        default:\n      }\n    });\n    this.select.addEventListener('change', this.update.bind(this));\n  }\n\n  togglePicker() {\n    this.container.classList.toggle('ql-expanded');\n    // Toggle aria-expanded and aria-hidden to make the picker accessible\n    toggleAriaAttribute(this.label, 'aria-expanded');\n    // @ts-expect-error\n    toggleAriaAttribute(this.options, 'aria-hidden');\n  }\n\n  buildItem(option: HTMLOptionElement) {\n    const item = document.createElement('span');\n    // @ts-expect-error\n    item.tabIndex = '0';\n    item.setAttribute('role', 'button');\n    item.classList.add('ql-picker-item');\n    const value = option.getAttribute('value');\n    if (value) {\n      item.setAttribute('data-value', value);\n    }\n    if (option.textContent) {\n      item.setAttribute('data-label', option.textContent);\n    }\n    item.addEventListener('click', () => {\n      this.selectItem(item, true);\n    });\n    item.addEventListener('keydown', (event) => {\n      switch (event.key) {\n        case 'Enter':\n          this.selectItem(item, true);\n          event.preventDefault();\n          break;\n        case 'Escape':\n          this.escape();\n          event.preventDefault();\n          break;\n        default:\n      }\n    });\n\n    return item;\n  }\n\n  buildLabel() {\n    const label = document.createElement('span');\n    label.classList.add('ql-picker-label');\n    label.innerHTML = DropdownIcon;\n    // @ts-expect-error\n    label.tabIndex = '0';\n    label.setAttribute('role', 'button');\n    label.setAttribute('aria-expanded', 'false');\n    this.container.appendChild(label);\n    return label;\n  }\n\n  buildOptions() {\n    const options = document.createElement('span');\n    options.classList.add('ql-picker-options');\n\n    // Don't want screen readers to read this until options are visible\n    options.setAttribute('aria-hidden', 'true');\n    // @ts-expect-error\n    options.tabIndex = '-1';\n\n    // Need a unique id for aria-controls\n    options.id = `ql-picker-options-${optionsCounter}`;\n    optionsCounter += 1;\n    this.label.setAttribute('aria-controls', options.id);\n\n    // @ts-expect-error\n    this.options = options;\n\n    Array.from(this.select.options).forEach((option) => {\n      const item = this.buildItem(option);\n      options.appendChild(item);\n      if (option.selected === true) {\n        this.selectItem(item);\n      }\n    });\n    this.container.appendChild(options);\n  }\n\n  buildPicker() {\n    Array.from(this.select.attributes).forEach((item) => {\n      this.container.setAttribute(item.name, item.value);\n    });\n    this.container.classList.add('ql-picker');\n    this.label = this.buildLabel();\n    this.buildOptions();\n  }\n\n  escape() {\n    // Close menu and return focus to trigger label\n    this.close();\n    // Need setTimeout for accessibility to ensure that the browser executes\n    // focus on the next process thread and after any DOM content changes\n    setTimeout(() => this.label.focus(), 1);\n  }\n\n  close() {\n    this.container.classList.remove('ql-expanded');\n    this.label.setAttribute('aria-expanded', 'false');\n    // @ts-expect-error\n    this.options.setAttribute('aria-hidden', 'true');\n  }\n\n  selectItem(item: HTMLElement | null, trigger = false) {\n    const selected = this.container.querySelector('.ql-selected');\n    if (item === selected) return;\n    if (selected != null) {\n      selected.classList.remove('ql-selected');\n    }\n    if (item == null) return;\n    item.classList.add('ql-selected');\n    // @ts-expect-error Fix me later\n    this.select.selectedIndex = Array.from(item.parentNode.children).indexOf(\n      item,\n    );\n    if (item.hasAttribute('data-value')) {\n      // @ts-expect-error Fix me later\n      this.label.setAttribute('data-value', item.getAttribute('data-value'));\n    } else {\n      this.label.removeAttribute('data-value');\n    }\n    if (item.hasAttribute('data-label')) {\n      // @ts-expect-error Fix me later\n      this.label.setAttribute('data-label', item.getAttribute('data-label'));\n    } else {\n      this.label.removeAttribute('data-label');\n    }\n    if (trigger) {\n      this.select.dispatchEvent(new Event('change'));\n      this.close();\n    }\n  }\n\n  update() {\n    let option;\n    if (this.select.selectedIndex > -1) {\n      const item =\n        // @ts-expect-error Fix me later\n        this.container.querySelector('.ql-picker-options').children[\n          this.select.selectedIndex\n        ];\n      option = this.select.options[this.select.selectedIndex];\n      // @ts-expect-error\n      this.selectItem(item);\n    } else {\n      this.selectItem(null);\n    }\n    const isActive =\n      option != null &&\n      option !== this.select.querySelector('option[selected]');\n    this.label.classList.toggle('ql-active', isActive);\n  }\n}\n\nexport default Picker;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,IAAIA,cAAc,GAAG,CAAC;AAEtB,SAASC,mBAAmBA,CAACC,OAAoB,EAAEC,SAAiB,EAAE;EACpED,OAAO,CAACE,YAAY,CAClBD,SAAS,KAAAE,MAAA,CACN,EAAEH,OAAO,CAACI,YAAY,CAACH,SAAS,CAAC,KAAK,MAAM,CAAE,CACnD,CAAC;AACH;AAAA,IAEMI,MAAM;EAKV,SAAAA,OAAYC,MAAyB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,MAAA;IACrC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACP,MAAM,CAACQ,KAAK,CAACC,OAAO,GAAG,MAAM;IAClC;IACA,IAAI,CAACT,MAAM,CAACU,UAAU,CAACC,YAAY,CAAC,IAAI,CAACP,SAAS,EAAE,IAAI,CAACJ,MAAM,CAAC;IAEhE,IAAI,CAACY,KAAK,CAACC,gBAAgB,CAAC,WAAW,EAAE,YAAM;MAC7CZ,KAAI,CAACa,YAAY,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAAC,SAAS,EAAG,UAAAE,KAAK,EAAK;MAChD,QAAQA,KAAK,CAACC,GAAG;QACf,KAAK,OAAO;UACVf,KAAI,CAACa,YAAY,CAAC,CAAC;UACnB;QACF,KAAK,QAAQ;UACXb,KAAI,CAACgB,MAAM,CAAC,CAAC;UACbF,KAAK,CAACG,cAAc,CAAC,CAAC;UACtB;QACF;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAAClB,MAAM,CAACa,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACM,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAChE;EAAA,WAAAC,aAAA,CAAAlB,OAAA,EAAAJ,MAAA;IAAAiB,GAAA;IAAAM,KAAA,EAEA,SAAAR,YAAYA,CAAA,EAAG;MACb,IAAI,CAACV,SAAS,CAACmB,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;MAC9C;MACA/B,mBAAmB,CAAC,IAAI,CAACmB,KAAK,EAAE,eAAe,CAAC;MAChD;MACAnB,mBAAmB,CAAC,IAAI,CAACgC,OAAO,EAAE,aAAa,CAAC;IAClD;EAAA;IAAAT,GAAA;IAAAM,KAAA,EAEA,SAAAI,SAASA,CAACC,MAAyB,EAAE;MAAA,IAAAC,MAAA;MACnC,IAAMC,IAAI,GAAGxB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC3C;MACAuB,IAAI,CAACC,QAAQ,GAAG,GAAG;MACnBD,IAAI,CAACjC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACnCiC,IAAI,CAACN,SAAS,CAACQ,GAAG,CAAC,gBAAgB,CAAC;MACpC,IAAMT,KAAK,GAAGK,MAAM,CAAC7B,YAAY,CAAC,OAAO,CAAC;MAC1C,IAAIwB,KAAK,EAAE;QACTO,IAAI,CAACjC,YAAY,CAAC,YAAY,EAAE0B,KAAK,CAAC;MACxC;MACA,IAAIK,MAAM,CAACK,WAAW,EAAE;QACtBH,IAAI,CAACjC,YAAY,CAAC,YAAY,EAAE+B,MAAM,CAACK,WAAW,CAAC;MACrD;MACAH,IAAI,CAAChB,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACnCe,MAAI,CAACK,UAAU,CAACJ,IAAI,EAAE,IAAI,CAAC;MAC7B,CAAC,CAAC;MACFA,IAAI,CAAChB,gBAAgB,CAAC,SAAS,EAAG,UAAAE,KAAK,EAAK;QAC1C,QAAQA,KAAK,CAACC,GAAG;UACf,KAAK,OAAO;YACVY,MAAI,CAACK,UAAU,CAACJ,IAAI,EAAE,IAAI,CAAC;YAC3Bd,KAAK,CAACG,cAAc,CAAC,CAAC;YACtB;UACF,KAAK,QAAQ;YACXU,MAAI,CAACX,MAAM,CAAC,CAAC;YACbF,KAAK,CAACG,cAAc,CAAC,CAAC;YACtB;UACF;QACF;MACF,CAAC,CAAC;MAEF,OAAOW,IAAI;IACb;EAAA;IAAAb,GAAA;IAAAM,KAAA,EAEA,SAAAY,UAAUA,CAAA,EAAG;MACX,IAAMtB,KAAK,GAAGP,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC5CM,KAAK,CAACW,SAAS,CAACQ,GAAG,CAAC,iBAAiB,CAAC;MACtCnB,KAAK,CAACuB,SAAS,GAAGC,YAAY;MAC9B;MACAxB,KAAK,CAACkB,QAAQ,GAAG,GAAG;MACpBlB,KAAK,CAAChB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACpCgB,KAAK,CAAChB,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MAC5C,IAAI,CAACQ,SAAS,CAACiC,WAAW,CAACzB,KAAK,CAAC;MACjC,OAAOA,KAAK;IACd;EAAA;IAAAI,GAAA;IAAAM,KAAA,EAEA,SAAAgB,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACb,IAAMd,OAAO,GAAGpB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC9CmB,OAAO,CAACF,SAAS,CAACQ,GAAG,CAAC,mBAAmB,CAAC;;MAE1C;MACAN,OAAO,CAAC7B,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC3C;MACA6B,OAAO,CAACK,QAAQ,GAAG,IAAI;;MAEvB;MACAL,OAAO,CAACe,EAAE,wBAAA3C,MAAA,CAAwBL,cAAe,CAAC;MAClDA,cAAc,IAAI,CAAC;MACnB,IAAI,CAACoB,KAAK,CAAChB,YAAY,CAAC,eAAe,EAAE6B,OAAO,CAACe,EAAE,CAAC;;MAEpD;MACA,IAAI,CAACf,OAAO,GAAGA,OAAO;MAEtBgB,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1C,MAAM,CAACyB,OAAO,CAAC,CAACkB,OAAO,CAAE,UAAAhB,MAAM,EAAK;QAClD,IAAME,IAAI,GAAGU,MAAI,CAACb,SAAS,CAACC,MAAM,CAAC;QACnCF,OAAO,CAACY,WAAW,CAACR,IAAI,CAAC;QACzB,IAAIF,MAAM,CAACiB,QAAQ,KAAK,IAAI,EAAE;UAC5BL,MAAI,CAACN,UAAU,CAACJ,IAAI,CAAC;QACvB;MACF,CAAC,CAAC;MACF,IAAI,CAACzB,SAAS,CAACiC,WAAW,CAACZ,OAAO,CAAC;IACrC;EAAA;IAAAT,GAAA;IAAAM,KAAA,EAEA,SAAAf,WAAWA,CAAA,EAAG;MAAA,IAAAsC,MAAA;MACZJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1C,MAAM,CAAC8C,UAAU,CAAC,CAACH,OAAO,CAAE,UAAAd,IAAI,EAAK;QACnDgB,MAAI,CAACzC,SAAS,CAACR,YAAY,CAACiC,IAAI,CAACkB,IAAI,EAAElB,IAAI,CAACP,KAAK,CAAC;MACpD,CAAC,CAAC;MACF,IAAI,CAAClB,SAAS,CAACmB,SAAS,CAACQ,GAAG,CAAC,WAAW,CAAC;MACzC,IAAI,CAACnB,KAAK,GAAG,IAAI,CAACsB,UAAU,CAAC,CAAC;MAC9B,IAAI,CAACI,YAAY,CAAC,CAAC;IACrB;EAAA;IAAAtB,GAAA;IAAAM,KAAA,EAEA,SAAAL,MAAMA,CAAA,EAAG;MAAA,IAAA+B,MAAA;MACP;MACA,IAAI,CAACC,KAAK,CAAC,CAAC;MACZ;MACA;MACAC,UAAU,CAAC;QAAA,OAAMF,MAAI,CAACpC,KAAK,CAACuC,KAAK,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC;IACzC;EAAA;IAAAnC,GAAA;IAAAM,KAAA,EAEA,SAAA2B,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC7C,SAAS,CAACmB,SAAS,CAAC6B,MAAM,CAAC,aAAa,CAAC;MAC9C,IAAI,CAACxC,KAAK,CAAChB,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MACjD;MACA,IAAI,CAAC6B,OAAO,CAAC7B,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAClD;EAAA;IAAAoB,GAAA;IAAAM,KAAA,EAEA,SAAAW,UAAUA,CAACJ,IAAwB,EAAmB;MAAA,IAAjBwB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAClD,IAAMV,QAAQ,GAAG,IAAI,CAACxC,SAAS,CAACqD,aAAa,CAAC,cAAc,CAAC;MAC7D,IAAI5B,IAAI,KAAKe,QAAQ,EAAE;MACvB,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpBA,QAAQ,CAACrB,SAAS,CAAC6B,MAAM,CAAC,aAAa,CAAC;MAC1C;MACA,IAAIvB,IAAI,IAAI,IAAI,EAAE;MAClBA,IAAI,CAACN,SAAS,CAACQ,GAAG,CAAC,aAAa,CAAC;MACjC;MACA,IAAI,CAAC/B,MAAM,CAAC0D,aAAa,GAAGjB,KAAK,CAACC,IAAI,CAACb,IAAI,CAACnB,UAAU,CAACiD,QAAQ,CAAC,CAACC,OAAO,CACtE/B,IACF,CAAC;MACD,IAAIA,IAAI,CAACgC,YAAY,CAAC,YAAY,CAAC,EAAE;QACnC;QACA,IAAI,CAACjD,KAAK,CAAChB,YAAY,CAAC,YAAY,EAAEiC,IAAI,CAAC/B,YAAY,CAAC,YAAY,CAAC,CAAC;MACxE,CAAC,MAAM;QACL,IAAI,CAACc,KAAK,CAACkD,eAAe,CAAC,YAAY,CAAC;MAC1C;MACA,IAAIjC,IAAI,CAACgC,YAAY,CAAC,YAAY,CAAC,EAAE;QACnC;QACA,IAAI,CAACjD,KAAK,CAAChB,YAAY,CAAC,YAAY,EAAEiC,IAAI,CAAC/B,YAAY,CAAC,YAAY,CAAC,CAAC;MACxE,CAAC,MAAM;QACL,IAAI,CAACc,KAAK,CAACkD,eAAe,CAAC,YAAY,CAAC;MAC1C;MACA,IAAIT,OAAO,EAAE;QACX,IAAI,CAACrD,MAAM,CAAC+D,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAACf,KAAK,CAAC,CAAC;MACd;IACF;EAAA;IAAAjC,GAAA;IAAAM,KAAA,EAEA,SAAAH,MAAMA,CAAA,EAAG;MACP,IAAIQ,MAAM;MACV,IAAI,IAAI,CAAC3B,MAAM,CAAC0D,aAAa,GAAG,CAAC,CAAC,EAAE;QAClC,IAAM7B,IAAI;QACR;QACA,IAAI,CAACzB,SAAS,CAACqD,aAAa,CAAC,oBAAoB,CAAC,CAACE,QAAQ,CACzD,IAAI,CAAC3D,MAAM,CAAC0D,aAAa,CAC1B;QACH/B,MAAM,GAAG,IAAI,CAAC3B,MAAM,CAACyB,OAAO,CAAC,IAAI,CAACzB,MAAM,CAAC0D,aAAa,CAAC;QACvD;QACA,IAAI,CAACzB,UAAU,CAACJ,IAAI,CAAC;MACvB,CAAC,MAAM;QACL,IAAI,CAACI,UAAU,CAAC,IAAI,CAAC;MACvB;MACA,IAAMgC,QAAQ,GACZtC,MAAM,IAAI,IAAI,IACdA,MAAM,KAAK,IAAI,CAAC3B,MAAM,CAACyD,aAAa,CAAC,kBAAkB,CAAC;MAC1D,IAAI,CAAC7C,KAAK,CAACW,SAAS,CAACC,MAAM,CAAC,WAAW,EAAEyC,QAAQ,CAAC;IACpD;EAAA;AAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAhE,OAAA,GAGaJ,MAAM", "ignoreList": []}]}