{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\SvgIcon\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfdmFsaWRhdGUgPSByZXF1aXJlKCJAL3V0aWxzL3ZhbGlkYXRlIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnU3ZnSWNvbicsCiAgcHJvcHM6IHsKICAgIGljb25DbGFzczogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gKDAsIF92YWxpZGF0ZS5pc0V4dGVybmFsKSh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgaWNvbk5hbWU6IGZ1bmN0aW9uIGljb25OYW1lKCkgewogICAgICByZXR1cm4gIiNpY29uLSIuY29uY2F0KHRoaXMuaWNvbkNsYXNzKTsKICAgIH0sCiAgICBzdmdDbGFzczogZnVuY3Rpb24gc3ZnQ2xhc3MoKSB7CiAgICAgIGlmICh0aGlzLmNsYXNzTmFtZSkgewogICAgICAgIHJldHVybiAnc3ZnLWljb24gJyArIHRoaXMuY2xhc3NOYW1lOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnc3ZnLWljb24nOwogICAgICB9CiAgICB9LAogICAgc3R5bGVFeHRlcm5hbEljb246IGZ1bmN0aW9uIHN0eWxlRXh0ZXJuYWxJY29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIG1hc2s6ICJ1cmwoIi5jb25jYXQodGhpcy5pY29uQ2xhc3MsICIpIG5vLXJlcGVhdCA1MCUgNTAlIiksCiAgICAgICAgJy13ZWJraXQtbWFzayc6ICJ1cmwoIi5jb25jYXQodGhpcy5pY29uQ2xhc3MsICIpIG5vLXJlcGVhdCA1MCUgNTAlIikKICAgICAgfTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_validate", "require", "name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "isExternal", "iconName", "concat", "svgClass", "styleExternalIcon", "mask"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"], "mappings": ";;;;;;AAQA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,WAAAA,oBAAA,OAAAP,SAAA;IACA;IACAQ,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAT,SAAA;IACA;IACAU,QAAA,WAAAA,SAAA;MACA,SAAAN,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;IACAO,iBAAA,WAAAA,kBAAA;MACA;QACAC,IAAA,SAAAH,MAAA,MAAAT,SAAA;QACA,uBAAAS,MAAA,MAAAT,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}