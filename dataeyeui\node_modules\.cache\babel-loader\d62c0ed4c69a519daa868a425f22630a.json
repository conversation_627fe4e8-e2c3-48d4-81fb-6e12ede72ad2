{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\system\\role\\authUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\system\\role\\authUser.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_role", "require", "_selectUser", "_interopRequireDefault", "name", "dicts", "components", "selectUser", "data", "loading", "userIds", "multiple", "showSearch", "total", "userList", "queryParams", "pageNum", "pageSize", "roleId", "undefined", "userName", "phonenumber", "created", "$route", "params", "getList", "methods", "_this", "allocatedUserList", "then", "response", "rows", "handleClose", "obj", "path", "$tab", "closeOpenPage", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "userId", "length", "openSelectUser", "$refs", "select", "show", "cancelAuthUser", "row", "_this2", "$modal", "confirm", "authUserCancel", "msgSuccess", "catch", "cancelAuthUserAll", "_this3", "join", "authUserCancelAll"], "sources": ["src/views/system/role/authUser.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n     <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n        <el-input\n          v-model=\"queryParams.phonenumber\"\n          placeholder=\"请输入手机号码\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"openSelectUser\"\n          v-hasPermi=\"['system:role:add']\"\n        >添加用户</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-circle-close\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"cancelAuthUserAll\"\n          v-hasPermi=\"['system:role:remove']\"\n        >批量取消授权</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-close\"\n          size=\"mini\"\n          @click=\"handleClose\"\n        >关闭</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-circle-close\"\n            @click=\"cancelAuthUser(scope.row)\"\n            v-hasPermi=\"['system:role:remove']\"\n          >取消授权</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n    <select-user ref=\"select\" :roleId=\"queryParams.roleId\" @ok=\"handleQuery\" />\n  </div>\n</template>\n\n<script>\nimport { allocatedUserList, authUserCancel, authUserCancelAll } from \"@/api/system/role\";\nimport selectUser from \"./selectUser\";\n\nexport default {\n  name: \"AuthUser\",\n  dicts: ['sys_normal_disable'],\n  components: { selectUser },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中用户组\n      userIds: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleId: undefined,\n        userName: undefined,\n        phonenumber: undefined\n      }\n    };\n  },\n  created() {\n    const roleId = this.$route.params && this.$route.params.roleId;\n    if (roleId) {\n      this.queryParams.roleId = roleId;\n      this.getList();\n    }\n  },\n  methods: {\n    /** 查询授权用户列表 */\n    getList() {\n      this.loading = true;\n      allocatedUserList(this.queryParams).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n        }\n      );\n    },\n    // 返回按钮\n    handleClose() {\n      const obj = { path: \"/system/role\" };\n      this.$tab.closeOpenPage(obj);\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userIds = selection.map(item => item.userId)\n      this.multiple = !selection.length\n    },\n    /** 打开授权用户表弹窗 */\n    openSelectUser() {\n      this.$refs.select.show();\n    },\n    /** 取消授权按钮操作 */\n    cancelAuthUser(row) {\n      const roleId = this.queryParams.roleId;\n      this.$modal.confirm('确认要取消该用户\"' + row.userName + '\"角色吗？').then(function() {\n        return authUserCancel({ userId: row.userId, roleId: roleId });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"取消授权成功\");\n      }).catch(() => {});\n    },\n    /** 批量取消授权按钮操作 */\n    cancelAuthUserAll(row) {\n      const roleId = this.queryParams.roleId;\n      const userIds = this.userIds.join(\",\");\n      this.$modal.confirm('是否取消选中用户授权数据项？').then(function() {\n        return authUserCancelAll({ roleId: roleId, userIds: userIds });\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"取消授权成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>"], "mappings": ";;;;;;;;;;;;AAsGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,WAAA,EAAAF;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,IAAAJ,MAAA,QAAAK,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAN,MAAA;IACA,IAAAA,MAAA;MACA,KAAAH,WAAA,CAAAG,MAAA,GAAAA,MAAA;MACA,KAAAO,OAAA;IACA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAlB,OAAA;MACA,IAAAmB,uBAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAb,QAAA,GAAAgB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAd,KAAA,GAAAiB,QAAA,CAAAjB,KAAA;QACAc,KAAA,CAAAlB,OAAA;MACA,CACA;IACA;IACA;IACAuB,WAAA,WAAAA,YAAA;MACA,IAAAC,GAAA;QAAAC,IAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;IACA,aACAI,WAAA,WAAAA,YAAA;MACA,KAAAtB,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAa,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/B,OAAA,GAAA+B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,KAAAjC,QAAA,IAAA8B,SAAA,CAAAI,MAAA;IACA;IACA,gBACAC,cAAA,WAAAA,eAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,IAAA;IACA;IACA,eACAC,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAlC,MAAA,QAAAH,WAAA,CAAAG,MAAA;MACA,KAAAmC,MAAA,CAAAC,OAAA,eAAAH,GAAA,CAAA/B,QAAA,YAAAS,IAAA;QACA,WAAA0B,oBAAA;UAAAX,MAAA,EAAAO,GAAA,CAAAP,MAAA;UAAA1B,MAAA,EAAAA;QAAA;MACA,GAAAW,IAAA;QACAuB,MAAA,CAAA3B,OAAA;QACA2B,MAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,iBACAC,iBAAA,WAAAA,kBAAAP,GAAA;MAAA,IAAAQ,MAAA;MACA,IAAAzC,MAAA,QAAAH,WAAA,CAAAG,MAAA;MACA,IAAAR,OAAA,QAAAA,OAAA,CAAAkD,IAAA;MACA,KAAAP,MAAA,CAAAC,OAAA,mBAAAzB,IAAA;QACA,WAAAgC,uBAAA;UAAA3C,MAAA,EAAAA,MAAA;UAAAR,OAAA,EAAAA;QAAA;MACA,GAAAmB,IAAA;QACA8B,MAAA,CAAAlC,OAAA;QACAkC,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}