2025-06-11 08:50:00.223 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 08:50:00.223 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 08:50:00.962 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 08:50:00.962 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 08:50:00.964 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 08:50:01.488 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 08:50:02.076 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 08:50:02.076 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 08:50:37.816 | 8ddea0983e8c4a4385e686f6dbce6af0 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为79915ab2-58cf-4305-bb7e-2f4a79f0a13b的会话获取图片验证码成功
2025-06-11 08:51:04.689 | c1165ffc594142a4b2610cc9a4eced5a | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-11 08:51:04.917 | 0edda34a73c0422496d8b0699d4cce44 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:51:05.419 | 4adab6e53ee64461a1626eeef58a5dad | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 08:52:00.018 | 0f3190e2e6824aada117a690876759f6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:52:00.220 | f2df5bfeace646098e3eb002d655afa6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 08:52:14.898 | 7af7dbe2deac4c14a98f05f012fec70b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:52:15.071 | 64381423b27548cabbe6d25901034e9b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 08:52:38.398 | 9b5f864a3038421dac1619a61c07c67d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:52:38.571 | 06c76460562d4e5c917fe006b215b247 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:12:42.583 | 0e468fa90afb47f39632effb4f901e57 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:12:42.667 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:12:42.669 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:12:46.181 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 09:12:46.182 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:12:46.982 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:12:46.982 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:12:46.984 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:12:47.429 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:12:47.936 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:12:47.936 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 09:12:48.074 | fb741e2ce2f04876a359ada2e5d1111a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:43:47.468 | 742844241e6443f7925c8c1d3a66bf65 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 09:43:47.577 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:43:47.577 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:43:51.298 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 09:43:51.298 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:43:52.852 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:43:52.853 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:43:52.854 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:43:53.370 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:43:54.159 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:43:54.160 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 09:43:54.161 | e50aeaf7a47d44059eea44409b6b1f09 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 09:43:54.227 | cbe952707ff34849b0a88ecc212bda08 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为597de780-44b6-4ca8-8cf8-4362324c8f3a的会话获取图片验证码成功
2025-06-11 09:43:58.072 | 6a2797ea0b354723b7ce757aeef144d3 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-11 09:43:58.407 | 678a01707c94474fb1fc787229e5a177 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:43:58.970 | fcf5d106495549d5a580400e65f6faea | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:53:10.696 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:53:10.696 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:53:13.833 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 09:53:13.833 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:53:14.577 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:53:14.577 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:53:14.579 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:53:14.991 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:53:15.494 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:53:15.494 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 09:58:11.951 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:58:11.951 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:58:15.342 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 09:58:15.343 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:58:16.065 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:58:16.066 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:58:16.067 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:58:16.474 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:58:16.974 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:58:16.974 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 09:58:45.186 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:58:45.187 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:58:47.623 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 09:58:47.624 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:58:48.311 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:58:48.312 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:58:48.313 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:58:48.693 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:58:49.207 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:58:49.207 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 10:01:20.614 | a4ffd8a1718d45e08d0e455b5d3a67bc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:01:20.743 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:01:20.743 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:01:24.139 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 10:01:24.139 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:01:25.024 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:01:25.024 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:01:25.026 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:01:25.514 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:01:26.058 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:01:26.058 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 10:01:26.209 | d62e260e8c4b4cfcae7e1d5a4d8ee9ad | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:36:32.884 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:36:32.888 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:36:35.491 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 10:36:35.491 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:36:36.511 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:36:36.511 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:36:36.513 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:36:37.001 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:36:37.520 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:36:37.520 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 10:57:14.057 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:57:14.058 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:57:17.330 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 10:57:17.331 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:57:18.163 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:57:18.163 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:57:18.166 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:57:18.675 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:57:19.198 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:57:19.198 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 12:15:16.790 | 00f482ce779143c5a52edb16cc0652be | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 12:15:17.082 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 12:15:17.082 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 16:55:37.902 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 16:55:37.903 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 16:55:38.601 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 16:55:38.601 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 16:55:38.605 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 16:55:39.141 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 16:55:39.635 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 16:55:39.635 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 17:12:50.740 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 17:12:50.741 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 17:12:54.252 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 17:12:54.252 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 17:12:55.069 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 17:12:55.069 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 17:12:55.070 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 17:12:55.571 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 17:12:56.112 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 17:12:56.112 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 17:13:13.632 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 17:13:13.632 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 17:13:14.331 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 17:13:14.331 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 17:13:14.334 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 17:13:14.728 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 17:13:15.228 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 17:13:15.228 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 17:16:26.061 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 17:16:26.062 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 17:16:26.139 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 17:16:26.141 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 17:16:28.633 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 17:16:28.634 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 17:16:29.354 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 17:16:29.354 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 17:16:29.355 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 17:16:29.429 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 17:16:29.429 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 17:16:29.760 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 17:16:30.132 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 17:16:30.132 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 17:16:30.134 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 17:16:30.170 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 17:16:30.172 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 17:16:30.297 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 17:16:30.297 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 17:16:30.316 | 57faf9858ea446ab957b8acb282434da | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.319 | 1563a22a207e44a09c4a2c791549f092 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.320 | 1a62671f6db546be8a4f9e0c210af064 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.320 | 8ffeff3cafd54c49a0c4c5025e3f57ad | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.321 | cc3ee7ad5864424a82b2bf7629018751 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.321 | fe72944ca91e4e0fb3404662d8345b2c | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.322 | 36b382581b7e49dcad90208116bdbaf9 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.322 | a441352ff8864a46beaea8f78fd05c0a | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.323 | 59677fb1f4f6428f9b68e5968895578b | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.324 | 704b110591f44bdaaae21108035f826c | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:30.425 | eff4b7fe43614a72b996bc4706b6baae | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.525 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 17:16:30.533 | f58adaee76a24e8bab9da441131d441c | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.539 | 9a1a63dc5abe43c6a0eecf5f5ed8c83b | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.544 | 488f0f3c110c4106a578d0265e916b46 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.547 | c5ec5b9f31044c2aa9ec14fd50b4d7b9 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.561 | 756d398952a04f4ebaedc229cbf3fd21 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.563 | a5941898d3a34324ac38aa06846f0cf4 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.570 | 68aa5d71444b44e4a4a820dfec26b218 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.575 | d861f02434c3425591ddbb07ac46b4d8 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:30.845 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 17:16:30.845 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 17:16:30.849 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 17:16:31.094 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 17:16:31.094 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 17:16:31.243 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 17:16:31.740 | 7ad2a9873897452d9ff06a592a5cbd35 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-11 17:16:31.791 | af2a660afac5446fa51c1a7111517f4a | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-11 17:16:31.816 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 17:16:31.816 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 17:16:31.932 | 24d789a8c24f479d9e436f8a62d727f6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为4ed2fbc2-ee63-49ba-a6d3-bcd089374350的会话获取图片验证码成功
2025-06-11 17:16:34.765 | 752109b775894d528385fb9132d33efc | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-11 17:16:35.276 | 898e9767ec7d403fa975d76fd568ffe8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 17:16:35.536 | e63f063a7e8844eb9696e89842fa4abd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:07:01.171 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:07:01.172 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:07:04.635 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 18:07:04.636 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:07:05.330 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:07:05.330 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:07:05.331 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:07:05.763 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:07:06.323 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:07:06.323 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 18:11:01.135 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:11:01.135 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:11:04.677 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 18:11:04.678 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:11:05.493 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:11:05.494 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:11:05.495 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:11:05.945 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:11:06.456 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:11:06.456 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 18:11:34.521 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:11:34.522 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:11:37.332 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 18:11:37.333 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:11:38.103 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:11:38.104 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:11:38.105 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:11:38.535 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:11:39.054 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:11:39.055 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 18:12:01.229 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:12:01.229 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:12:04.044 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 18:12:04.046 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:12:04.744 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:12:04.744 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:12:04.746 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:12:05.142 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:12:05.652 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:12:05.653 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 18:12:33.900 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:12:33.901 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:12:34.104 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:12:34.105 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:12:36.882 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 18:12:36.883 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:12:37.663 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:12:37.663 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:12:37.665 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:12:37.822 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-11 18:12:37.824 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:12:38.100 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:12:38.541 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:12:38.542 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:12:38.543 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:12:38.679 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:12:38.679 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 18:12:38.948 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:12:39.494 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:12:39.494 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-11 18:13:01.173 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:13:01.174 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:13:07.663 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:13:07.663 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:17:01.219 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:17:01.220 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:19:51.091 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:19:51.092 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:19:51.114 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:19:51.114 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:19:51.981 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:19:51.981 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:19:51.983 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:19:52.057 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:19:52.057 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:19:52.058 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:19:52.399 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:19:52.520 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:19:52.974 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:19:52.974 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:19:53.042 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:19:53.042 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:21:14.323 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:21:14.324 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:21:15.278 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:21:15.278 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:21:15.279 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:21:15.734 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:21:16.241 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:21:16.242 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:24:01.135 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:24:01.136 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:24:04.416 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:24:04.417 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:24:05.308 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:24:05.308 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:24:05.309 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:24:05.736 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:24:06.218 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:24:06.218 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:24:48.873 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:24:48.875 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:24:51.743 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:24:51.743 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:24:52.650 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:24:52.651 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:24:52.652 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:24:53.084 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:24:53.588 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:24:53.589 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:32:01.220 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:32:01.222 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:32:04.120 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:32:04.121 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:32:05.003 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:32:05.004 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:32:05.005 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:32:05.426 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:32:05.930 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:32:05.930 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:33:23.324 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:33:23.325 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:33:23.327 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:33:23.330 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:33:26.642 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:33:26.642 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:33:26.643 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:33:26.643 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:33:27.532 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:33:27.532 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:33:27.534 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:33:27.568 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:33:27.568 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:33:27.569 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:33:27.958 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:33:28.012 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:33:28.505 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:33:28.506 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:33:28.518 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:33:28.518 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:37:01.141 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:37:01.143 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:37:04.116 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:37:04.117 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:37:05.062 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:37:05.062 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:37:05.064 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:37:05.515 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:37:05.988 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:37:05.988 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:38:24.209 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:38:24.209 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:38:24.224 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:38:24.225 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:38:27.530 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:38:27.530 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:38:27.532 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:38:27.533 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:38:28.424 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:38:28.424 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:38:28.425 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:38:28.429 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:38:28.430 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:38:28.432 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:38:28.854 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:38:28.863 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:38:29.343 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:38:29.343 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:38:29.397 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:38:29.398 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:42:13.289 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:42:13.290 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:42:14.157 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:42:14.157 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:42:14.159 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:42:14.572 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:42:15.090 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:42:15.090 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:42:19.944 | bc92c4e7f887459fb724c26b2d476378 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a01b6fdd-7bcb-4909-9789-7f00dd3b4a46的会话获取图片验证码成功
2025-06-11 18:42:22.278 | a25f575a3234487191e1eca2c7c74fcf | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-11 18:42:22.483 | 12008fc2bee249be8294db639d048209 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:42:22.657 | cbe557533ba442f99f84b3af37a92b57 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:43:47.341 | 8a5c0abbe59b492584f63a5c71310dce | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:43:47.515 | bdf28f7151204aa19762f01130f90405 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:44:16.121 | ccf200bb1463423491c31982ad0cc6d3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:44:16.298 | 25b9750354da4f599abfa3ddb25730fe | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:45:35.979 | 40642a8c0a004361a4ecbc27e8d7ce5c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:45:36.153 | 15f92d7750be436f9e048cf8b28f6bfa | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:50:46.490 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:50:46.490 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:50:46.799 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:50:46.799 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:50:49.322 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:50:49.322 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:50:49.591 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:50:49.591 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:50:51.161 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:50:51.162 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:50:51.163 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:50:52.353 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:50:53.033 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:50:53.033 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:50:53.067 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:50:53.067 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:50:53.068 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:50:53.098 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:50:53.098 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:50:53.587 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:50:55.574 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:50:55.574 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:50:55.928 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:50:55.928 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:50:58.125 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:50:58.126 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:50:58.127 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:50:59.831 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:51:00.458 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:51:00.458 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:51:37.607 | ad8001f229ae4849a43b86c123f3c5d9 | ERROR    | app.controllers.pinboard_controller:test_pinboard:28 - 测试接口失败: ResponseUtil.success() got an unexpected keyword argument 'message'
2025-06-11 18:51:37.607 | ad8001f229ae4849a43b86c123f3c5d9 | ERROR    | exceptions.handle:exception_handler:70 - ResponseUtil.error() got an unexpected keyword argument 'message'
Traceback (most recent call last):

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 26, in test_pinboard
    return ResponseUtil.success(data={"message": "Pinboard接口正常工作", "timestamp": "2024-12-28"}, message="测试成功")
           │            └ <classmethod(<function ResponseUtil.success at 0x0000019FB4EDE8C0>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.success() got an unexpected keyword argument 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 368
               │     └ 3
               └ <function _main at 0x0000019F90312710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 368
           │    └ <function BaseProcess._bootstrap at 0x0000019F90229990>
           └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000019F90229000>
    └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000019F903295D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>
    │    └ <function subprocess_started at 0x0000019F92699990>
    └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=460, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000019F903296F0>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=460, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000019F9267A5F0>
           │       │   └ <uvicorn.server.Server object at 0x0000019F903296F0>
           │       └ <function run at 0x0000019F9031CDC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000019FB7289070>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000019F91F8E320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000019F91F8E290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000019F91F8FD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000019F91E6B760>
    └ <Handle <TaskStepMethWrapper object at 0x0000019FB85AFA90>()>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x0000019FB85AFA90>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x0000019FB85AFA90>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x0000019FB85AFA90>()>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000019FB739DF00>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000019F90329960>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000019FB739DF00>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000019FB74023E0>
          └ <fastapi.applications.FastAPI object at 0x0000019F90329960>

> File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000019FB742CAF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000019FB74024A0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000019FB74023E0>

  File "E:\Big model\金刚数瞳前端\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000019FB859ECB0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000019FB74024D0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000019FB74024A0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000019FB859ECB0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860CD90>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000019FB4EDF880>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860CD90>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000019FB7402590>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860CD90>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860CD90>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000019FB74025C0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000019FB7402590>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860CD90>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000019FB860CB20>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000019FB74025C0>
          └ <function wrap_app_handling_exceptions at 0x0000019F93627250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB859E560>
          │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>

  File "E:\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB859E560>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>>
          └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>

  File "E:\python\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB859E560>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000019F93658820>
          └ APIRoute(path='/pinboard/test', name='test_pinboard', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB859E560>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000019FB7283400>
          └ APIRoute(path='/pinboard/test', name='test_pinboard', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB859E560>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000019FB860C940>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000019FB859E200>
          └ <function wrap_app_handling_exceptions at 0x0000019F93627250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB859ED40>
          │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859EE60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000019FB859E200>

  File "E:\python\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000019FB860C940>
                     └ <function get_request_handler.<locals>.app at 0x0000019FB7283370>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000019F9365A320>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {}
                 │         └ <function test_pinboard at 0x0000019FB6F2BD00>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[], security_requ...

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 29, in test_pinboard
    return ResponseUtil.error(message=f"测试接口失败: {str(e)}")
           │            └ <classmethod(<function ResponseUtil.error at 0x0000019FB4EDEB90>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.error() got an unexpected keyword argument 'message'
2025-06-11 18:52:05.534 | 7eefd9f264824788ad61d2f9206293a4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:52:05.731 | c5f7c0b8176b4890ab1550ce2741d7a1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:52:06.162 | 0407afd173594425afba5de72c70ac84 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-11 18:52:06.449 | 0407afd173594425afba5de72c70ac84 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 4 条记录
2025-06-11 18:52:06.449 | 0407afd173594425afba5de72c70ac84 | ERROR    | app.controllers.pinboard_controller:get_my_pinboards:159 - 获取我的Pinboard列表失败: ResponseUtil.success() got an unexpected keyword argument 'message'
2025-06-11 18:52:06.494 | 0407afd173594425afba5de72c70ac84 | ERROR    | exceptions.handle:exception_handler:70 - ResponseUtil.error() got an unexpected keyword argument 'message'
Traceback (most recent call last):

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 156, in get_my_pinboards
    return ResponseUtil.success(data=result, message="获取我的Pinboard列表成功")
           │            │            └ {'items': [PinboardItem(uid='pb-001-uuid-001', name='我的问题测试1', description='这是一个测试报告，用于验证数据分析功能', owner_id=1, owner_name='adm...
           │            └ <classmethod(<function ResponseUtil.success at 0x0000019FB4EDE8C0>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.success() got an unexpected keyword argument 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 368
               │     └ 3
               └ <function _main at 0x0000019F90312710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 368
           │    └ <function BaseProcess._bootstrap at 0x0000019F90229990>
           └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000019F90229000>
    └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000019F903295D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>
    │    └ <function subprocess_started at 0x0000019F92699990>
    └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=460, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000019F903296F0>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=460, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000019F9267A5F0>
           │       │   └ <uvicorn.server.Server object at 0x0000019F903296F0>
           │       └ <function run at 0x0000019F9031CDC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000019FB7289070>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000019F91F8E320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000019F91F8E290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000019F91F8FD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000019F91E6B760>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000019FB739DF00>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000019F90329960>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000019FB739DF00>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000019FB74023E0>
          └ <fastapi.applications.FastAPI object at 0x0000019F90329960>

> File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000019FB859EEF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000019FB74024A0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000019FB74023E0>

  File "E:\Big model\金刚数瞳前端\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000019FB87691B0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000019FB74024D0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000019FB74024A0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000019FB87691B0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860D630>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000019FB4EDF880>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860D630>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000019FB7402590>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860D630>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860D630>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000019FB74025C0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000019FB7402590>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000019FB860D630>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000019FB860FEB0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000019FB74025C0>
          └ <function wrap_app_handling_exceptions at 0x0000019F93627250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB8769240>
          │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>

  File "E:\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB8769240>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>>
          └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>

  File "E:\python\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB8769240>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000019F93658820>
          └ APIRoute(path='/pinboard/my', name='get_my_pinboards', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB8769240>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000019FB7283AC0>
          └ APIRoute(path='/pinboard/my', name='get_my_pinboards', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB8769240>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000019FB860D870>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000019FB87693F0>
          └ <function wrap_app_handling_exceptions at 0x0000019F93627250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB8769480>
          │   │      └ <function RequestResponseCycle.receive at 0x0000019FB859E4D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000019FB87693F0>

  File "E:\python\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000019FB860D870>
                     └ <function get_request_handler.<locals>.app at 0x0000019FB72839A0>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000019F9365A320>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': CurrentUserModel(permissions=['*:*:*'], roles=['admin'], user=UserInfoModel(user_id=1, dept_id=103, user_nam...
                 │         └ <function get_my_pinboards at 0x0000019FB6F9B1C0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(1), name='page', mode='validation'), ModelField(field_inf...

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 160, in get_my_pinboards
    return ResponseUtil.error(message=f"获取我的Pinboard列表失败: {str(e)}")
           │            └ <classmethod(<function ResponseUtil.error at 0x0000019FB4EDEB90>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.error() got an unexpected keyword argument 'message'
2025-06-11 18:52:06.595 | 21a5cee11bb947408f334528b2327cf3 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-11 18:52:06.661 | 21a5cee11bb947408f334528b2327cf3 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-11 18:52:06.661 | 21a5cee11bb947408f334528b2327cf3 | ERROR    | app.controllers.pinboard_controller:get_tags:107 - 获取标签列表失败: ResponseUtil.success() got an unexpected keyword argument 'message'
2025-06-11 18:52:06.711 | 21a5cee11bb947408f334528b2327cf3 | ERROR    | exceptions.handle:exception_handler:70 - ResponseUtil.error() got an unexpected keyword argument 'message'
Traceback (most recent call last):

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 104, in get_tags
    return ResponseUtil.success(data=result, message="获取标签列表成功")
           │            │            └ [TagItem(id=6, name='质量监控', color='#9C27B0', report_count=1, modify_type='手动'), TagItem(id=3, name='趋势分析', color='#E6A23C', r...
           │            └ <classmethod(<function ResponseUtil.success at 0x0000019FB4EDE8C0>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.success() got an unexpected keyword argument 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 368
               │     └ 3
               └ <function _main at 0x0000019F90312710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 368
           │    └ <function BaseProcess._bootstrap at 0x0000019F90229990>
           └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000019F90229000>
    └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000019F903295D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>
    │    └ <function subprocess_started at 0x0000019F92699990>
    └ <SpawnProcess name='SpawnProcess-16' parent=39380 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=460, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000019F903296F0>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=460, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000019F9267A5F0>
           │       │   └ <uvicorn.server.Server object at 0x0000019F903296F0>
           │       └ <function run at 0x0000019F9031CDC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000019FB7289070>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000019F91F8E320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000019F91F8E290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000019F91F8FD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000019F91E6B760>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000019FB739DF00>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000019F90329960>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000019FB739DF00>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019FB8...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000019FB74023E0>
          └ <fastapi.applications.FastAPI object at 0x0000019F90329960>

> File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000019FB876B880>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000019...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000019FB74024A0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000019FB74023E0>

  File "E:\Big model\金刚数瞳前端\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000019FB876BAC0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000019FB74024D0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000019FB74024A0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000019FB876BAC0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB87FA8C0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000019FB4EDF880>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB87FA8C0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000019FB7402590>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000019FB87FA8C0>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000019FB87FA8C0>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000019FB74025C0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000019FB7402590>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000019FB87FA8C0>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000019FB87FA9E0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000019FB74025C0>
          └ <function wrap_app_handling_exceptions at 0x0000019F93627250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB876BBE0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>

  File "E:\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB876BBE0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>>
          └ <fastapi.routing.APIRouter object at 0x0000019FB6FE3F40>

  File "E:\python\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB876BBE0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000019F93658820>
          └ APIRoute(path='/pinboard/tags', name='get_tags', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB876BBE0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000019FB7283760>
          └ APIRoute(path='/pinboard/tags', name='get_tags', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB876BBE0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000019FB87FAB90>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000019FB876BC70>
          └ <function wrap_app_handling_exceptions at 0x0000019F93627250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000019FB876BD00>
          │   │      └ <function RequestResponseCycle.receive at 0x0000019FB876B910>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000019FB876BC70>

  File "E:\python\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000019FB87FAB90>
                     └ <function get_request_handler.<locals>.app at 0x0000019FB72836D0>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000019F9365A320>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': CurrentUserModel(permissions=['*:*:*'], roles=['admin'], user=UserInfoModel(user_id=1, dept_id=103, user_nam...
                 │         └ <function get_tags at 0x0000019FB6F9B0A0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='project_id', mode='validation')], header_par...

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 108, in get_tags
    return ResponseUtil.error(message=f"获取标签列表失败: {str(e)}")
           │            └ <classmethod(<function ResponseUtil.error at 0x0000019FB4EDEB90>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.error() got an unexpected keyword argument 'message'
2025-06-11 18:52:20.841 | 2d0162dfd3534129b1e2123224546b17 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:52:20.992 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:52:20.993 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:52:24.149 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:52:24.150 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:52:24.993 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:52:24.993 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:52:24.995 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:52:25.411 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:52:25.996 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:52:25.997 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:52:26.123 | 33155ef5ff5e415b8e0755aff4bf65af | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:52:26.474 | 9c6a415b163d4e288c88164a2bcea3a3 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-11 18:52:26.804 | 9c6a415b163d4e288c88164a2bcea3a3 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 4 条记录
2025-06-11 18:52:26.806 | 9c6a415b163d4e288c88164a2bcea3a3 | ERROR    | app.controllers.pinboard_controller:get_my_pinboards:159 - 获取我的Pinboard列表失败: ResponseUtil.success() got an unexpected keyword argument 'message'
2025-06-11 18:52:26.848 | 9c6a415b163d4e288c88164a2bcea3a3 | ERROR    | exceptions.handle:exception_handler:70 - ResponseUtil.error() got an unexpected keyword argument 'message'
Traceback (most recent call last):

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 156, in get_my_pinboards
    return ResponseUtil.success(data=result, message="获取我的Pinboard列表成功")
           │            │            └ {'items': [PinboardItem(uid='pb-001-uuid-001', name='我的问题测试1', description='这是一个测试报告，用于验证数据分析功能', owner_id=1, owner_name='adm...
           │            └ <classmethod(<function ResponseUtil.success at 0x0000028BA7DC28C0>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.success() got an unexpected keyword argument 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 344
               │     └ 3
               └ <function _main at 0x0000028B831F2710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 344
           │    └ <function BaseProcess._bootstrap at 0x0000028B830F9990>
           └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000028B830F9000>
    └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000028B832095D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>
    │    └ <function subprocess_started at 0x0000028B85569990>
    └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=2196, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000028B832096F0>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=2196, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000028B8554A5F0>
           │       │   └ <uvicorn.server.Server object at 0x0000028B832096F0>
           │       └ <function run at 0x0000028B831FCDC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000028BAA171070>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000028B84E1E320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000028B84E1E290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028B84E1FD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028B84D7B760>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000028BAA27DF30>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028BAB...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000028B83209960>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000028BAA27DF30>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028BAB...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028BAB...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000028BAA2E2410>
          └ <fastapi.applications.FastAPI object at 0x0000028B83209960>

> File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000028BAA30CAF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000028BAA2E24D0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000028BAA2E2410>

  File "E:\Big model\金刚数瞳前端\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000028BAB47EE60>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000028BAA2E2500>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000028BAA2E24D0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000028BAB47EE60>
          │         │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB55D3C0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000028BA7DC3880>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB55D3C0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028BAA2E25C0>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB55D3C0>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB55D3C0>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000028BAA2E25F0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028BAA2E25C0>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB55D3C0>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000028BAB55CC40>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000028BAA2E25F0>
          └ <function wrap_app_handling_exceptions at 0x0000028B86507250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB47E560>
          │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>

  File "E:\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB47E560>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>>
          └ <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>

  File "E:\python\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB47E560>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000028B86538820>
          └ APIRoute(path='/pinboard/my', name='get_my_pinboards', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB47E560>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000028BAA167AC0>
          └ APIRoute(path='/pinboard/my', name='get_my_pinboards', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB47E560>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000028BAB55D510>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000028BAB47E4D0>
          └ <function wrap_app_handling_exceptions at 0x0000028B86507250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB47E200>
          │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB47ECB0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000028BAB47E4D0>

  File "E:\python\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000028BAB55D510>
                     └ <function get_request_handler.<locals>.app at 0x0000028BAA1679A0>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000028B8653A320>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': CurrentUserModel(permissions=['*:*:*'], roles=['admin'], user=UserInfoModel(user_id=1, dept_id=103, user_nam...
                 │         └ <function get_my_pinboards at 0x0000028BA9E771C0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(1), name='page', mode='validation'), ModelField(field_inf...

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 160, in get_my_pinboards
    return ResponseUtil.error(message=f"获取我的Pinboard列表失败: {str(e)}")
           │            └ <classmethod(<function ResponseUtil.error at 0x0000028BA7DC2B90>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.error() got an unexpected keyword argument 'message'
2025-06-11 18:52:26.894 | 4d281f14385b4366b23f11052c564a50 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-11 18:52:26.972 | 4d281f14385b4366b23f11052c564a50 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-11 18:52:26.972 | 4d281f14385b4366b23f11052c564a50 | ERROR    | app.controllers.pinboard_controller:get_tags:107 - 获取标签列表失败: ResponseUtil.success() got an unexpected keyword argument 'message'
2025-06-11 18:52:27.015 | 4d281f14385b4366b23f11052c564a50 | ERROR    | exceptions.handle:exception_handler:70 - ResponseUtil.error() got an unexpected keyword argument 'message'
Traceback (most recent call last):

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 104, in get_tags
    return ResponseUtil.success(data=result, message="获取标签列表成功")
           │            │            └ [TagItem(id=6, name='质量监控', color='#9C27B0', report_count=1, modify_type='手动'), TagItem(id=3, name='趋势分析', color='#E6A23C', r...
           │            └ <classmethod(<function ResponseUtil.success at 0x0000028BA7DC28C0>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.success() got an unexpected keyword argument 'message'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 344
               │     └ 3
               └ <function _main at 0x0000028B831F2710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 344
           │    └ <function BaseProcess._bootstrap at 0x0000028B830F9990>
           └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000028B830F9000>
    └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000028B832095D0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>
    │    └ <function subprocess_started at 0x0000028B85569990>
    └ <SpawnProcess name='SpawnProcess-17' parent=39380 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=2196, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000028B832096F0>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=2196, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000028B8554A5F0>
           │       │   └ <uvicorn.server.Server object at 0x0000028B832096F0>
           │       └ <function run at 0x0000028B831FCDC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000028BAA171070>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000028B84E1E320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000028B84E1E290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028B84E1FD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028B84D7B760>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000028BAA27DF30>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028BAB...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000028B83209960>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000028BAA27DF30>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028BAB...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028BAB...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000028BAA2E2410>
          └ <fastapi.applications.FastAPI object at 0x0000028B83209960>

> File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000028BAB561240>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000028...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000028BAA2E24D0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000028BAA2E2410>

  File "E:\Big model\金刚数瞳前端\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000028BAB561480>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000028BAA2E2500>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000028BAA2E24D0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000028BAB561480>
          │         │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB58DC90>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000028BA7DC3880>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB58DC90>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028BAA2E25C0>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB58DC90>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB58DC90>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000028BAA2E25F0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028BAA2E25C0>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000028BAB58DC90>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000028BAB58DDB0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000028BAA2E25F0>
          └ <function wrap_app_handling_exceptions at 0x0000028B86507250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB5615A0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>

  File "E:\python\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB5615A0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>>
          └ <fastapi.routing.APIRouter object at 0x0000028BA9EC3F70>

  File "E:\python\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB5615A0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000028B86538820>
          └ APIRoute(path='/pinboard/tags', name='get_tags', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB5615A0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000028BAA167760>
          └ APIRoute(path='/pinboard/tags', name='get_tags', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB5615A0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000028BAB58DF60>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000028BAB561630>
          └ <function wrap_app_handling_exceptions at 0x0000028B86507250>

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc

  File "E:\python\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000028BAB5616C0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000028BAB5612D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000028BAB561630>

  File "E:\python\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000028BAB58DF60>
                     └ <function get_request_handler.<locals>.app at 0x0000028BAA1676D0>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000028B8653A320>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': CurrentUserModel(permissions=['*:*:*'], roles=['admin'], user=UserInfoModel(user_id=1, dept_id=103, user_nam...
                 │         └ <function get_tags at 0x0000028BA9E770A0>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(None), name='project_id', mode='validation')], header_par...

  File "E:\Big model\金刚数瞳前端\dataeyeapi\app\controllers\pinboard_controller.py", line 108, in get_tags
    return ResponseUtil.error(message=f"获取标签列表失败: {str(e)}")
           │            └ <classmethod(<function ResponseUtil.error at 0x0000028BA7DC2B90>)>
           └ <class 'utils.response_util.ResponseUtil'>

TypeError: ResponseUtil.error() got an unexpected keyword argument 'message'
2025-06-11 18:52:53.924 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:52:53.926 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:52:57.949 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:52:57.949 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:52:58.807 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:52:58.807 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:52:58.809 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:52:59.225 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:52:59.798 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:52:59.799 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:53:01.215 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:53:01.216 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:53:04.495 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:53:04.495 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:53:05.322 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:53:05.322 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:53:05.323 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:53:05.721 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:53:06.245 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:53:06.246 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:53:27.757 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:53:27.758 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:53:31.266 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:53:31.267 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:53:32.108 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:53:32.108 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:53:32.110 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:53:32.515 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:53:32.999 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:53:32.999 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:53:34.288 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:53:34.289 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:53:37.633 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:53:37.633 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:53:38.539 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:53:38.540 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:53:38.541 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:53:38.974 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:53:39.488 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:53:39.488 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:54:01.122 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:54:01.123 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:54:01.201 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:54:01.201 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:54:04.627 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:54:04.628 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:54:04.712 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:54:04.713 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:54:05.482 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:54:05.482 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:54:05.485 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:54:05.621 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:54:05.622 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:54:05.624 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:54:05.899 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:54:06.061 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:54:06.489 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:54:06.489 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:54:06.576 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:54:06.576 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:54:34.407 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:54:34.407 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:54:34.483 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:54:34.484 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:54:37.858 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:54:37.858 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:54:37.937 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:54:37.938 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:54:38.809 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:54:38.809 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:54:38.810 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:54:38.960 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:54:38.962 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:54:38.963 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:54:39.267 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:54:39.451 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:54:39.765 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:54:39.765 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:54:39.962 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:54:39.963 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:55:01.228 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:55:01.230 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:55:04.669 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:55:04.669 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:55:05.530 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:55:05.530 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:55:05.531 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:55:05.938 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:55:06.469 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:55:06.469 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:55:07.332 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:55:07.332 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:55:10.615 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:55:10.616 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:55:11.613 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:55:11.614 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:55:11.615 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:55:12.087 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:55:12.590 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:55:12.590 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:55:37.000 | e180b10e7ffa44a29bacd881c35ae22b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:55:37.196 | 0b11f0e327834822b1aeaf718fa19533 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:55:37.598 | 69efc990e1f0474daa9a9dabec8d884e | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-11 18:55:37.944 | 69efc990e1f0474daa9a9dabec8d884e | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 4 条记录
2025-06-11 18:55:38.032 | 4949d45113e34fa69edbf17e138874b1 | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-11 18:55:38.111 | 4949d45113e34fa69edbf17e138874b1 | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
2025-06-11 18:55:44.971 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:55:44.972 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:55:45.002 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 18:55:45.003 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 18:55:48.627 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:55:48.628 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:55:48.647 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-11 18:55:48.647 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 18:55:49.470 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:55:49.470 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:55:49.471 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:55:49.478 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 18:55:49.478 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 18:55:49.480 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 18:55:49.877 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:55:49.894 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 18:55:50.372 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:55:50.372 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:55:50.408 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 18:55:50.408 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-11 18:56:51.381 | 94d4d2c649104c418adefd828404f181 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 18:56:51.575 | 08ee7df3597f414b979d5e1f0c7de0ac | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 18:56:52.002 | 3ef09b9101e546a3b9ccde2bbe32eea4 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:145 - 用户 admin 请求我的Pinboard列表
2025-06-11 18:56:52.215 | 3ef09b9101e546a3b9ccde2bbe32eea4 | INFO     | app.controllers.pinboard_controller:get_my_pinboards:155 - 成功获取我的Pinboard列表，共 4 条记录
2025-06-11 18:56:52.308 | bab419b6795d42b99eaf6fb2d3656e8b | INFO     | app.controllers.pinboard_controller:get_tags:95 - 用户 admin 请求标签列表
2025-06-11 18:56:52.387 | bab419b6795d42b99eaf6fb2d3656e8b | INFO     | app.controllers.pinboard_controller:get_tags:103 - 成功获取标签列表，共 4 个标签
