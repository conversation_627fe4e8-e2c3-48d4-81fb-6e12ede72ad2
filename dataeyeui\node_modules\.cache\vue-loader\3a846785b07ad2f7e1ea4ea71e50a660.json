{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\redirect.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\redirect.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBjcmVhdGVkKCkgewogICAgY29uc3QgeyBwYXJhbXMsIHF1ZXJ5IH0gPSB0aGlzLiRyb3V0ZQogICAgY29uc3QgeyBwYXRoIH0gPSBwYXJhbXMKICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsgcGF0aDogJy8nICsgcGF0aCwgcXVlcnkgfSkKICB9LAogIHJlbmRlcjogZnVuY3Rpb24oaCkgewogICAgcmV0dXJuIGgoKSAvLyBhdm9pZCB3YXJuaW5nIG1lc3NhZ2UKICB9Cn0K"}, {"version": 3, "sources": ["redirect.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "redirect.vue", "sourceRoot": "src/views", "sourcesContent": ["<script>\nexport default {\n  created() {\n    const { params, query } = this.$route\n    const { path } = params\n    this.$router.replace({ path: '/' + path, query })\n  },\n  render: function(h) {\n    return h() // avoid warning message\n  }\n}\n</script>\n"]}]}