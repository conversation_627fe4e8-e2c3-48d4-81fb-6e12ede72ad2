{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\mybutton\\index.vue?vue&type=style&index=0&id=2246cf7b&scoped=true&lang=css", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\mybutton\\index.vue", "mtime": 1750057981837}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsTA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/mybutton", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"tab-container\">\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'personal' }\" @click=\"activeTab = 'personal'\">个人设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'workspace' }\" @click=\"activeTab = 'workspace'\">工作区设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'analysis' }\" @click=\"activeTab = 'analysis'\">智能解析设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'search' }\" @click=\"activeTab = 'search'\">搜索提示设置</div>\n    </div>\n\n    <!-- 加载状态 -->\n    <el-skeleton v-if=\"loading\" :rows=\"6\" animated />\n\n    <!-- 个人设置 -->\n    <div v-if=\"activeTab === 'personal' && !loading\" class=\"form-container\">\n      <div class=\"form-item\">\n        <div class=\"form-label\">用户ID：</div>\n        <div class=\"form-input\">\n          <el-input v-model=\"userId\" placeholder=\"请输入用户ID\"></el-input>\n        </div>\n        <div class=\"form-count\">{{ userIdLength }} / 20</div>\n      </div>\n\n      <div class=\"form-item\">\n        <div class=\"form-label\">密码：</div>\n        <div class=\"form-input\">\n          <el-input v-model=\"password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\n        </div>\n      </div>\n\n      <div class=\"form-item\">\n        <div class=\"form-label\"></div>\n        <div class=\"form-input\">\n          <a href=\"javascript:;\" class=\"login-link\">返回登录 ></a>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"resetToDefault\" :loading=\"loading\">设为默认</el-button>\n      </div>\n    </div>\n\n    <!-- 搜索提示设置 -->\n    <div v-if=\"activeTab === 'search' && !loading\" class=\"form-container\">\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">智能提示</div>\n          <el-switch v-model=\"searchSettings.smartTips\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">根据您的搜索行为切换不同的提示词</div>\n      </div>\n\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">默认字段提示</div>\n          <el-switch v-model=\"searchSettings.trySuggestions\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">任何情况对焦搜索框都给出字段提示，可手动切换到问题提示</div>\n      </div>\n\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">默认问题提示</div>\n          <el-switch v-model=\"searchSettings.relatedSearch\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">任何情况对焦搜索框都给出问题提示，可手动切换到字段提示</div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"saveSearchSettings\" :loading=\"loading\">保存设置</el-button>\n      </div>\n    </div>\n\n    <!-- 其他标签页的内容可以根据需要添加 -->\n    <div v-if=\"activeTab === 'workspace' && !loading\" class=\"form-container\">\n      <div class=\"setting-container\">\n        <div v-if=\"activeTab === 'workspace'\" class=\"workspace-setting-content\">\n          <div class=\"setting-label\">预测分析</div>\n          <el-radio-group v-model=\"predictMode\">\n            <el-radio :label=\"'follow'\">跟随工作区设置</el-radio>\n            <el-radio :label=\"'alwaysOn'\">始终打开</el-radio>\n            <el-radio :label=\"'alwaysOff'\">始终关闭</el-radio>\n          </el-radio-group>\n        </div>\n        <el-button class=\"save-btn\" type=\"primary\" @click=\"saveSetting\">保存设置</el-button>\n      </div>\n    </div>\n\n    <div v-if=\"activeTab === 'analysis' && !loading\" class=\"form-container\">\n      <!-- 基础解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">基础解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">异常值</div>\n          <el-switch v-model=\"analysisSettings.anomaly\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.anomaly\">展示数据中显著高于或低于其他值的点</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">最大值</div>\n          <el-switch v-model=\"analysisSettings.maximum\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">最小值</div>\n          <el-switch v-model=\"analysisSettings.minimum\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">平均值</div>\n          <el-switch v-model=\"analysisSettings.average\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">离散统计</div>\n          <el-switch v-model=\"analysisSettings.discrete\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n      </div>\n\n      <!-- 维度相关解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">维度相关解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">80/20构成</div>\n          <el-switch v-model=\"analysisSettings.bioTag\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.bioTag\">\n          判断数据是否符合二八分布,即这组数据中,最重要的维度只占其中一小部分,约20%,其余80%尽管是多数,却是次要的\n        </div>\n      </div>\n\n      <!-- 趋势类解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">趋势类解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">环比异常值</div>\n          <el-switch v-model=\"analysisSettings.chainAnomaly\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.chainAnomaly\">\n          展示数据中增速或降幅显著高于其他时间的点\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">周期性波动</div>\n          <el-switch v-model=\"analysisSettings.cyclicalFluctuation\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"saveAnalysisSettings\" :loading=\"loading\">保存设置</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getUserSettings, saveUserSettings } from '@/api/user_settings'\n\nexport default {\n  name: 'MyButton',\n  data() {\n    return {\n      activeTab: 'personal', // 默认显示个人设置标签\n      userId: '',\n      password: '********',\n      userIdLength: 0,\n      currentUserId: 1, // 当前登录用户ID，实际应该从登录状态获取\n      loading: false,\n      searchSettings: {\n        smartTips: true,\n        trySuggestions: true,\n        relatedSearch: true\n      },\n      analysisSettings: {\n        anomaly: true,\n        maximum: true,\n        minimum: true,\n        average: true,\n        discrete: true,\n        bioTag: true,\n        chainAnomaly: true,\n        cyclicalFluctuation: true\n      },\n      predictMode: 'follow',\n    }\n  },\n  watch: {\n    userId(val) {\n      this.userIdLength = val.length;\n    }\n  },\n  created() {\n    this.fetchUserSettings()\n  },\n  methods: {\n    async fetchUserSettings() {\n      this.loading = true\n      try {\n        const response = await getUserSettings(this.currentUserId)\n        if (response.code === 200) {\n          const { user, searchSettings, analysisSettings } = response.data\n          // 更新用户信息\n          this.userId = user.userId || user.nickName || ''\n          this.userIdLength = this.userId.length\n          // 更新设置\n          this.searchSettings = { ...this.searchSettings, ...searchSettings }\n          this.analysisSettings = { ...this.analysisSettings, ...analysisSettings }\n        }\n      } catch (error) {\n        this.$message.error('获取用户设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    async saveSearchSettings() {\n      this.loading = true\n      try {\n        const response = await saveUserSettings(this.currentUserId, {\n          searchSettings: this.searchSettings\n        })\n        if (response.code === 200) {\n          this.$message({\n            message: '搜索提示设置保存成功',\n            type: 'success'\n          });\n        }\n      } catch (error) {\n        this.$message.error('保存搜索设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    async saveAnalysisSettings() {\n      this.loading = true\n      try {\n        const response = await saveUserSettings(this.currentUserId, {\n          analysisSettings: this.analysisSettings\n        })\n        if (response.code === 200) {\n          this.$message({\n            message: '智能解析设置保存成功',\n            type: 'success'\n          });\n        }\n      } catch (error) {\n        this.$message.error('保存分析设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    saveSetting() {\n      this.$message.success('设置已保存')\n    },\n    async resetToDefault() {\n      this.$confirm('确定要重置为默认设置吗？这将覆盖您当前的所有个性化设置。', '确认重置', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        this.loading = true\n        try {\n          // 重置为默认设置\n          const defaultSearchSettings = {\n            smartTips: true,\n            trySuggestions: true,\n            relatedSearch: true\n          }\n          const defaultAnalysisSettings = {\n            anomaly: true,\n            maximum: true,\n            minimum: true,\n            average: true,\n            discrete: true,\n            bioTag: true,\n            chainAnomaly: true,\n            cyclicalFluctuation: true\n          }\n\n          // 保存默认设置到数据库\n          const response = await saveUserSettings(this.currentUserId, {\n            searchSettings: defaultSearchSettings,\n            analysisSettings: defaultAnalysisSettings\n          })\n\n          if (response.code === 200) {\n            // 更新本地数据\n            this.searchSettings = { ...defaultSearchSettings }\n            this.analysisSettings = { ...defaultAnalysisSettings }\n\n            this.$message({\n              message: '已重置为默认设置',\n              type: 'success'\n            })\n          }\n        } catch (error) {\n          this.$message.error('重置设置失败：' + (error.message || '未知错误'))\n        } finally {\n          this.loading = false\n        }\n      }).catch(() => {\n        // 用户取消操作\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  background-color: #fff;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.tab-container {\n  display: flex;\n  border-bottom: 1px solid #e6e6e6;\n  margin-bottom: 20px;\n}\n\n.tab-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  margin-right: 20px;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  font-weight: bold;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  width: 100%;\n  height: 2px;\n  background-color: #409EFF;\n}\n\n.form-container {\n  width: 100%;\n  max-width: 600px;\n}\n\n.form-item {\n  display: flex;\n  margin-bottom: 20px;\n  align-items: center;\n}\n\n.form-label {\n  width: 80px;\n  text-align: right;\n  padding-right: 10px;\n}\n\n.form-input {\n  flex: 1;\n}\n\n.form-count {\n  width: 60px;\n  text-align: right;\n  color: #999;\n  font-size: 12px;\n}\n\n.login-link {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.footer {\n  margin-top: 40px;\n  text-align: center;\n}\n\n.default-btn {\n  padding: 8px 20px;\n  border-radius: 4px;\n}\n\n/* 搜索提示设置样式 */\n.setting-section {\n  margin-bottom: 25px;\n}\n\n.setting-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.setting-title {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.setting-desc {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.placeholder {\n  color: #999;\n  font-size: 16px;\n  padding: 20px 0;\n}\n\n/* 智能解析设置样式 */\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.setting-item-title {\n  font-size: 14px;\n}\n\n.setting-item-desc {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-bottom: 15px;\n  margin-left: 10px;\n}\n\n/* 工作区设置样式 */\n.setting-container {\n  min-height: 100vh;\n  background: #fff;\n  padding: 40px 0 0 0;\n  position: relative;\n}\n\n.setting-tabs {\n  margin-left: 60px;\n}\n\n.workspace-setting-content {\n  margin-left: 60px;\n  margin-top: 32px;\n}\n\n.setting-label {\n  font-size: 16px;\n  margin-bottom: 18px;\n  font-weight: 400;\n}\n\n.el-radio-group {\n  display: flex;\n  flex-direction: column;\n  margin-top: 12px;\n}\n\n.el-radio {\n  margin-bottom: 12px;\n  font-size: 15px;\n}\n\n.save-btn {\n  position: fixed;\n  bottom: 32px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 120px;\n  height: 40px;\n  border-radius: 20px;\n  font-size: 16px;\n}\n</style>\n"]}]}