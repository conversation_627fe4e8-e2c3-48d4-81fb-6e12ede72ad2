{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\mybutton\\index.vue?vue&type=style&index=0&id=2246cf7b&scoped=true&lang=css", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\mybutton\\index.vue", "mtime": 1750058108898}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0TA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/mybutton", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"tab-container\">\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'personal' }\" @click=\"activeTab = 'personal'\">个人设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'workspace' }\" @click=\"activeTab = 'workspace'\">工作区设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'analysis' }\" @click=\"activeTab = 'analysis'\">智能解析设置</div>\n      <div class=\"tab-item\" :class=\"{ active: activeTab === 'search' }\" @click=\"activeTab = 'search'\">搜索提示设置</div>\n    </div>\n\n    <!-- 加载状态 -->\n    <el-skeleton v-if=\"loading\" :rows=\"6\" animated />\n\n    <!-- 个人设置 -->\n    <div v-if=\"activeTab === 'personal' && !loading\" class=\"form-container\">\n      <div class=\"form-item\">\n        <div class=\"form-label\">用户ID：</div>\n        <div class=\"form-input\">\n          <el-input v-model=\"userId\" placeholder=\"请输入用户ID\"></el-input>\n        </div>\n        <div class=\"form-count\">{{ userIdLength }} / 20</div>\n      </div>\n\n      <div class=\"form-item\">\n        <div class=\"form-label\">密码：</div>\n        <div class=\"form-input\">\n          <el-input v-model=\"password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\n        </div>\n      </div>\n\n      <div class=\"form-item\">\n        <div class=\"form-label\"></div>\n        <div class=\"form-input\">\n          <a href=\"javascript:;\" class=\"login-link\" @click=\"goToLogin\">返回登录 ></a>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"resetToDefault\" :loading=\"loading\">设为默认</el-button>\n      </div>\n    </div>\n\n    <!-- 搜索提示设置 -->\n    <div v-if=\"activeTab === 'search' && !loading\" class=\"form-container\">\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">智能提示</div>\n          <el-switch v-model=\"searchSettings.smartTips\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">根据您的搜索行为切换不同的提示词</div>\n      </div>\n\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">默认字段提示</div>\n          <el-switch v-model=\"searchSettings.trySuggestions\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">任何情况对焦搜索框都给出字段提示，可手动切换到问题提示</div>\n      </div>\n\n      <div class=\"setting-section\">\n        <div class=\"setting-header\">\n          <div class=\"setting-title\">默认问题提示</div>\n          <el-switch v-model=\"searchSettings.relatedSearch\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-desc\">任何情况对焦搜索框都给出问题提示，可手动切换到字段提示</div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"saveSearchSettings\" :loading=\"loading\">保存设置</el-button>\n      </div>\n    </div>\n\n    <!-- 其他标签页的内容可以根据需要添加 -->\n    <div v-if=\"activeTab === 'workspace' && !loading\" class=\"form-container\">\n      <div class=\"setting-container\">\n        <div v-if=\"activeTab === 'workspace'\" class=\"workspace-setting-content\">\n          <div class=\"setting-label\">预测分析</div>\n          <el-radio-group v-model=\"predictMode\">\n            <el-radio :label=\"'follow'\">跟随工作区设置</el-radio>\n            <el-radio :label=\"'alwaysOn'\">始终打开</el-radio>\n            <el-radio :label=\"'alwaysOff'\">始终关闭</el-radio>\n          </el-radio-group>\n        </div>\n        <el-button class=\"save-btn\" type=\"primary\" @click=\"saveSetting\">保存设置</el-button>\n      </div>\n    </div>\n\n    <div v-if=\"activeTab === 'analysis' && !loading\" class=\"form-container\">\n      <!-- 基础解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">基础解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">异常值</div>\n          <el-switch v-model=\"analysisSettings.anomaly\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.anomaly\">展示数据中显著高于或低于其他值的点</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">最大值</div>\n          <el-switch v-model=\"analysisSettings.maximum\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">最小值</div>\n          <el-switch v-model=\"analysisSettings.minimum\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">平均值</div>\n          <el-switch v-model=\"analysisSettings.average\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">离散统计</div>\n          <el-switch v-model=\"analysisSettings.discrete\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n      </div>\n\n      <!-- 维度相关解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">维度相关解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">80/20构成</div>\n          <el-switch v-model=\"analysisSettings.bioTag\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.bioTag\">\n          判断数据是否符合二八分布,即这组数据中,最重要的维度只占其中一小部分,约20%,其余80%尽管是多数,却是次要的\n        </div>\n      </div>\n\n      <!-- 趋势类解析部分 -->\n      <div class=\"setting-section\">\n        <div class=\"setting-title\">趋势类解析</div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">环比异常值</div>\n          <el-switch v-model=\"analysisSettings.chainAnomaly\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n        <div class=\"setting-item-desc\" v-if=\"analysisSettings.chainAnomaly\">\n          展示数据中增速或降幅显著高于其他时间的点\n        </div>\n\n        <div class=\"setting-item\">\n          <div class=\"setting-item-title\">周期性波动</div>\n          <el-switch v-model=\"analysisSettings.cyclicalFluctuation\" active-color=\"#409EFF\" inactive-color=\"#DCDFE6\"></el-switch>\n        </div>\n      </div>\n\n      <div class=\"footer\">\n        <el-button type=\"primary\" class=\"default-btn\" @click=\"saveAnalysisSettings\" :loading=\"loading\">保存设置</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getUserSettings, saveUserSettings } from '@/api/user_settings'\n\nexport default {\n  name: 'MyButton',\n  data() {\n    return {\n      activeTab: 'personal', // 默认显示个人设置标签\n      userId: '',\n      password: '********',\n      userIdLength: 0,\n      currentUserId: 1, // 当前登录用户ID，实际应该从登录状态获取\n      loading: false,\n      searchSettings: {\n        smartTips: true,\n        trySuggestions: true,\n        relatedSearch: true\n      },\n      analysisSettings: {\n        anomaly: true,\n        maximum: true,\n        minimum: true,\n        average: true,\n        discrete: true,\n        bioTag: true,\n        chainAnomaly: true,\n        cyclicalFluctuation: true\n      },\n      predictMode: 'follow',\n    }\n  },\n  watch: {\n    userId(val) {\n      this.userIdLength = val.length;\n    }\n  },\n  created() {\n    this.fetchUserSettings()\n  },\n  methods: {\n    async fetchUserSettings() {\n      this.loading = true\n      try {\n        const response = await getUserSettings(this.currentUserId)\n        if (response.code === 200) {\n          const { user, searchSettings, analysisSettings } = response.data\n          // 更新用户信息\n          this.userId = user.userId || user.nickName || ''\n          this.userIdLength = this.userId.length\n          // 更新设置\n          this.searchSettings = { ...this.searchSettings, ...searchSettings }\n          this.analysisSettings = { ...this.analysisSettings, ...analysisSettings }\n        }\n      } catch (error) {\n        this.$message.error('获取用户设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    async saveSearchSettings() {\n      this.loading = true\n      try {\n        const response = await saveUserSettings(this.currentUserId, {\n          searchSettings: this.searchSettings\n        })\n        if (response.code === 200) {\n          this.$message({\n            message: '搜索提示设置保存成功',\n            type: 'success'\n          });\n        }\n      } catch (error) {\n        this.$message.error('保存搜索设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    async saveAnalysisSettings() {\n      this.loading = true\n      try {\n        const response = await saveUserSettings(this.currentUserId, {\n          analysisSettings: this.analysisSettings\n        })\n        if (response.code === 200) {\n          this.$message({\n            message: '智能解析设置保存成功',\n            type: 'success'\n          });\n        }\n      } catch (error) {\n        this.$message.error('保存分析设置失败：' + (error.message || '未知错误'))\n      } finally {\n        this.loading = false\n      }\n    },\n    saveSetting() {\n      this.$message.success('设置已保存')\n    },\n    async resetToDefault() {\n      this.$confirm('确定要重置为默认设置吗？这将覆盖您当前的所有个性化设置。', '确认重置', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        this.loading = true\n        try {\n          // 重置为默认设置\n          const defaultSearchSettings = {\n            smartTips: true,\n            trySuggestions: true,\n            relatedSearch: true\n          }\n          const defaultAnalysisSettings = {\n            anomaly: true,\n            maximum: true,\n            minimum: true,\n            average: true,\n            discrete: true,\n            bioTag: true,\n            chainAnomaly: true,\n            cyclicalFluctuation: true\n          }\n\n          // 保存默认设置到数据库\n          const response = await saveUserSettings(this.currentUserId, {\n            searchSettings: defaultSearchSettings,\n            analysisSettings: defaultAnalysisSettings\n          })\n\n          if (response.code === 200) {\n            // 更新本地数据\n            this.searchSettings = { ...defaultSearchSettings }\n            this.analysisSettings = { ...defaultAnalysisSettings }\n\n            this.$message({\n              message: '已重置为默认设置',\n              type: 'success'\n            })\n          }\n        } catch (error) {\n          this.$message.error('重置设置失败：' + (error.message || '未知错误'))\n        } finally {\n          this.loading = false\n        }\n      }).catch(() => {\n        // 用户取消操作\n      })\n    },\n    goToLogin() {\n      // 跳转到登录页面\n      this.$router.push('/login')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  background-color: #fff;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.tab-container {\n  display: flex;\n  border-bottom: 1px solid #e6e6e6;\n  margin-bottom: 20px;\n}\n\n.tab-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  margin-right: 20px;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #409EFF;\n  font-weight: bold;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  width: 100%;\n  height: 2px;\n  background-color: #409EFF;\n}\n\n.form-container {\n  width: 100%;\n  max-width: 600px;\n}\n\n.form-item {\n  display: flex;\n  margin-bottom: 20px;\n  align-items: center;\n}\n\n.form-label {\n  width: 80px;\n  text-align: right;\n  padding-right: 10px;\n}\n\n.form-input {\n  flex: 1;\n}\n\n.form-count {\n  width: 60px;\n  text-align: right;\n  color: #999;\n  font-size: 12px;\n}\n\n.login-link {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.footer {\n  margin-top: 40px;\n  text-align: center;\n}\n\n.default-btn {\n  padding: 8px 20px;\n  border-radius: 4px;\n}\n\n/* 搜索提示设置样式 */\n.setting-section {\n  margin-bottom: 25px;\n}\n\n.setting-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.setting-title {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.setting-desc {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.placeholder {\n  color: #999;\n  font-size: 16px;\n  padding: 20px 0;\n}\n\n/* 智能解析设置样式 */\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.setting-item-title {\n  font-size: 14px;\n}\n\n.setting-item-desc {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-bottom: 15px;\n  margin-left: 10px;\n}\n\n/* 工作区设置样式 */\n.setting-container {\n  min-height: 100vh;\n  background: #fff;\n  padding: 40px 0 0 0;\n  position: relative;\n}\n\n.setting-tabs {\n  margin-left: 60px;\n}\n\n.workspace-setting-content {\n  margin-left: 60px;\n  margin-top: 32px;\n}\n\n.setting-label {\n  font-size: 16px;\n  margin-bottom: 18px;\n  font-weight: 400;\n}\n\n.el-radio-group {\n  display: flex;\n  flex-direction: column;\n  margin-top: 12px;\n}\n\n.el-radio {\n  margin-bottom: 12px;\n  font-size: 15px;\n}\n\n.save-btn {\n  position: fixed;\n  bottom: 32px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 120px;\n  height: 40px;\n  border-radius: 20px;\n  font-size: 16px;\n}\n</style>\n"]}]}