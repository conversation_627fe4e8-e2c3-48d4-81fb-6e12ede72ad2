{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\store\\modules\\user.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "_auth", "_validate", "_profile", "_interopRequireDefault", "user", "state", "token", "getToken", "id", "name", "avatar", "roles", "permissions", "mutations", "SET_TOKEN", "SET_ID", "SET_NAME", "SET_AVATAR", "SET_ROLES", "SET_PERMISSIONS", "actions", "<PERSON><PERSON>", "_ref", "userInfo", "commit", "username", "trim", "password", "code", "uuid", "Promise", "resolve", "reject", "login", "then", "res", "setToken", "catch", "error", "GetInfo", "_ref2", "getInfo", "isHttp", "isEmpty", "defAva", "process", "env", "VUE_APP_BASE_API", "length", "userId", "userName", "LogOut", "_ref3", "logout", "removeToken", "FedLogOut", "_ref4", "_default", "exports", "default"], "sources": ["D:/jgst/dataeyeui/src/store/modules/user.js"], "sourcesContent": ["import { login, logout, getInfo } from '@/api/login'\nimport { getToken, setToken, removeToken } from '@/utils/auth'\nimport { isHttp, isEmpty } from \"@/utils/validate\"\nimport defAva from '@/assets/images/profile.jpg'\n\nconst user = {\n  state: {\n    token: getToken(),\n    id: '',\n    name: '',\n    avatar: '',\n    roles: [],\n    permissions: []\n  },\n\n  mutations: {\n    SET_TOKEN: (state, token) => {\n      state.token = token\n    },\n    SET_ID: (state, id) => {\n      state.id = id\n    },\n    SET_NAME: (state, name) => {\n      state.name = name\n    },\n    SET_AVATAR: (state, avatar) => {\n      state.avatar = avatar\n    },\n    SET_ROLES: (state, roles) => {\n      state.roles = roles\n    },\n    SET_PERMISSIONS: (state, permissions) => {\n      state.permissions = permissions\n    }\n  },\n\n  actions: {\n    // 登录\n    Login({ commit }, userInfo) {\n      const username = userInfo.username.trim()\n      const password = userInfo.password\n      const code = userInfo.code\n      const uuid = userInfo.uuid\n      return new Promise((resolve, reject) => {\n        login(username, password, code, uuid).then(res => {\n          setToken(res.token)\n          commit('SET_TOKEN', res.token)\n          resolve()\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 获取用户信息\n    GetInfo({ commit, state }) {\n      return new Promise((resolve, reject) => {\n        getInfo().then(res => {\n          const user = res.user\n          let avatar = user.avatar || \"\"\n          if (!isHttp(avatar)) {\n            avatar = (isEmpty(avatar)) ? defAva : process.env.VUE_APP_BASE_API + avatar\n          }\n          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组\n            commit('SET_ROLES', res.roles)\n            commit('SET_PERMISSIONS', res.permissions)\n          } else {\n            commit('SET_ROLES', ['ROLE_DEFAULT'])\n          }\n          commit('SET_ID', user.userId)\n          commit('SET_NAME', user.userName)\n          commit('SET_AVATAR', avatar)\n          resolve(res)\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 退出系统\n    LogOut({ commit, state }) {\n      return new Promise((resolve, reject) => {\n        logout(state.token).then(() => {\n          commit('SET_TOKEN', '')\n          commit('SET_ROLES', [])\n          commit('SET_PERMISSIONS', [])\n          removeToken()\n          resolve()\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 前端 登出\n    FedLogOut({ commit }) {\n      return new Promise(resolve => {\n        commit('SET_TOKEN', '')\n        removeToken()\n        resolve()\n      })\n    }\n  }\n}\n\nexport default user\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAEA,IAAMK,IAAI,GAAG;EACXC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAAC,cAAQ,EAAC,CAAC;IACjBC,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE;EACf,CAAC;EAEDC,SAAS,EAAE;IACTC,SAAS,EAAE,SAAXA,SAASA,CAAGT,KAAK,EAAEC,KAAK,EAAK;MAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDS,MAAM,EAAE,SAARA,MAAMA,CAAGV,KAAK,EAAEG,EAAE,EAAK;MACrBH,KAAK,CAACG,EAAE,GAAGA,EAAE;IACf,CAAC;IACDQ,QAAQ,EAAE,SAAVA,QAAQA,CAAGX,KAAK,EAAEI,IAAI,EAAK;MACzBJ,KAAK,CAACI,IAAI,GAAGA,IAAI;IACnB,CAAC;IACDQ,UAAU,EAAE,SAAZA,UAAUA,CAAGZ,KAAK,EAAEK,MAAM,EAAK;MAC7BL,KAAK,CAACK,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDQ,SAAS,EAAE,SAAXA,SAASA,CAAGb,KAAK,EAAEM,KAAK,EAAK;MAC3BN,KAAK,CAACM,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDQ,eAAe,EAAE,SAAjBA,eAAeA,CAAGd,KAAK,EAAEO,WAAW,EAAK;MACvCP,KAAK,CAACO,WAAW,GAAGA,WAAW;IACjC;EACF,CAAC;EAEDQ,OAAO,EAAE;IACP;IACAC,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAAaC,QAAQ,EAAE;MAAA,IAApBC,MAAM,GAAAF,IAAA,CAANE,MAAM;MACZ,IAAMC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC;MACzC,IAAMC,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;MAClC,IAAMC,IAAI,GAAGL,QAAQ,CAACK,IAAI;MAC1B,IAAMC,IAAI,GAAGN,QAAQ,CAACM,IAAI;MAC1B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAC,YAAK,EAACR,QAAQ,EAAEE,QAAQ,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAACK,IAAI,CAAC,UAAAC,GAAG,EAAI;UAChD,IAAAC,cAAQ,EAACD,GAAG,CAAC7B,KAAK,CAAC;UACnBkB,MAAM,CAAC,WAAW,EAAEW,GAAG,CAAC7B,KAAK,CAAC;UAC9ByB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAoB;MAAA,IAAjBhB,MAAM,GAAAgB,KAAA,CAANhB,MAAM;QAAEnB,KAAK,GAAAmC,KAAA,CAALnC,KAAK;MACrB,OAAO,IAAIyB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAS,cAAO,EAAC,CAAC,CAACP,IAAI,CAAC,UAAAC,GAAG,EAAI;UACpB,IAAM/B,IAAI,GAAG+B,GAAG,CAAC/B,IAAI;UACrB,IAAIM,MAAM,GAAGN,IAAI,CAACM,MAAM,IAAI,EAAE;UAC9B,IAAI,CAAC,IAAAgC,gBAAM,EAAChC,MAAM,CAAC,EAAE;YACnBA,MAAM,GAAI,IAAAiC,iBAAO,EAACjC,MAAM,CAAC,GAAIkC,gBAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAAGrC,MAAM;UAC7E;UACA,IAAIyB,GAAG,CAACxB,KAAK,IAAIwB,GAAG,CAACxB,KAAK,CAACqC,MAAM,GAAG,CAAC,EAAE;YAAE;YACvCxB,MAAM,CAAC,WAAW,EAAEW,GAAG,CAACxB,KAAK,CAAC;YAC9Ba,MAAM,CAAC,iBAAiB,EAAEW,GAAG,CAACvB,WAAW,CAAC;UAC5C,CAAC,MAAM;YACLY,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;UACvC;UACAA,MAAM,CAAC,QAAQ,EAAEpB,IAAI,CAAC6C,MAAM,CAAC;UAC7BzB,MAAM,CAAC,UAAU,EAAEpB,IAAI,CAAC8C,QAAQ,CAAC;UACjC1B,MAAM,CAAC,YAAY,EAAEd,MAAM,CAAC;UAC5BqB,OAAO,CAACI,GAAG,CAAC;QACd,CAAC,CAAC,CAACE,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAa,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAoB;MAAA,IAAjB5B,MAAM,GAAA4B,KAAA,CAAN5B,MAAM;QAAEnB,KAAK,GAAA+C,KAAA,CAAL/C,KAAK;MACpB,OAAO,IAAIyB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAAqB,aAAM,EAAChD,KAAK,CAACC,KAAK,CAAC,CAAC4B,IAAI,CAAC,YAAM;UAC7BV,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvBA,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;UAC7B,IAAA8B,iBAAW,EAAC,CAAC;UACbvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAC,KAAK,EAAI;UAChBN,MAAM,CAACM,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACAiB,SAAS,WAATA,SAASA,CAAAC,KAAA,EAAa;MAAA,IAAVhC,MAAM,GAAAgC,KAAA,CAANhC,MAAM;MAChB,OAAO,IAAIM,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5BP,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvB,IAAA8B,iBAAW,EAAC,CAAC;QACbvB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAA,IAAA0B,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcvD,IAAI", "ignoreList": []}]}