from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from config.get_db import get_db
from alerts_model import Alert

router = APIRouter()

@router.get("/api/alerts")
async def get_alerts(
    pageNum: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db)
):
    offset = (pageNum - 1) * pageSize
    stmt = select(Alert).offset(offset).limit(pageSize)
    total_stmt = select(func.count(Alert.id))
    result = await db.execute(stmt)
    items = result.scalars().all()
    total_result = await db.execute(total_stmt)
    total = total_result.scalar()
    def serialize(alert):
        def format_datetime(dt):
            if not dt:
                return ''
            if isinstance(dt, str):
                return dt.replace('T', ' ').split('.')[0]
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        return {
            'id': alert.id,
            'name': alert.alert_name,  # 名称
            'level': alert.alert_type, # 级别
            'created_at': format_datetime(alert.created_at), # 创建时间
            'createTime': format_datetime(alert.created_at), # 前端期望的字段
            'project_id': alert.project_id,
            'card_uid': alert.card_uid,
            'alert_type': alert.alert_type,
            'alert_name': alert.alert_name,
            'alert_condition': alert.alert_condition,
            'is_active': alert.is_active,
            'last_triggered_at': format_datetime(alert.last_triggered_at),
            'updated_at': format_datetime(alert.updated_at),
            'create_by': alert.create_by,
            'update_by': alert.update_by,
        }
    return {
        "items": [serialize(alert) for alert in items],
        "total": total,
        "pageNum": pageNum,
        "pageSize": pageSize
    }

@router.get("/api/alerts/{alert_id}")
async def get_alert_detail(alert_id: int, db: AsyncSession = Depends(get_db)):
    result = await db.execute(select(Alert).where(Alert.id == alert_id))
    alert = result.scalars().first()
    if not alert:
        return {"code": 404, "msg": "未找到该告警"}
    return {
        "id": alert.id,
        "name": alert.alert_name,
        "level": alert.alert_type,
        "created_at": alert.created_at.strftime('%Y-%m-%d %H:%M:%S') if alert.created_at else None,
        "project_id": alert.project_id,
        "card_uid": alert.card_uid,
        "alert_type": alert.alert_type,
        "alert_name": alert.alert_name,
        "alert_condition": alert.alert_condition,
        "is_active": alert.is_active,
        "last_triggered_at": alert.last_triggered_at.strftime('%Y-%m-%d %H:%M:%S') if alert.last_triggered_at else None,
        "updated_at": alert.updated_at.strftime('%Y-%m-%d %H:%M:%S') if alert.updated_at else None,
        "create_by": alert.create_by,
        "update_by": alert.update_by,
    }

@router.delete("/api/alerts/{alert_id}")
async def delete_alert(alert_id: int, db: AsyncSession = Depends(get_db)):
    result = await db.execute(select(Alert).where(Alert.id == alert_id))
    alert = result.scalars().first()
    if not alert:
        raise HTTPException(status_code=404, detail="未找到该告警")
    await db.delete(alert)
    await db.commit()
    return {"success": True, "msg": "删除成功"}
