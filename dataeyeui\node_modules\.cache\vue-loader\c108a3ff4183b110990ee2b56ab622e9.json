{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\error\\401.vue?vue&type=style&index=0&id=099c4504&lang=scss&scoped=true", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\error\\401.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749172154830}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZXJyUGFnZS1jb250YWluZXIgewogIHdpZHRoOiA4MDBweDsKICBtYXgtd2lkdGg6IDEwMCU7CiAgbWFyZ2luOiAxMDBweCBhdXRvOwogIC5wYW4tYmFjay1idG4gewogICAgYmFja2dyb3VuZDogIzAwODQ4OTsKICAgIGNvbG9yOiAjZmZmOwogICAgYm9yZGVyOiBub25lIWltcG9ydGFudDsKICB9CiAgLnBhbi1naWYgewogICAgbWFyZ2luOiAwIGF1dG87CiAgICBkaXNwbGF5OiBibG9jazsKICB9CiAgLnBhbi1pbWcgewogICAgZGlzcGxheTogYmxvY2s7CiAgICBtYXJnaW46IDAgYXV0bzsKICAgIHdpZHRoOiAxMDAlOwogIH0KICAudGV4dC1qdW1ibyB7CiAgICBmb250LXNpemU6IDYwcHg7CiAgICBmb250LXdlaWdodDogNzAwOwogICAgY29sb3I6ICM0ODQ4NDg7CiAgfQogIC5saXN0LXVuc3R5bGVkIHsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGxpIHsKICAgICAgcGFkZGluZy1ib3R0b206IDVweDsKICAgIH0KICAgIGEgewogICAgICBjb2xvcjogIzAwODQ4OTsKICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogICAgICAmOmhvdmVyIHsKICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["401.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "401.vue", "sourceRoot": "src/views/error", "sourcesContent": ["<template>\n  <div class=\"errPage-container\">\n    <el-button icon=\"arrow-left\" class=\"pan-back-btn\" @click=\"back\">\n      返回\n    </el-button>\n    <el-row>\n      <el-col :span=\"12\">\n        <h1 class=\"text-jumbo text-ginormous\">\n          401错误!\n        </h1>\n        <h2>您没有访问权限！</h2>\n        <h6>对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面</h6>\n        <ul class=\"list-unstyled\">\n          <li class=\"link-type\">\n            <router-link to=\"/\">\n              回首页\n            </router-link>\n          </li>\n        </ul>\n      </el-col>\n      <el-col :span=\"12\">\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport errGif from '@/assets/401_images/401.gif'\n\nexport default {\n  name: 'Page401',\n  data() {\n    return {\n      errGif: errGif + '?' + +new Date()\n    }\n  },\n  methods: {\n    back() {\n      if (this.$route.query.noGoBack) {\n        this.$router.push({ path: '/' })\n      } else {\n        this.$router.go(-1)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .errPage-container {\n    width: 800px;\n    max-width: 100%;\n    margin: 100px auto;\n    .pan-back-btn {\n      background: #008489;\n      color: #fff;\n      border: none!important;\n    }\n    .pan-gif {\n      margin: 0 auto;\n      display: block;\n    }\n    .pan-img {\n      display: block;\n      margin: 0 auto;\n      width: 100%;\n    }\n    .text-jumbo {\n      font-size: 60px;\n      font-weight: 700;\n      color: #484848;\n    }\n    .list-unstyled {\n      font-size: 14px;\n      li {\n        padding-bottom: 5px;\n      }\n      a {\n        color: #008489;\n        text-decoration: none;\n        &:hover {\n          text-decoration: underline;\n        }\n      }\n    }\n  }\n</style>\n"]}]}