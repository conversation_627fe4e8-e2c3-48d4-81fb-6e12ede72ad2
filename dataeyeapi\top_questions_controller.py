from fastapi import APIRouter, Query

router = APIRouter()

@router.get("/api/top_questions")
def get_top_questions(pageNum: int = Query(1), pageSize: int = Query(10)):
    # 示例返回，实际应替换为数据库查询
    return {
        "items": [
            {"id": 1, "question": "去年营业额前十的门店同比情况"},
            {"id": 2, "question": "去年的营业额"},
            {"id": 3, "question": "去年门店是(三里屯)品牌是(耐克)门店面积营业额"},
            {"id": 4, "question": "去年营业额前十的门店同比情况会员客流上海"},
            {"id": 5, "question": "今年Q1营业额每天"}
        ],
        "total": 5,
        "pageNum": pageNum,
        "pageSize": pageSize
    }
