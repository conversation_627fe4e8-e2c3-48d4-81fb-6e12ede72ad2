from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc
from config.get_db import get_db
from top_questions_model import TopQuestion

router = APIRouter()

@router.get("/api/top_questions")
async def get_top_questions(
    pageNum: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    category: str = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """获取热门问题列表"""
    try:
        # 构建查询
        stmt = select(TopQuestion).where(TopQuestion.is_active == True)

        # 按分类筛选
        if category:
            stmt = stmt.where(TopQuestion.category == category)

        # 按搜索次数和排序权重排序
        stmt = stmt.order_by(desc(TopQuestion.sort_order), desc(TopQuestion.search_count))

        # 分页
        offset = (pageNum - 1) * pageSize
        stmt = stmt.offset(offset).limit(pageSize)

        # 执行查询
        result = await db.execute(stmt)
        items = result.scalars().all()

        # 获取总数
        count_stmt = select(TopQuestion).where(TopQuestion.is_active == True)
        if category:
            count_stmt = count_stmt.where(TopQuestion.category == category)

        count_result = await db.execute(count_stmt)
        total = len(count_result.scalars().all())

        # 序列化数据
        def serialize(item):
            def format_datetime(dt):
                if not dt:
                    return ''
                if isinstance(dt, str):
                    return dt.replace('T', ' ').split('.')[0]
                return dt.strftime('%Y-%m-%d %H:%M:%S')

            return {
                'id': item.id,
                'question': item.question,
                'content': item.question,  # 前端期望的字段
                'search_count': item.search_count,
                'category': item.category,
                'tags': item.tags,
                'is_active': item.is_active,
                'sort_order': item.sort_order,
                'created_at': format_datetime(item.created_at),
                'updated_at': format_datetime(item.updated_at)
            }

        return {
            "items": [serialize(item) for item in items],
            "total": total,
            "pageNum": pageNum,
            "pageSize": pageSize
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门问题失败: {str(e)}")

@router.post("/api/top_questions/{question_id}/increment")
async def increment_search_count(question_id: int, db: AsyncSession = Depends(get_db)):
    """增加问题搜索次数"""
    try:
        # 查找问题
        stmt = select(TopQuestion).where(TopQuestion.id == question_id)
        result = await db.execute(stmt)
        question = result.scalar_one_or_none()

        if not question:
            raise HTTPException(status_code=404, detail="问题不存在")

        # 增加搜索次数
        question.search_count += 1
        await db.commit()

        return {
            "code": 200,
            "message": "搜索次数更新成功",
            "data": {
                "id": question.id,
                "search_count": question.search_count
            }
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新搜索次数失败: {str(e)}")

@router.get("/api/top_questions/categories")
async def get_question_categories(db: AsyncSession = Depends(get_db)):
    """获取问题分类列表"""
    try:
        stmt = select(TopQuestion.category).distinct().where(TopQuestion.is_active == True)
        result = await db.execute(stmt)
        categories = [row[0] for row in result.fetchall() if row[0]]

        return {
            "code": 200,
            "message": "success",
            "data": categories
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")
