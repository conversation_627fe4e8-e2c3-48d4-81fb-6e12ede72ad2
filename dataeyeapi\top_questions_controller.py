from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc
from config.get_db import get_db
from top_questions_model import TopQuestion

router = APIRouter()

@router.get("/api/top_questions")
async def get_top_questions(
    pageNum: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    category: str = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """获取热门问题列表"""
    try:
        # 临时返回固定数据，避免数据库表不存在的问题
        sample_data = [
            {"id": 1, "question": "门店营业额前十", "content": "门店营业额前十", "search_count": 150, "category": "销售分析"},
            {"id": 2, "question": "今日汇率与昨日汇率", "content": "今日汇率与昨日汇率", "search_count": 120, "category": "金融数据"},
            {"id": 3, "question": "上海房产价格走势", "content": "上海房产价格走势", "search_count": 100, "category": "房地产"},
            {"id": 4, "question": "产品销售量统计", "content": "产品销售量统计", "search_count": 95, "category": "销售分析"},
            {"id": 5, "question": "最近的股票市场行情", "content": "最近的股票市场行情", "search_count": 90, "category": "金融数据"},
            {"id": 6, "question": "用户活跃度分析", "content": "用户活跃度分析", "search_count": 85, "category": "用户分析"},
            {"id": 7, "question": "月度收入报表", "content": "月度收入报表", "search_count": 80, "category": "财务分析"},
            {"id": 8, "question": "客户满意度调查", "content": "客户满意度调查", "search_count": 75, "category": "客户分析"}
        ]

        # 分页处理
        start_idx = (pageNum - 1) * pageSize
        end_idx = start_idx + pageSize
        items = sample_data[start_idx:end_idx]

        return {
            "items": items,
            "total": len(sample_data),
            "pageNum": pageNum,
            "pageSize": pageSize
        }

        # TODO: 数据库表创建后恢复以下代码
        # # 构建查询
        # stmt = select(TopQuestion).where(TopQuestion.is_active == True)
        #
        # # 按分类筛选
        # if category:
        #     stmt = stmt.where(TopQuestion.category == category)
        #
        # # 按搜索次数和排序权重排序
        # stmt = stmt.order_by(desc(TopQuestion.sort_order), desc(TopQuestion.search_count))
        #
        # # 分页
        # offset = (pageNum - 1) * pageSize
        # stmt = stmt.offset(offset).limit(pageSize)
        #
        # # 执行查询
        # result = await db.execute(stmt)
        # items = result.scalars().all()
        #
        # # 获取总数
        # count_stmt = select(TopQuestion).where(TopQuestion.is_active == True)
        # if category:
        #     count_stmt = count_stmt.where(TopQuestion.category == category)
        #
        # count_result = await db.execute(count_stmt)
        # total = len(count_result.scalars().all())
        #
        # # 序列化数据
        # def serialize(item):
        #     def format_datetime(dt):
        #         if not dt:
        #             return ''
        #         if isinstance(dt, str):
        #             return dt.replace('T', ' ').split('.')[0]
        #         return dt.strftime('%Y-%m-%d %H:%M:%S')
        #
        #     return {
        #         'id': item.id,
        #         'question': item.question,
        #         'content': item.question,  # 前端期望的字段
        #         'search_count': item.search_count,
        #         'category': item.category,
        #         'tags': item.tags,
        #         'is_active': item.is_active,
        #         'sort_order': item.sort_order,
        #         'created_at': format_datetime(item.created_at),
        #         'updated_at': format_datetime(item.updated_at)
        #     }
        #
        # return {
        #     "items": [serialize(item) for item in items],
        #     "total": total,
        #     "pageNum": pageNum,
        #     "pageSize": pageSize
        # }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门问题失败: {str(e)}")

@router.post("/api/top_questions/{question_id}/increment")
async def increment_search_count(question_id: int, db: AsyncSession = Depends(get_db)):
    """增加问题搜索次数"""
    try:
        # 临时返回成功响应
        return {
            "code": 200,
            "message": "搜索次数更新成功",
            "data": {
                "id": question_id,
                "search_count": 1
            }
        }

        # TODO: 数据库表创建后恢复以下代码
        # # 查找问题
        # stmt = select(TopQuestion).where(TopQuestion.id == question_id)
        # result = await db.execute(stmt)
        # question = result.scalar_one_or_none()
        #
        # if not question:
        #     raise HTTPException(status_code=404, detail="问题不存在")
        #
        # # 增加搜索次数
        # question.search_count += 1
        # await db.commit()
        #
        # return {
        #     "code": 200,
        #     "message": "搜索次数更新成功",
        #     "data": {
        #         "id": question.id,
        #         "search_count": question.search_count
        #     }
        # }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新搜索次数失败: {str(e)}")

@router.get("/api/top_questions/categories")
async def get_question_categories(db: AsyncSession = Depends(get_db)):
    """获取问题分类列表"""
    try:
        # 临时返回固定分类数据
        categories = ["销售分析", "金融数据", "房地产", "用户分析", "财务分析", "客户分析"]

        return {
            "code": 200,
            "message": "success",
            "data": categories
        }

        # TODO: 数据库表创建后恢复以下代码
        # stmt = select(TopQuestion.category).distinct().where(TopQuestion.is_active == 1)
        # result = await db.execute(stmt)
        # categories = [row[0] for row in result.fetchall() if row[0]]
        #
        # return {
        #     "code": 200,
        #     "message": "success",
        #     "data": categories
        # }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")
