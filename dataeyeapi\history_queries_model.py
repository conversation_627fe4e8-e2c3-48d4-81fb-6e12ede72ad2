from sqlalchemy import Column, BigInteger, String, Text, DateTime, Integer
from config.database import Base
import datetime

class HistoryQuery(Base):
    __tablename__ = 'history_queries'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='记录ID')
    project_id = Column(BigInteger, nullable=False, comment='项目ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    identity = Column(String(32), nullable=False, comment='查询唯一标识(MD5哈希)')
    tokens = Column(Text, comment='查询标记(JSON格式)')
    star = Column(Integer, default=0, comment='是否收藏')
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment='创建时间')
