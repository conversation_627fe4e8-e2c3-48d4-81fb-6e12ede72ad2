{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\router\\mybutton.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\router\\mybutton.js", "mtime": 1747828482000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVXaWxkY2FyZC5qcyIpKTsKdmFyIF9sYXlvdXQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvbGF5b3V0IikpOwp2YXIgbXlidXR0b25Sb3V0ZXIgPSB7CiAgcGF0aDogJy9teWJ1dHRvbicsCiAgY29tcG9uZW50OiBfbGF5b3V0LmRlZmF1bHQsCiAgcmVkaXJlY3Q6ICdub1JlZGlyZWN0JywKICBuYW1lOiAnTXlCdXR0b24nLAogIG1ldGE6IHsKICAgIHRpdGxlOiAn5oiR55qEJywKICAgIGljb246ICd1c2VyJwogIH0sCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAnaW5kZXgnLAogICAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL215YnV0dG9uL2luZGV4JykpOwogICAgICB9KTsKICAgIH0sCiAgICBuYW1lOiAnTXlCdXR0b25JbmRleCcsCiAgICBtZXRhOiB7CiAgICAgIHRpdGxlOiAn6K6+572uJywKICAgICAgaWNvbjogJ2Rhc2hib2FyZCcKICAgIH0KICB9XQp9Owp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBteWJ1dHRvblJvdXRlcjs="}, {"version": 3, "names": ["_layout", "_interopRequireDefault", "require", "mybutton<PERSON>outer", "path", "component", "Layout", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "_default", "exports"], "sources": ["D:/jgst/dataeyeui/src/router/mybutton.js"], "sourcesContent": ["import Layout from '@/layout'\n\nconst mybuttonRouter = {\n  path: '/mybutton',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'My<PERSON>utt<PERSON>',\n  meta: {\n    title: '我的',\n    icon: 'user'\n  },\n  children: [\n    {\n      path: 'index',\n      component: () => import('@/views/mybutton/index'),\n      name: 'MyButtonIndex',\n      meta: { title: '设置', icon: 'dashboard' }\n    }\n  ]\n}\n\nexport default mybuttonRouter\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,cAAc,GAAG;EACrBC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,eAAM;EACjBC,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;IACJC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDM,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAY;EACzC,CAAC;AAEL,CAAC;AAAA,IAAAO,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAEcd,cAAc", "ignoreList": []}]}