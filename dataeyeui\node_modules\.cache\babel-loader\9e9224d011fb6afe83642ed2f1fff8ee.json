{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\router\\my.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\router\\my.js", "mtime": 1747828520000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovamdzdC9kYXRhZXlldWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVXaWxkY2FyZC5qcyIpKTsKdmFyIF9sYXlvdXQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvbGF5b3V0IikpOwp2YXIgbXlSb3V0ZXIgPSB7CiAgcGF0aDogJy9teScsCiAgY29tcG9uZW50OiBfbGF5b3V0LmRlZmF1bHQsCiAgcmVkaXJlY3Q6ICdub1JlZGlyZWN0JywKICBuYW1lOiAnTXknLAogIG1ldGE6IHsKICAgIHRpdGxlOiAn5oiR55qEJywKICAgIGljb246ICd1c2VyJwogIH0sCiAgY2hpbGRyZW46IFt7CiAgICBwYXRoOiAnaW5kZXgnLAogICAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkMi5kZWZhdWx0KShyZXF1aXJlKCdAL3ZpZXdzL215L2luZGV4JykpOwogICAgICB9KTsKICAgIH0sCiAgICBuYW1lOiAnTXlJbmRleCcsCiAgICBtZXRhOiB7CiAgICAgIHRpdGxlOiAn5oiR55qEJywKICAgICAgaWNvbjogJ3VzZXInCiAgICB9CiAgfV0KfTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gbXlSb3V0ZXI7"}, {"version": 3, "names": ["_layout", "_interopRequireDefault", "require", "myRouter", "path", "component", "Layout", "redirect", "name", "meta", "title", "icon", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "_default", "exports"], "sources": ["D:/jgst/dataeyeui/src/router/my.js"], "sourcesContent": ["import Layout from '@/layout'\n\nconst myRouter = {\n  path: '/my',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'My',\n  meta: {\n    title: '我的',\n    icon: 'user'\n  },\n  children: [\n    {\n      path: 'index',\n      component: () => import('@/views/my/index'),\n      name: 'MyIndex',\n      meta: { title: '我的', icon: 'user' }\n    }\n  ]\n}\n\nexport default myRouter\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,QAAQ,GAAG;EACfC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEC,eAAM;EACjBC,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE;IACJC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,CACR;IACER,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAf,OAAA,CAAe,kBAAkB;MAAA;IAAA,CAAC;IAC3CM,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAO;EACpC,CAAC;AAEL,CAAC;AAAA,IAAAO,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAEcd,QAAQ", "ignoreList": []}]}