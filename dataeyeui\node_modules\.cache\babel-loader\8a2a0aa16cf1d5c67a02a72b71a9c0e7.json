{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\bold.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\bold.js", "mtime": 1749172157068}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_inline", "_interopRequireDefault", "require", "Bold", "_Inline", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "optimize", "context", "_superPropGet2", "domNode", "tagName", "statics", "replaceWith", "blotName", "create", "formats", "Inline", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/bold.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Bold extends Inline {\n  static blotName = 'bold';\n  static tagName = ['STRONG', 'B'];\n\n  static create() {\n    return super.create();\n  }\n\n  static formats() {\n    return true;\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    if (this.domNode.tagName !== this.statics.tagName[0]) {\n      this.replaceWith(this.statics.blotName);\n    }\n  }\n}\n\nexport default Bold;\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuC,IAEjCC,IAAI,0BAAAC,OAAA;EAAA,SAAAD,KAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,IAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,IAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,IAAA,EAAAC,OAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,IAAA;IAAAQ,GAAA;IAAAC,KAAA,EAYR,SAAAC,QAAQA,CAACC,OAA+B,EAAE;MACxC,IAAAC,cAAA,CAAAT,OAAA,EAAAH,IAAA,wBAAeW,OAAO;MACtB,IAAI,IAAI,CAACE,OAAO,CAACC,OAAO,KAAK,IAAI,CAACC,OAAO,CAACD,OAAO,CAAC,CAAC,CAAC,EAAE;QACpD,IAAI,CAACE,WAAW,CAAC,IAAI,CAACD,OAAO,CAACE,QAAQ,CAAC;MACzC;IACF;EAAA;IAAAT,GAAA;IAAAC,KAAA,EAbA,SAAOS,MAAMA,CAAA,EAAG;MACd,WAAAN,cAAA,CAAAT,OAAA,EAAAH,IAAA;IACF;EAAA;IAAAQ,GAAA;IAAAC,KAAA,EAEA,SAAOU,OAAOA,CAAA,EAAG;MACf,OAAO,IAAI;IACb;EAAA;AAAA,EAViBC,eAAM;AAAA,IAAAC,gBAAA,CAAAlB,OAAA,EAAnBH,IAAI,cACU,MAAM;AAAA,IAAAqB,gBAAA,CAAAlB,OAAA,EADpBH,IAAI,aAES,CAAC,QAAQ,EAAE,GAAG,CAAC;AAAA,IAAAsB,QAAA,GAAAC,OAAA,CAAApB,OAAA,GAkBnBH,IAAI", "ignoreList": []}]}