<template>
  <div class="app-container">
    <div class="tab-container">
      <div class="tab-item" :class="{ active: activeTab === 'personal' }" @click="activeTab = 'personal'">个人设置</div>
      <div class="tab-item" :class="{ active: activeTab === 'workspace' }" @click="activeTab = 'workspace'">工作区设置</div>
      <div class="tab-item" :class="{ active: activeTab === 'analysis' }" @click="activeTab = 'analysis'">智能解析设置</div>
      <div class="tab-item" :class="{ active: activeTab === 'search' }" @click="activeTab = 'search'">搜索提示设置</div>
    </div>

    <!-- 个人设置 -->
    <div v-if="activeTab === 'personal'" class="form-container">
      <div class="form-item">
        <div class="form-label">昵称：</div>
        <div class="form-input">
          <el-input v-model="personalForm.nickName" placeholder="请输入昵称"></el-input>
        </div>
      </div>
      <div class="form-item">
        <div class="form-label">手机号：</div>
        <div class="form-input">
          <el-input v-model="personalForm.phonenumber" placeholder="请输入手机号"></el-input>
        </div>
      </div>
      <div class="form-item">
        <div class="form-label">邮箱：</div>
        <div class="form-input">
          <el-input v-model="personalForm.email" placeholder="请输入邮箱"></el-input>
        </div>
      </div>
      <div class="form-item">
        <div class="form-label">性别：</div>
        <div class="form-input">
          <el-radio-group v-model="personalForm.sex">
            <el-radio :label="'0'">男</el-radio>
            <el-radio :label="'1'">女</el-radio>
            <el-radio :label="'2'">未知</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="footer">
        <el-button type="primary" class="default-btn" @click="savePersonal">保存</el-button>
      </div>
    </div>

    <!-- 搜索提示设置 -->
    <div v-if="activeTab === 'search'" class="form-container">
      <div class="setting-section">
        <div class="setting-header">
          <div class="setting-title">智能提示</div>
          <el-switch v-model="searchSettings.smartTips" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-desc">根据您的搜索行为切换不同的提示词</div>
      </div>

      <div class="setting-section">
        <div class="setting-header">
          <div class="setting-title">默认字段提示</div>
          <el-switch v-model="searchSettings.trySuggestions" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-desc">任何情况对焦搜索框都给出字段提示，可手动切换到问题提示</div>
      </div>

      <div class="setting-section">
        <div class="setting-header">
          <div class="setting-title">默认问题提示</div>
          <el-switch v-model="searchSettings.relatedSearch" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-desc">任何情况对焦搜索框都给出问题提示，可手动切换到字段提示</div>
      </div>

      <div class="footer">
        <el-button type="primary" class="default-btn" @click="saveSearchSettings">保存设置</el-button>
      </div>
    </div>

    <!-- 其他标签页的内容可以根据需要添加 -->
    <div v-if="activeTab === 'workspace'" class="form-container workspace-setting">
      <div class="workspace-title">预测分析</div>
      <el-radio-group v-model="workspaceSettings.predict" class="workspace-radio-group">
        <el-radio :label="'follow'">跟随工作区设置</el-radio>
        <el-radio :label="'always'">始终打开</el-radio>
        <el-radio :label="'never'">始终关闭</el-radio>
      </el-radio-group>
      <div class="footer">
        <el-button type="primary" class="default-btn" @click="saveWorkspaceSettings">保存设置</el-button>
      </div>
    </div>

    <div v-if="activeTab === 'analysis'" class="form-container">
      <!-- 基础解析部分 -->
      <div class="setting-section">
        <div class="setting-title">基础解析</div>

        <div class="setting-item">
          <div class="setting-item-title">异常值</div>
          <el-switch v-model="analysisSettings.anomaly" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-item-desc" v-if="analysisSettings.anomaly">展示数据中显著高于或低于其他值的点</div>

        <div class="setting-item">
          <div class="setting-item-title">最大值</div>
          <el-switch v-model="analysisSettings.maximum" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>

        <div class="setting-item">
          <div class="setting-item-title">最小值</div>
          <el-switch v-model="analysisSettings.minimum" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>

        <div class="setting-item">
          <div class="setting-item-title">平均值</div>
          <el-switch v-model="analysisSettings.average" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>

        <div class="setting-item">
          <div class="setting-item-title">离散统计</div>
          <el-switch v-model="analysisSettings.discrete" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
      </div>

      <!-- 维度相关解析部分 -->
      <div class="setting-section">
        <div class="setting-title">维度相关解析</div>

        <div class="setting-item">
          <div class="setting-item-title">80/20构成</div>
          <el-switch v-model="analysisSettings.bioTag" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-item-desc" v-if="analysisSettings.bioTag">
          判断数据是否符合二八分布,即这组数据中,最重要的维度只占其中一小部分,约20%,其余80%尽管是多数,却是次要的
        </div>
      </div>

      <!-- 趋势类解析部分 -->
      <div class="setting-section">
        <div class="setting-title">趋势类解析</div>

        <div class="setting-item">
          <div class="setting-item-title">环比异常值</div>
          <el-switch v-model="analysisSettings.chainAnomaly" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-item-desc" v-if="analysisSettings.chainAnomaly">
          展示数据中增速或降幅显著高于其他时间的点
        </div>

        <div class="setting-item">
          <div class="setting-item-title">周期性波动</div>
          <el-switch v-model="analysisSettings.cyclicalFluctuation" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
      </div>

      <div class="footer">
        <el-button type="primary" class="default-btn" @click="saveAnalysisSettings">保存设置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserProfile, updateUserProfile } from '@/api/system/user';
import { getConfigKey, updateConfig } from '@/api/system/config';

export default {
  name: 'MyButton',
  data() {
    return {
      activeTab: 'personal',
      personalForm: {
        nickName: '',
        phonenumber: '',
        email: '',
        sex: '0'
      },
      loading: false,
      // 搜索提示设置
      searchSettings: {
        smartTips: true,
        trySuggestions: true,
        relatedSearch: true
      },
      // 智能解析设置
      analysisSettings: {
        anomaly: true,
        maximum: true,
        minimum: true,
        average: true,
        discrete: true,
        bioTag: true,
        chainAnomaly: true,
        cyclicalFluctuation: true
      },
      // 工作区设置
      workspaceSettings: {
        predict: 'follow'
      }
    }
  },
  watch: {
    userId(val) {
      this.userIdLength = val ? val.length : 0;
    }
  },
  created() {
    this.initPersonal();
    this.initSearchSettings();
    this.initAnalysisSettings();
    this.initWorkspaceSettings();
  },
  methods: {
    // 个人设置初始化
    async initPersonal() {
      this.loading = true;
      try {
        const res = await getUserProfile();
        if (res && res.data) {
          this.personalForm.nickName = res.data.nickName || '';
          this.personalForm.phonenumber = res.data.phonenumber || '';
          this.personalForm.email = res.data.email || '';
          this.personalForm.sex = res.data.sex || '0';
        }
      } catch (e) {
        this.$message.error('获取个人信息失败');
      } finally {
        this.loading = false;
      }
    },
    // 保存个人设置
    async savePersonal() {
      this.loading = true;
      try {
        const { nickName, phonenumber, email, sex } = this.personalForm;
        await updateUserProfile({ nickName, phonenumber, email, sex });
        this.$message.success('个人信息保存成功');
      } catch (e) {
        this.$message.error('保存失败');
      } finally {
        this.loading = false;
      }
    },
    // 搜索提示设置初始化
    async initSearchSettings() {
      try {
        const smartTips = await getConfigKey('search.smartTips');
        const trySuggestions = await getConfigKey('search.trySuggestions');
        const relatedSearch = await getConfigKey('search.relatedSearch');
        this.searchSettings.smartTips = smartTips.msg === 'true';
        this.searchSettings.trySuggestions = trySuggestions.msg === 'true';
        this.searchSettings.relatedSearch = relatedSearch.msg === 'true';
      } catch (e) {
        // 可忽略，首次无配置
      }
    },
    // 保存搜索提示设置
    async saveSearchSettings() {
      this.loading = true;
      try {
        const configs = [
          { key: 'search.smartTips', value: String(this.searchSettings.smartTips) },
          { key: 'search.trySuggestions', value: String(this.searchSettings.trySuggestions) },
          { key: 'search.relatedSearch', value: String(this.searchSettings.relatedSearch) }
        ];
        console.log('即将保存 configs:', configs);
        if (!Array.isArray(configs) || configs.length === 0) {
          this.$message.error('待保存配置项为空');
          this.loading = false;
          return;
        }
        for (const [idx, item] of configs.entries()) {
          console.log(`当前 item[${idx}]:`, item);
          if (!item || typeof item !== 'object') {
            this.$message.error(`第${idx + 1}项配置不是对象`);
            this.loading = false;
            return;
          }
          if (!item.key || typeof item.key !== 'string' || item.key.trim() === '') {
            this.$message.error(`第${idx + 1}项参数键名不能为空`);
            this.loading = false;
            return;
          }
          // 先查完整参数对象
          const res = await getConfigKey(item.key);
          if (!res || !res.data || !res.data.configId) {
            this.$message.error(`第${idx + 1}项参数未找到或无主键`);
            this.loading = false;
            return;
          }
          // 构造完整参数对象
          const updateObj = {
            ...res.data,
            configValue: item.value
          };
          console.log('updateConfig参数:', updateObj);
          await updateConfig(updateObj);
        }
        this.$message.success('搜索提示设置保存成功');
      } catch (e) {
        this.$message.error('保存失败');
      } finally {
        this.loading = false;
      }
    },
    // 智能解析设置初始化
    async initAnalysisSettings() {
      try {
        const keys = ['anomaly','maximum','minimum','average','discrete','bioTag','chainAnomaly','cyclicalFluctuation'];
        for (const key of keys) {
          const res = await getConfigKey('analysis.' + key);
          if (res && res.msg !== undefined) this.analysisSettings[key] = res.msg === 'true';
        }
      } catch (e) {}
    },
    // 保存智能解析设置
    async saveAnalysisSettings() {
      this.loading = true;
      try {
        const keys = Object.keys(this.analysisSettings);
        for (const [idx, key] of keys.entries()) {
          if (!key) {
            this.$message.error('参数键名不能为空');
            this.loading = false;
            return;
          }
          // 先查完整参数对象
          const res = await getConfigKey('analysis.' + key);
          if (!res || !res.data || !res.data.configId) {
            this.$message.error(`第${idx + 1}项参数未找到或无主键`);
            this.loading = false;
            return;
          }
          // 构造完整参数对象
          const updateObj = {
            ...res.data,
            configValue: String(this.analysisSettings[key])
          };
          console.log('updateConfig参数:', updateObj);
          await updateConfig(updateObj);
        }
        this.$message.success('智能解析设置保存成功');
      } catch (e) {
        this.$message.error('保存失败');
      } finally {
        this.loading = false;
      }
    },
    // 工作区设置初始化
    async initWorkspaceSettings() {
      try {
        const res = await getConfigKey('workspace.predict');
        if (res && res.msg) this.workspaceSettings.predict = res.msg;
      } catch (e) {}
    },
    // 保存工作区设置
    async saveWorkspaceSettings() {
      this.loading = true;
      try {
        if (!this.workspaceSettings.predict) {
          this.$message.error('参数键名不能为空');
          this.loading = false;
          return;
        }
        // 先查完整参数对象
        const res = await getConfigKey('workspace.predict');
        if (!res || !res.data || !res.data.configId) {
          this.$message.error('参数未找到或无主键');
          this.loading = false;
          return;
        }
        // 构造完整参数对象
        const updateObj = {
          ...res.data,
          configValue: this.workspaceSettings.predict
        };
        console.log('updateConfig参数:', updateObj);
        await updateConfig(updateObj);
        this.$message.success('工作区设置保存成功');
      } catch (e) {
        this.$message.error('保存失败');
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  background-color: #fff;
  min-height: 100vh;
  padding: 20px;
}

.tab-container {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 20px;
}

.tab-item {
  padding: 10px 15px;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409EFF;
}

.form-container {
  width: 100%;
  max-width: 600px;
}

.form-item {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
}

.form-label {
  width: 80px;
  text-align: right;
  padding-right: 10px;
}

.form-input {
  flex: 1;
}

.form-count {
  width: 60px;
  text-align: right;
  color: #999;
  font-size: 12px;
}

.login-link {
  color: #409EFF;
  text-decoration: none;
}

.footer {
  margin-top: 40px;
  text-align: center;
}

.default-btn {
  padding: 8px 20px;
  border-radius: 4px;
}

/* 搜索提示设置样式 */
.setting-section {
  margin-bottom: 25px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.setting-title {
  font-size: 16px;
  font-weight: bold;
}

.setting-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.placeholder {
  color: #999;
  font-size: 16px;
  padding: 20px 0;
}

/* 智能解析设置样式 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.setting-item-title {
  font-size: 14px;
}

.setting-item-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  margin-left: 10px;
}

/* 工作区设置样式 */
.workspace-setting {
  max-width: 600px;
  margin: 0;
  padding-top: 30px;
}

.workspace-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 24px;
  color: #333;
}

.workspace-radio-group {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-left: 0;
}

.workspace-radio-group .el-radio {
  font-size: 16px;
  color: #222;
  margin-bottom: 8px;
}
</style>
