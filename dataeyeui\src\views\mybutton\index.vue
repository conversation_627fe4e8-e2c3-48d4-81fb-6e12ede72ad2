<template>
  <div class="app-container">
    <div class="tab-container">
      <div class="tab-item" :class="{ active: activeTab === 'personal' }" @click="activeTab = 'personal'">个人设置</div>
      <div class="tab-item" :class="{ active: activeTab === 'workspace' }" @click="activeTab = 'workspace'">工作区设置</div>
      <div class="tab-item" :class="{ active: activeTab === 'analysis' }" @click="activeTab = 'analysis'">智能解析设置</div>
      <div class="tab-item" :class="{ active: activeTab === 'search' }" @click="activeTab = 'search'">搜索提示设置</div>
    </div>

    <!-- 加载状态 -->
    <el-skeleton v-if="loading" :rows="6" animated />

    <!-- 个人设置 -->
    <div v-if="activeTab === 'personal' && !loading" class="form-container">
      <div class="form-item">
        <div class="form-label">用户ID：</div>
        <div class="form-input">
          <el-input v-model="userId" placeholder="请输入用户ID"></el-input>
        </div>
        <div class="form-count">{{ userIdLength }} / 20</div>
      </div>

      <div class="form-item">
        <div class="form-label">密码：</div>
        <div class="form-input">
          <el-input v-model="password" type="password" placeholder="请输入密码" show-password></el-input>
        </div>
      </div>

      <div class="form-item">
        <div class="form-label"></div>
        <div class="form-input">
          <a href="javascript:;" class="login-link">返回登录 ></a>
        </div>
      </div>

      <div class="footer">
        <el-button type="primary" class="default-btn" @click="resetToDefault" :loading="loading">设为默认</el-button>
      </div>
    </div>

    <!-- 搜索提示设置 -->
    <div v-if="activeTab === 'search' && !loading" class="form-container">
      <div class="setting-section">
        <div class="setting-header">
          <div class="setting-title">智能提示</div>
          <el-switch v-model="searchSettings.smartTips" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-desc">根据您的搜索行为切换不同的提示词</div>
      </div>

      <div class="setting-section">
        <div class="setting-header">
          <div class="setting-title">默认字段提示</div>
          <el-switch v-model="searchSettings.trySuggestions" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-desc">任何情况对焦搜索框都给出字段提示，可手动切换到问题提示</div>
      </div>

      <div class="setting-section">
        <div class="setting-header">
          <div class="setting-title">默认问题提示</div>
          <el-switch v-model="searchSettings.relatedSearch" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-desc">任何情况对焦搜索框都给出问题提示，可手动切换到字段提示</div>
      </div>

      <div class="footer">
        <el-button type="primary" class="default-btn" @click="saveSearchSettings" :loading="loading">保存设置</el-button>
      </div>
    </div>

    <!-- 其他标签页的内容可以根据需要添加 -->
    <div v-if="activeTab === 'workspace' && !loading" class="form-container">
      <div class="setting-container">
        <div v-if="activeTab === 'workspace'" class="workspace-setting-content">
          <div class="setting-label">预测分析</div>
          <el-radio-group v-model="predictMode">
            <el-radio :label="'follow'">跟随工作区设置</el-radio>
            <el-radio :label="'alwaysOn'">始终打开</el-radio>
            <el-radio :label="'alwaysOff'">始终关闭</el-radio>
          </el-radio-group>
        </div>
        <el-button class="save-btn" type="primary" @click="saveSetting">保存设置</el-button>
      </div>
    </div>

    <div v-if="activeTab === 'analysis' && !loading" class="form-container">
      <!-- 基础解析部分 -->
      <div class="setting-section">
        <div class="setting-title">基础解析</div>

        <div class="setting-item">
          <div class="setting-item-title">异常值</div>
          <el-switch v-model="analysisSettings.anomaly" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-item-desc" v-if="analysisSettings.anomaly">展示数据中显著高于或低于其他值的点</div>

        <div class="setting-item">
          <div class="setting-item-title">最大值</div>
          <el-switch v-model="analysisSettings.maximum" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>

        <div class="setting-item">
          <div class="setting-item-title">最小值</div>
          <el-switch v-model="analysisSettings.minimum" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>

        <div class="setting-item">
          <div class="setting-item-title">平均值</div>
          <el-switch v-model="analysisSettings.average" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>

        <div class="setting-item">
          <div class="setting-item-title">离散统计</div>
          <el-switch v-model="analysisSettings.discrete" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
      </div>

      <!-- 维度相关解析部分 -->
      <div class="setting-section">
        <div class="setting-title">维度相关解析</div>

        <div class="setting-item">
          <div class="setting-item-title">80/20构成</div>
          <el-switch v-model="analysisSettings.bioTag" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-item-desc" v-if="analysisSettings.bioTag">
          判断数据是否符合二八分布,即这组数据中,最重要的维度只占其中一小部分,约20%,其余80%尽管是多数,却是次要的
        </div>
      </div>

      <!-- 趋势类解析部分 -->
      <div class="setting-section">
        <div class="setting-title">趋势类解析</div>

        <div class="setting-item">
          <div class="setting-item-title">环比异常值</div>
          <el-switch v-model="analysisSettings.chainAnomaly" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
        <div class="setting-item-desc" v-if="analysisSettings.chainAnomaly">
          展示数据中增速或降幅显著高于其他时间的点
        </div>

        <div class="setting-item">
          <div class="setting-item-title">周期性波动</div>
          <el-switch v-model="analysisSettings.cyclicalFluctuation" active-color="#409EFF" inactive-color="#DCDFE6"></el-switch>
        </div>
      </div>

      <div class="footer">
        <el-button type="primary" class="default-btn" @click="saveAnalysisSettings" :loading="loading">保存设置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserSettings, saveUserSettings } from '@/api/user_settings'

export default {
  name: 'MyButton',
  data() {
    return {
      activeTab: 'personal', // 默认显示个人设置标签
      userId: '',
      password: '********',
      userIdLength: 0,
      currentUserId: 1, // 当前登录用户ID，实际应该从登录状态获取
      loading: false,
      searchSettings: {
        smartTips: true,
        trySuggestions: true,
        relatedSearch: true
      },
      analysisSettings: {
        anomaly: true,
        maximum: true,
        minimum: true,
        average: true,
        discrete: true,
        bioTag: true,
        chainAnomaly: true,
        cyclicalFluctuation: true
      },
      predictMode: 'follow',
    }
  },
  watch: {
    userId(val) {
      this.userIdLength = val.length;
    }
  },
  created() {
    this.fetchUserSettings()
  },
  methods: {
    async fetchUserSettings() {
      this.loading = true
      try {
        const response = await getUserSettings(this.currentUserId)
        if (response.code === 200) {
          const { user, searchSettings, analysisSettings } = response.data
          // 更新用户信息
          this.userId = user.userId || user.nickName || ''
          this.userIdLength = this.userId.length
          // 更新设置
          this.searchSettings = { ...this.searchSettings, ...searchSettings }
          this.analysisSettings = { ...this.analysisSettings, ...analysisSettings }
        }
      } catch (error) {
        this.$message.error('获取用户设置失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    async saveSearchSettings() {
      this.loading = true
      try {
        const response = await saveUserSettings(this.currentUserId, {
          searchSettings: this.searchSettings
        })
        if (response.code === 200) {
          this.$message({
            message: '搜索提示设置保存成功',
            type: 'success'
          });
        }
      } catch (error) {
        this.$message.error('保存搜索设置失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    async saveAnalysisSettings() {
      this.loading = true
      try {
        const response = await saveUserSettings(this.currentUserId, {
          analysisSettings: this.analysisSettings
        })
        if (response.code === 200) {
          this.$message({
            message: '智能解析设置保存成功',
            type: 'success'
          });
        }
      } catch (error) {
        this.$message.error('保存分析设置失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    saveSetting() {
      this.$message.success('设置已保存')
    },
    async resetToDefault() {
      this.$confirm('确定要重置为默认设置吗？这将覆盖您当前的所有个性化设置。', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        try {
          // 重置为默认设置
          const defaultSearchSettings = {
            smartTips: true,
            trySuggestions: true,
            relatedSearch: true
          }
          const defaultAnalysisSettings = {
            anomaly: true,
            maximum: true,
            minimum: true,
            average: true,
            discrete: true,
            bioTag: true,
            chainAnomaly: true,
            cyclicalFluctuation: true
          }

          // 保存默认设置到数据库
          const response = await saveUserSettings(this.currentUserId, {
            searchSettings: defaultSearchSettings,
            analysisSettings: defaultAnalysisSettings
          })

          if (response.code === 200) {
            // 更新本地数据
            this.searchSettings = { ...defaultSearchSettings }
            this.analysisSettings = { ...defaultAnalysisSettings }

            this.$message({
              message: '已重置为默认设置',
              type: 'success'
            })
          }
        } catch (error) {
          this.$message.error('重置设置失败：' + (error.message || '未知错误'))
        } finally {
          this.loading = false
        }
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  background-color: #fff;
  min-height: 100vh;
  padding: 20px;
}

.tab-container {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 20px;
}

.tab-item {
  padding: 10px 15px;
  cursor: pointer;
  margin-right: 20px;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409EFF;
}

.form-container {
  width: 100%;
  max-width: 600px;
}

.form-item {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
}

.form-label {
  width: 80px;
  text-align: right;
  padding-right: 10px;
}

.form-input {
  flex: 1;
}

.form-count {
  width: 60px;
  text-align: right;
  color: #999;
  font-size: 12px;
}

.login-link {
  color: #409EFF;
  text-decoration: none;
}

.footer {
  margin-top: 40px;
  text-align: center;
}

.default-btn {
  padding: 8px 20px;
  border-radius: 4px;
}

/* 搜索提示设置样式 */
.setting-section {
  margin-bottom: 25px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.setting-title {
  font-size: 16px;
  font-weight: bold;
}

.setting-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.placeholder {
  color: #999;
  font-size: 16px;
  padding: 20px 0;
}

/* 智能解析设置样式 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.setting-item-title {
  font-size: 14px;
}

.setting-item-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  margin-left: 10px;
}

/* 工作区设置样式 */
.setting-container {
  min-height: 100vh;
  background: #fff;
  padding: 40px 0 0 0;
  position: relative;
}

.setting-tabs {
  margin-left: 60px;
}

.workspace-setting-content {
  margin-left: 60px;
  margin-top: 32px;
}

.setting-label {
  font-size: 16px;
  margin-bottom: 18px;
  font-weight: 400;
}

.el-radio-group {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
}

.el-radio {
  margin-bottom: 12px;
  font-size: 15px;
}

.save-btn {
  position: fixed;
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 40px;
  border-radius: 20px;
  font-size: 16px;
}
</style>
