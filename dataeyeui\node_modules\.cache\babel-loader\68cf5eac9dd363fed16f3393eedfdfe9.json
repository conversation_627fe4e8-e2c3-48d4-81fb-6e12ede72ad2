{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\components\\ImagePreview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\components\\ImagePreview\\index.vue", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_validate", "require", "name", "props", "src", "type", "String", "default", "width", "Number", "height", "computed", "realSrc", "real_src", "split", "isExternal", "process", "env", "VUE_APP_BASE_API", "realSrcList", "real_src_list", "srcList", "for<PERSON>ach", "item", "push", "realWidth", "concat", "realHeight"], "sources": ["src/components/ImagePreview/index.vue"], "sourcesContent": ["<template>\n  <el-image\n    :src=\"`${realSrc}`\"\n    fit=\"cover\"\n    :style=\"`width:${realWidth};height:${realHeight};`\"\n    :preview-src-list=\"realSrcList\"\n  >\n    <div slot=\"error\" class=\"image-slot\">\n      <i class=\"el-icon-picture-outline\"></i>\n    </div>\n  </el-image>\n</template>\n\n<script>\nimport { isExternal } from \"@/utils/validate\";\n\nexport default {\n  name: \"ImagePreview\",\n  props: {\n    src: {\n      type: String,\n      default: \"\"\n    },\n    width: {\n      type: [Number, String],\n      default: \"\"\n    },\n    height: {\n      type: [Number, String],\n      default: \"\"\n    }\n  },\n  computed: {\n    realSrc() {\n      if (!this.src) {\n        return;\n      }\n      let real_src = this.src.split(\",\")[0];\n      if (isExternal(real_src)) {\n        return real_src;\n      }\n      return process.env.VUE_APP_BASE_API + real_src;\n    },\n    realSrcList() {\n      if (!this.src) {\n        return;\n      }\n      let real_src_list = this.src.split(\",\");\n      let srcList = [];\n      real_src_list.forEach(item => {\n        if (isExternal(item)) {\n          return srcList.push(item);\n        }\n        return srcList.push(process.env.VUE_APP_BASE_API + item);\n      });\n      return srcList;\n    },\n    realWidth() {\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`;\n    },\n    realHeight() {\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`;\n    }\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.el-image {\n  border-radius: 5px;\n  background-color: #ebeef5;\n  box-shadow: 0 0 5px 1px #ccc;\n  ::v-deep .el-image__inner {\n    transition: all 0.3s;\n    cursor: pointer;\n    &:hover {\n      transform: scale(1.2);\n    }\n  }\n  ::v-deep .image-slot {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n    color: #909399;\n    font-size: 30px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAcA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,GAAAI,MAAA,EAAAH,MAAA;MACAC,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,GAAAI,MAAA,EAAAH,MAAA;MACAC,OAAA;IACA;EACA;EACAI,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,UAAAR,GAAA;QACA;MACA;MACA,IAAAS,QAAA,QAAAT,GAAA,CAAAU,KAAA;MACA,QAAAC,oBAAA,EAAAF,QAAA;QACA,OAAAA,QAAA;MACA;MACA,OAAAG,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAL,QAAA;IACA;IACAM,WAAA,WAAAA,YAAA;MACA,UAAAf,GAAA;QACA;MACA;MACA,IAAAgB,aAAA,QAAAhB,GAAA,CAAAU,KAAA;MACA,IAAAO,OAAA;MACAD,aAAA,CAAAE,OAAA,WAAAC,IAAA;QACA,QAAAR,oBAAA,EAAAQ,IAAA;UACA,OAAAF,OAAA,CAAAG,IAAA,CAAAD,IAAA;QACA;QACA,OAAAF,OAAA,CAAAG,IAAA,CAAAR,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAK,IAAA;MACA;MACA,OAAAF,OAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,mBAAAjB,KAAA,oBAAAA,KAAA,MAAAkB,MAAA,MAAAlB,KAAA;IACA;IACAmB,UAAA,WAAAA,WAAA;MACA,mBAAAjB,MAAA,oBAAAA,MAAA,MAAAgB,MAAA,MAAAhB,MAAA;IACA;EACA;AACA", "ignoreList": []}]}