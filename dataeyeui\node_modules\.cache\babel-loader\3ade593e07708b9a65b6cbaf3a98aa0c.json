{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\api\\user_settings.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\api\\user_settings.js", "mtime": 1750044490005}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0VXNlclNldHRpbmdzID0gZ2V0VXNlclNldHRpbmdzOwpleHBvcnRzLnNhdmVVc2VyU2V0dGluZ3MgPSBzYXZlVXNlclNldHRpbmdzOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g55So5oi36K6+572u55u45YWzQVBJCgovLyDojrflj5bnlKjmiLforr7nva4KZnVuY3Rpb24gZ2V0VXNlclNldHRpbmdzKHVzZXJJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL2FwaS91c2VyX3NldHRpbmdzLyIuY29uY2F0KHVzZXJJZCksCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOS/neWtmOeUqOaIt+iuvue9rgpmdW5jdGlvbiBzYXZlVXNlclNldHRpbmdzKHVzZXJJZCwgZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL2FwaS91c2VyX3NldHRpbmdzLyIuY29uY2F0KHVzZXJJZCksCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getUserSettings", "userId", "request", "url", "concat", "method", "saveUserSettings", "data"], "sources": ["D:/jgst/dataeyeui/src/api/user_settings.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 用户设置相关API\n\n// 获取用户设置\nexport function getUserSettings(userId) {\n  return request({\n    url: `/api/user_settings/${userId}`,\n    method: 'get'\n  })\n}\n\n// 保存用户设置\nexport function saveUserSettings(userId, data) {\n  return request({\n    url: `/api/user_settings/${userId}`,\n    method: 'post',\n    data\n  })\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;;AAEA;AACO,SAASC,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,wBAAAC,MAAA,CAAwBH,MAAM,CAAE;IACnCI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACL,MAAM,EAAEM,IAAI,EAAE;EAC7C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,wBAAAC,MAAA,CAAwBH,MAAM,CAAE;IACnCI,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}