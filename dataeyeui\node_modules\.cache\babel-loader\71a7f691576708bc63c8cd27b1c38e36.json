{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\link.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\node_modules\\quill\\formats\\link.js", "mtime": 1749172158707}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_inline", "_interopRequireDefault", "require", "Link", "exports", "default", "_Inline", "_classCallCheck2", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "format", "name", "statics", "blotName", "_superPropGet2", "domNode", "setAttribute", "constructor", "sanitize", "create", "node", "formats", "getAttribute", "url", "PROTOCOL_WHITELIST", "SANITIZED_URL", "Inline", "_defineProperty2", "protocols", "anchor", "document", "createElement", "href", "protocol", "slice", "indexOf"], "sources": ["../../src/formats/link.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Link extends Inline {\n  static blotName = 'link';\n  static tagName = 'A';\n  static SANITIZED_URL = 'about:blank';\n  static PROTOCOL_WHITELIST = ['http', 'https', 'mailto', 'tel', 'sms'];\n\n  static create(value: string) {\n    const node = super.create(value) as HTMLElement;\n    node.setAttribute('href', this.sanitize(value));\n    node.setAttribute('rel', 'noopener noreferrer');\n    node.setAttribute('target', '_blank');\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    return domNode.getAttribute('href');\n  }\n\n  static sanitize(url: string) {\n    return sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;\n  }\n\n  format(name: string, value: unknown) {\n    if (name !== this.statics.blotName || !value) {\n      super.format(name, value);\n    } else {\n      // @ts-expect-error\n      this.domNode.setAttribute('href', this.constructor.sanitize(value));\n    }\n  }\n}\n\nfunction sanitize(url: string, protocols: string[]) {\n  const anchor = document.createElement('a');\n  anchor.href = url;\n  const protocol = anchor.href.slice(0, anchor.href.indexOf(':'));\n  return protocols.indexOf(protocol) > -1;\n}\n\nexport { Link as default, sanitize };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuC,IAEjCC,IAAI,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAAA,SAAAH,KAAA;IAAA,IAAAI,gBAAA,CAAAF,OAAA,QAAAF,IAAA;IAAA,WAAAK,WAAA,CAAAH,OAAA,QAAAF,IAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAL,OAAA,EAAAF,IAAA,EAAAG,OAAA;EAAA,WAAAK,aAAA,CAAAN,OAAA,EAAAF,IAAA;IAAAS,GAAA;IAAAC,KAAA,EAsBR,SAAAC,MAAMA,CAACC,IAAY,EAAEF,KAAc,EAAE;MACnC,IAAIE,IAAI,KAAK,IAAI,CAACC,OAAO,CAACC,QAAQ,IAAI,CAACJ,KAAK,EAAE;QAC5C,IAAAK,cAAA,CAAAb,OAAA,EAAAF,IAAA,sBAAaY,IAAI,EAAEF,KAAK;MAC1B,CAAC,MAAM;QACL;QACA,IAAI,CAACM,OAAO,CAACC,YAAY,CAAC,MAAM,EAAE,IAAI,CAACC,WAAW,CAACC,QAAQ,CAACT,KAAK,CAAC,CAAC;MACrE;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAvBA,SAAOU,MAAMA,CAACV,KAAa,EAAE;MAC3B,IAAMW,IAAI,OAAAN,cAAA,CAAAb,OAAA,EAAAF,IAAA,sBAAgBU,KAAK,EAAgB;MAC/CW,IAAI,CAACJ,YAAY,CAAC,MAAM,EAAE,IAAI,CAACE,QAAQ,CAACT,KAAK,CAAC,CAAC;MAC/CW,IAAI,CAACJ,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC;MAC/CI,IAAI,CAACJ,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACrC,OAAOI,IAAI;IACb;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAEA,SAAOY,OAAOA,CAACN,OAAoB,EAAE;MACnC,OAAOA,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC;IACrC;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAOS,QAAQA,CAACK,GAAW,EAAE;MAC3B,OAAOL,SAAQ,CAACK,GAAG,EAAE,IAAI,CAACC,kBAAkB,CAAC,GAAGD,GAAG,GAAG,IAAI,CAACE,aAAa;IAC1E;EAAA;AAAA,EApBiBC,eAAM;AAAA,IAAAC,gBAAA,CAAA1B,OAAA,EAAnBF,IAAI,cACU,MAAM;AAAA,IAAA4B,gBAAA,CAAA1B,OAAA,EADpBF,IAAI,aAES,GAAG;AAAA,IAAA4B,gBAAA,CAAA1B,OAAA,EAFhBF,IAAI,mBAGe,aAAa;AAAA,IAAA4B,gBAAA,CAAA1B,OAAA,EAHhCF,IAAI,wBAIoB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;AA4BvE,SAASmB,SAAQA,CAACK,GAAW,EAAEK,SAAmB,EAAE;EAClD,IAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EAC1CF,MAAM,CAACG,IAAI,GAAGT,GAAG;EACjB,IAAMU,QAAQ,GAAGJ,MAAM,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC,EAAEL,MAAM,CAACG,IAAI,CAACG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC/D,OAAOP,SAAS,CAACO,OAAO,CAACF,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzC", "ignoreList": []}]}