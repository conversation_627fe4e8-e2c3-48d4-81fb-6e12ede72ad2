{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\jgst\\dataeyeui\\src\\views\\my\\index.vue?vue&type=style&index=0&id=7a3d5949&scoped=true&lang=css", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\views\\my\\index.vue", "mtime": 1750042731010}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749172155435}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749172157758}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749172156224}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749172156914}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgSA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/my", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"my-container\">\n      <!-- 左侧菜单 -->\n      <div class=\"left-menu\">\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'address' }\" @click=\"activeMenu = 'address'\">\n          <i class=\"el-icon-data-analysis\"></i>\n          <span>指标告警({{ menuCounts.address }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'f2code' }\" @click=\"activeMenu = 'f2code'\">\n          <i class=\"el-icon-document\"></i>\n          <span>卡片提醒({{ menuCounts.f2code }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'questions' }\" @click=\"activeMenu = 'questions'\">\n          <i class=\"el-icon-question\"></i>\n          <span>关注的问题({{ menuCounts.questions }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'edit' }\" @click=\"activeMenu = 'edit'\">\n          <i class=\"el-icon-star-off\"></i>\n          <span>保存的卡片({{ menuCounts.edit }})</span>\n        </div>\n        <div class=\"menu-item\" :class=\"{ active: activeMenu === 'tags' }\" @click=\"activeMenu = 'tags'\">\n          <i class=\"el-icon-time\"></i>\n          <span>搜索历史({{ menuCounts.tags }})</span>\n        </div>\n      </div>\n\n      <!-- 右侧内容区 -->\n      <div class=\"content-area\">\n        <el-skeleton v-if=\"loading\" rows=\"6\" animated />\n        <el-alert v-if=\"error\" :title=\"error\" type=\"error\" show-icon style=\"margin-bottom:16px\" />\n\n        <!-- 指标告警内容 -->\n        <template v-if=\"activeMenu === 'address' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">指标告警</div>\n          </div>\n          <el-table :data=\"alertList\" style=\"width:100%\" border v-if=\"alertList.length\">\n            <el-table-column prop=\"name\" label=\"告警名称\" min-width=\"180\" />\n            <el-table-column prop=\"level\" label=\"级别\" width=\"100\" />\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeAlertRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无数据</div></div>\n        </template>\n\n        <!-- 卡片提醒内容 -->\n        <template v-if=\"activeMenu === 'f2code' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">卡片记录</div>\n          </div>\n          <el-table :data=\"filteredCardShowList\" style=\"width:100%\" border v-if=\"filteredCardShowList.length\">\n            <el-table-column prop=\"name\" label=\"监控卡片\" min-width=\"180\" />\n            <el-table-column prop=\"createTime\" label=\"触发条件\" width=\"180\" />\n            <el-table-column prop=\"updateTime\" label=\"计算频率\" width=\"180\" />\n            <el-table-column prop=\"creator\" label=\"提醒接受人\" width=\"120\" />\n            <el-table-column prop=\"lastViewTime\" label=\"最近触发时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeCardRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无数据</div></div>\n        </template>\n\n        <!-- 关注的问题内容 -->\n        <template v-if=\"activeMenu === 'questions' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">关注的问题</div>\n            <div class=\"search-box\">\n              <el-input placeholder=\"在关注的问题中查找\" prefix-icon=\"el-icon-search\" v-model=\"questionSearchKeyword\" clearable />\n            </div>\n          </div>\n          <el-table :data=\"filteredQuestionList\" style=\"width:100%\" border v-if=\"filteredQuestionList.length\">\n            <el-table-column prop=\"question\" label=\"问题\" min-width=\"200\" />\n            <el-table-column prop=\"starTime\" label=\"关注时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeQuestionRow(scope.row)\">取消关注</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无数据</div></div>\n        </template>\n\n        <!-- 保存的卡片内容 -->\n        <template v-if=\"activeMenu === 'edit' && !loading && !error\">\n          <div class=\"saved-cards-header\">\n            <div class=\"title\">保存的卡片</div>\n            <div class=\"search-box\">\n              <el-input placeholder=\"在保存的卡片中查找\" prefix-icon=\"el-icon-search\" v-model=\"cardSearchKeyword\" clearable />\n            </div>\n          </div>\n          <el-table :data=\"filteredCardList\" style=\"width:100%\" border v-if=\"filteredCardList.length\">\n            <el-table-column prop=\"name\" label=\"卡片名称\" min-width=\"180\" />\n            <el-table-column prop=\"createTime\" label=\"保存时间\" width=\"180\" />\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\">查看</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"removeCardRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂未保存任何卡片</div></div>\n        </template>\n\n        <!-- 搜索历史内容 -->\n        <template v-if=\"activeMenu === 'tags' && !loading && !error\">\n          <div class=\"search-history-header-new\">\n            <div class=\"search-history-title\">搜索历史 <span class=\"desc\">保留最近1个月的记录</span></div>\n            <el-input class=\"search-history-input\" placeholder=\"在搜索历史中检索\" prefix-icon=\"el-icon-search\" v-model=\"searchKeyword\" clearable />\n          </div>\n          <el-table :data=\"filteredHistoryList\" class=\"search-history-table\" style=\"width:100%\" border v-if=\"filteredHistoryList.length\">\n            <el-table-column label=\"全部\" min-width=\"70%\">\n              <template slot-scope=\"scope\">\n                <div class=\"history-item\">{{ scope.row.content }}</div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"修改时间\" align=\"right\" min-width=\"30%\">\n              <template slot-scope=\"scope\">\n                <div class=\"history-time\">{{ scope.row.time }}</div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\" @click=\"removeHistoryRow(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <div v-else class=\"empty-content\"><div class=\"empty-text\">暂无历史记录</div></div>\n        </template>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCardList, removeCard } from '@/api/card'\nimport { getAlertList, removeAlert } from '@/api/alert'\nimport { getQuestionStarList, removeQuestionStar } from '@/api/question'\nimport { getHistoryList, removeHistory } from '@/api/history'\n\nexport default {\n  name: 'MyCenter',\n  data() {\n    return {\n      activeMenu: 'address',\n      searchKeyword: '',\n      cardSearchKeyword: '',\n      questionSearchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      // 数据区\n      cardList: [],\n      cardShowList: [],\n      alertList: [],\n      questionList: [],\n      historyList: [],\n      // 加载与异常状态\n      loading: false,\n      error: null\n    }\n  },\n  computed: {\n    menuCounts() {\n      return {\n        address: this.alertList.length,\n        f2code: this.cardShowList.length,\n        questions: this.questionList.length,\n        edit: this.cardList.length,\n        tags: this.historyList.length\n      }\n    },\n    filteredCardList() {\n      if (!this.cardSearchKeyword) return this.cardList\n      return this.cardList.filter(item => (item.name || '').includes(this.cardSearchKeyword))\n    },\n    filteredCardShowList() {\n      if (!this.cardSearchKeyword) return this.cardShowList\n      return this.cardShowList.filter(item => (item.name || '').includes(this.cardSearchKeyword))\n    },\n    filteredQuestionList() {\n      if (!this.questionSearchKeyword) return this.questionList\n      return this.questionList.filter(item => (item.question || '').includes(this.questionSearchKeyword))\n    },\n    filteredHistoryList() {\n      if (!this.searchKeyword) return this.historyList\n      return this.historyList.filter(item => (item.content || '').includes(this.searchKeyword))\n    }\n  },\n  created() {\n    this.fetchAllData()\n  },\n  methods: {\n    async fetchAllData() {\n      this.loading = true\n      this.error = null\n      try {\n        // 指标告警\n        const alertRes = await getAlertList({ pageNum: 1, pageSize: 100 })\n        this.alertList = alertRes.items || alertRes.rows || []\n        // 卡片提醒/保存的卡片\n        // 此处cardList即为数据库cards表的数据\n        const cardRes = await getCardList({ pageNum: 1, pageSize: 100 })\n        this.cardList = cardRes.items || cardRes.rows || []\n        this.cardShowList = this.cardList // 视图分离后可做筛选\n        // 关注的问题\n        const questionRes = await getQuestionStarList({ pageNum: 1, pageSize: 100 })\n        this.questionList = questionRes.items || questionRes.rows || []\n        // 搜索历史\n        const historyRes = await getHistoryList({ pageNum: 1, pageSize: 100 })\n        this.historyList = historyRes.items || historyRes.rows || []\n      } catch (e) {\n        this.error = e.message || '数据加载失败'\n      } finally {\n        this.loading = false\n      }\n    },\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.fetchAllData()\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.fetchAllData()\n    },\n    async removeCardRow(row) {\n      this.loading = true\n      try {\n        await removeCard(row.id || row.cardId)\n        this.$message.success('删除成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '删除失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async removeAlertRow(row) {\n      this.loading = true\n      try {\n        await removeAlert(row.id || row.alertId)\n        this.$message.success('删除成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '删除失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async removeQuestionRow(row) {\n      this.loading = true\n      try {\n        await removeQuestionStar(row.id || row.starId)\n        this.$message.success('取消关注成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '操作失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async removeHistoryRow(row) {\n      this.loading = true\n      try {\n        await removeHistory(row.id || row.historyId)\n        this.$message.success('删除成功')\n        this.fetchAllData()\n      } catch (e) {\n        this.$message.error(e.message || '删除失败')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.my-container {\n  display: flex;\n  background-color: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  min-height: calc(100vh - 40px);\n}\n\n/* 左侧菜单样式 */\n.left-menu {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  padding: 20px 0;\n}\n\n.menu-item {\n  padding: 12px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  color: #606266;\n  transition: all 0.3s;\n}\n\n.menu-item:hover {\n  background-color: #f5f7fa;\n}\n\n.menu-item.active {\n  color: #409EFF;\n  background-color: #ecf5ff;\n}\n\n.menu-item i {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n/* 右侧内容区样式 */\n.content-area {\n  flex: 1;\n  padding: 20px;\n  position: relative;\n}\n\n.header {\n  margin-bottom: 20px;\n}\n\n.add-button {\n  border-radius: 20px;\n  padding: 8px 20px;\n}\n\n.empty-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  color: #909399;\n}\n\n.empty-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n}\n\n.empty-icon i {\n  font-size: 24px;\n  color: #909399;\n}\n\n.empty-text {\n  font-size: 14px;\n}\n\n/* 搜索历史样式 */\n.search-history-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.username {\n  font-size: 16px;\n  font-weight: bold;\n  margin-right: 5px;\n}\n\n.user-desc {\n  font-size: 14px;\n  color: #909399;\n}\n\n.search-box {\n  width: 300px;\n}\n\n.search-history-list {\n  margin-bottom: 20px;\n}\n\n.history-item {\n  font-size: 14px;\n  color: #303133;\n  line-height: 1.5;\n  padding: 10px 0;\n}\n\n.history-time {\n  font-size: 14px;\n  color: #909399;\n  padding: 10px 0;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 20px;\n}\n\n/* 保存的卡片样式 */\n.saved-cards-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.card-table-container {\n  position: relative;\n  min-height: 300px;\n}\n\n.empty-table {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  text-align: center;\n  color: #909399;\n  font-size: 14px;\n  padding: 20px 0;\n}\n\n.link-text {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.link-text:hover {\n  text-decoration: underline;\n}\n\n/* 新搜索历史样式 */\n.search-history-header-new {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.search-history-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.desc {\n  font-size: 14px;\n  color: #909399;\n  margin-left: 5px;\n}\n\n.search-history-input {\n  width: 300px;\n}\n\n.search-history-table {\n  margin-top: 10px;\n}\n\n.search-history-table .el-table__header {\n  background-color: #f9f9f9;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.search-history-table .el-table__header th {\n  font-size: 14px;\n  color: #606266;\n  padding: 12px 0;\n  text-align: left;\n}\n\n.search-history-table .el-table__body td {\n  font-size: 14px;\n  color: #303133;\n  padding: 10px 0;\n}\n\n.search-history-table .el-table__body tr:hover {\n  background-color: #f5f7fa;\n}\n</style>\n"]}]}