-- 热门问题表
CREATE TABLE top_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '问题ID',
    question TEXT NOT NULL COMMENT '问题内容',
    search_count INT DEFAULT 0 COMMENT '搜索次数',
    category VARCHAR(50) COMMENT '问题分类',
    tags VARCHAR(200) COMMENT '标签(JSON格式)',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_search_count (search_count),
    INDEX idx_category (category),
    INDEX idx_sort_order (sort_order)
) COMMENT='热门问题表';

-- 插入示例数据
INSERT INTO top_questions (question, search_count, category, sort_order) VALUES
('门店营业额前十', 150, '销售分析', 1),
('今日汇率与昨日汇率', 120, '金融数据', 2),
('上海房产价格走势', 100, '房地产', 3),
('产品销售量统计', 95, '销售分析', 4),
('最近的股票市场行情', 90, '金融数据', 5),
('用户活跃度分析', 85, '用户分析', 6),
('月度收入报表', 80, '财务分析', 7),
('客户满意度调查', 75, '客户分析', 8);