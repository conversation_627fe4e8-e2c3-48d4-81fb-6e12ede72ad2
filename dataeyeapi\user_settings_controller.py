from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_
from config.get_db import get_db
from user_settings_model import UserSetting
from module_admin.entity.do.user_do import SysUser
import json
import datetime

router = APIRouter()

@router.get("/api/user_settings/{user_id}")
async def get_user_settings(user_id: int, db: AsyncSession = Depends(get_db)):
    """获取用户设置"""
    try:
        # 获取用户基本信息
        user_stmt = select(SysUser).where(SysUser.user_id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 获取用户设置
        settings_stmt = select(UserSetting).where(UserSetting.user_id == user_id)
        settings_result = await db.execute(settings_stmt)
        settings = settings_result.scalars().all()
        
        # 组织设置数据
        search_settings = {}
        analysis_settings = {}
        
        for setting in settings:
            value = setting.setting_value
            # 转换字符串布尔值
            if value in ['true', 'false']:
                value = value == 'true'
            
            if setting.setting_type == 'search_settings':
                # 转换键名以匹配前端
                key_mapping = {
                    'smart_tips': 'smartTips',
                    'try_suggestions': 'trySuggestions', 
                    'related_search': 'relatedSearch'
                }
                frontend_key = key_mapping.get(setting.setting_key, setting.setting_key)
                search_settings[frontend_key] = value
            elif setting.setting_type == 'analysis_settings':
                # 转换键名以匹配前端
                key_mapping = {
                    'bio_tag': 'bioTag',
                    'chain_anomaly': 'chainAnomaly',
                    'cyclical_fluctuation': 'cyclicalFluctuation'
                }
                frontend_key = key_mapping.get(setting.setting_key, setting.setting_key)
                analysis_settings[frontend_key] = value
        
        # 设置默认值
        default_search_settings = {
            'smartTips': True,
            'trySuggestions': True,
            'relatedSearch': True
        }
        default_analysis_settings = {
            'anomaly': True,
            'maximum': True,
            'minimum': True,
            'average': True,
            'discrete': True,
            'bioTag': True,
            'chainAnomaly': True,
            'cyclicalFluctuation': True
        }
        
        # 合并默认值
        for key, default_value in default_search_settings.items():
            if key not in search_settings:
                search_settings[key] = default_value
                
        for key, default_value in default_analysis_settings.items():
            if key not in analysis_settings:
                analysis_settings[key] = default_value
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "user": {
                    "userId": user.user_name,
                    "nickName": user.nick_name,
                    "email": user.email,
                    "phone": user.phonenumber
                },
                "searchSettings": search_settings,
                "analysisSettings": analysis_settings
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户设置失败: {str(e)}")

@router.post("/api/user_settings/{user_id}")
async def save_user_settings(
    user_id: int,
    settings_data: dict = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """保存用户设置"""
    try:
        # 验证用户存在
        user_stmt = select(SysUser).where(SysUser.user_id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 处理搜索设置
        if 'searchSettings' in settings_data:
            search_settings = settings_data['searchSettings']
            # 键名映射（前端到后端）
            key_mapping = {
                'smartTips': 'smart_tips',
                'trySuggestions': 'try_suggestions',
                'relatedSearch': 'related_search'
            }
            
            for frontend_key, value in search_settings.items():
                backend_key = key_mapping.get(frontend_key, frontend_key)
                await upsert_setting(db, user_id, 'search_settings', backend_key, str(value).lower())
        
        # 处理智能解析设置
        if 'analysisSettings' in settings_data:
            analysis_settings = settings_data['analysisSettings']
            # 键名映射（前端到后端）
            key_mapping = {
                'bioTag': 'bio_tag',
                'chainAnomaly': 'chain_anomaly',
                'cyclicalFluctuation': 'cyclical_fluctuation'
            }
            
            for frontend_key, value in analysis_settings.items():
                backend_key = key_mapping.get(frontend_key, frontend_key)
                await upsert_setting(db, user_id, 'analysis_settings', backend_key, str(value).lower())
        
        await db.commit()
        
        return {
            "code": 200,
            "message": "设置保存成功",
            "data": None
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"保存用户设置失败: {str(e)}")

async def upsert_setting(db: AsyncSession, user_id: int, setting_type: str, setting_key: str, setting_value: str):
    """插入或更新设置"""
    # 查找现有设置
    stmt = select(UserSetting).where(
        and_(
            UserSetting.user_id == user_id,
            UserSetting.setting_type == setting_type,
            UserSetting.setting_key == setting_key
        )
    )
    result = await db.execute(stmt)
    existing_setting = result.scalar_one_or_none()
    
    if existing_setting:
        # 更新现有设置
        existing_setting.setting_value = setting_value
        existing_setting.updated_at = datetime.datetime.utcnow()
    else:
        # 创建新设置
        new_setting = UserSetting(
            user_id=user_id,
            setting_type=setting_type,
            setting_key=setting_key,
            setting_value=setting_value
        )
        db.add(new_setting)
