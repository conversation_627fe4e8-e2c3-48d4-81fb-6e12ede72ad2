#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class TagInfo(BaseModel):
    """标签信息"""
    id: int
    name: str
    color: Optional[str] = None

class UserInfo(BaseModel):
    """用户信息"""
    user_id: int
    user_name: str
    nick_name: Optional[str] = None
    avatar: Optional[str] = None

class PinboardItem(BaseModel):
    """Pinboard项目信息"""
    uid: str = Field(..., description="Pinboard唯一标识")
    name: str = Field(..., description="Pinboard名称")
    description: Optional[str] = Field(None, description="Pinboard描述")
    owner_id: int = Field(..., description="创建者ID")
    owner_name: str = Field(..., description="创建者名称")
    owner_avatar: Optional[str] = Field(None, description="创建者头像")
    last_editor: Optional[str] = Field(None, description="最后编辑者")
    last_editor_avatar: Optional[str] = Field(None, description="最后编辑者头像")
    is_template: bool = Field(False, description="是否为模板")
    is_shared: bool = Field(False, description="是否共享")
    share_type: str = Field("none", description="共享类型")
    is_new: bool = Field(False, description="是否为新建")
    tags: List[TagInfo] = Field(default_factory=list, description="标签列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    update_time: str = Field(..., description="格式化的更新时间")

class PinboardListData(BaseModel):
    """Pinboard列表数据"""
    items: List[PinboardItem] = Field(..., description="Pinboard列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")

class PinboardListResponse(BaseModel):
    """Pinboard列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: PinboardListData = Field(..., description="响应数据")

class PinboardCreateRequest(BaseModel):
    """创建Pinboard请求"""
    name: str = Field(..., min_length=1, max_length=100, description="Pinboard名称")
    description: Optional[str] = Field(None, max_length=500, description="Pinboard描述")
    project_id: int = Field(..., description="所属项目ID")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    is_template: bool = Field(False, description="是否为模板")

class PinboardResponse(BaseModel):
    """Pinboard响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: PinboardItem = Field(..., description="Pinboard数据")

class TagItem(BaseModel):
    """标签项目"""
    id: int = Field(..., description="标签ID")
    name: str = Field(..., description="标签名称")
    color: Optional[str] = Field(None, description="标签颜色")
    report_count: int = Field(0, description="关联报告数")
    modify_type: str = Field("手动", description="修改方式")

class TagListResponse(BaseModel):
    """标签列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: List[TagItem] = Field(..., description="标签列表")

# 协作报告相关Schema
class CollaborateReportItem(BaseModel):
    """协作报告项"""
    uid: str = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    description: Optional[str] = Field(None, description="报告描述")
    author: str = Field(..., description="作者")
    avatar: Optional[str] = Field(None, description="作者头像")
    last_update: str = Field(..., description="最后更新时间")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    collaborators: int = Field(0, description="协作者数量")
    is_new: bool = Field(False, description="是否为新报告")

class CollaborateReportListResponse(BaseModel):
    """协作报告列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="协作报告数据")

# 共享报告相关Schema
class SharedReportItem(BaseModel):
    """共享报告项"""
    uid: str = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    description: Optional[str] = Field(None, description="报告描述")
    share_url: Optional[str] = Field(None, description="分享链接")
    access_password: Optional[str] = Field(None, description="访问密码")
    expire_time: Optional[str] = Field(None, description="过期时间")
    view_count: int = Field(0, description="访问次数")
    share_time: str = Field(..., description="分享时间")
    permissions: List[str] = Field(default_factory=list, description="权限列表")

class SharedReportListResponse(BaseModel):
    """共享报告列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="共享报告数据")

# 关注报告相关Schema
class FollowedReportItem(BaseModel):
    """关注报告项"""
    uid: str = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    description: Optional[str] = Field(None, description="报告描述")
    author: str = Field(..., description="作者")
    avatar: Optional[str] = Field(None, description="作者头像")
    follow_time: str = Field(..., description="关注时间")
    last_update: str = Field(..., description="最后更新时间")
    category: str = Field(..., description="分类")
    has_update: bool = Field(False, description="是否有更新")

class FollowedReportListResponse(BaseModel):
    """关注报告列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="关注报告数据")

# 定时提醒相关Schema
class ReminderItem(BaseModel):
    """提醒项"""
    id: int = Field(..., description="提醒ID")
    name: str = Field(..., description="提醒名称")
    description: Optional[str] = Field(None, description="提醒描述")
    pinboard_uid: str = Field(..., description="关联报告ID")
    report_title: str = Field(..., description="报告标题")
    frequency: str = Field(..., description="提醒频率")
    execute_time: str = Field(..., description="执行时间")
    next_run: str = Field(..., description="下次执行时间")
    recipients: List[str] = Field(default_factory=list, description="接收人列表")
    push_methods: List[str] = Field(default_factory=list, description="推送方式")
    enabled: bool = Field(True, description="是否启用")
    create_time: str = Field(..., description="创建时间")

class ReminderCreateRequest(BaseModel):
    """创建提醒请求"""
    name: str = Field(..., min_length=1, max_length=100, description="提醒名称")
    description: Optional[str] = Field(None, description="提醒描述")
    pinboard_uid: str = Field(..., min_length=1, description="关联报告ID")
    frequency: str = Field(..., description="提醒频率")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    execute_time: str = Field(..., description="执行时间")
    recipients: List[str] = Field(default_factory=list, min_items=1, description="接收人列表")
    push_methods: List[str] = Field(default_factory=list, min_items=1, description="推送方式")
    expire_date: Optional[str] = Field(None, description="过期日期")

class ReminderListResponse(BaseModel):
    """提醒列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="提醒数据")

# 回收站相关Schema
class TrashReportItem(BaseModel):
    """回收站报告项"""
    uid: str = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    description: Optional[str] = Field(None, description="报告描述")
    original_path: str = Field(..., description="原路径")
    delete_time: str = Field(..., description="删除时间")
    delete_by: str = Field(..., description="删除人")
    size: str = Field(..., description="文件大小")
    remaining_days: int = Field(..., description="剩余天数")

class TrashReportListResponse(BaseModel):
    """回收站报告列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="回收站数据")

class ProjectInfo(BaseModel):
    """项目信息"""
    id: int
    name: str
    desc: Optional[str] = None
    owner_id: int

class TemplateItem(BaseModel):
    """模板项目"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    is_new: bool = Field(False, description="是否为新模板")
    preview_image: str = Field(..., description="预览图片")
    category: str = Field(..., description="模板分类")

class TemplateListResponse(BaseModel):
    """模板列表响应"""
    code: int = Field(200, description="响应码")
    message: str = Field("success", description="响应消息")
    data: List[TemplateItem] = Field(..., description="模板列表")
