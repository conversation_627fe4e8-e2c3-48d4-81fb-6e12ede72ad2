{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\api\\menu.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\api\\menu.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9qZ3N0L2RhdGFleWV1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0Um91dGVycyA9IHZvaWQgMDsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOiOt+WPlui3r+eUsQp2YXIgZ2V0Um91dGVycyA9IGV4cG9ydHMuZ2V0Um91dGVycyA9IGZ1bmN0aW9uIGdldFJvdXRlcnMoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZ2V0Um91dGVycycsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getRouters", "exports", "request", "url", "method"], "sources": ["D:/jgst/dataeyeui/src/api/menu.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取路由\nexport const getRouters = () => {\n  return request({\n    url: '/getRouters',\n    method: 'get'\n  })\n}"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,IAAMC,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAbA,UAAUA,CAAA,EAAS;EAC9B,OAAO,IAAAE,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}]}