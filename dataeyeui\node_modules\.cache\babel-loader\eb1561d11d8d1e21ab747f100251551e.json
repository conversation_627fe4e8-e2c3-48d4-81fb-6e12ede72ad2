{"remainingRequest": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js!D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\jgst\\dataeyeui\\src\\utils\\dict\\DictMeta.js", "dependencies": [{"path": "D:\\jgst\\dataeyeui\\src\\utils\\dict\\DictMeta.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\babel.config.js", "mtime": 1746725230000}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749172154827}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749172156229}, {"path": "D:\\jgst\\dataeyeui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749172155224}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_DictOptions", "_interopRequireDefault", "DictMeta", "exports", "default", "_createClass2", "options", "_classCallCheck2", "type", "request", "responseConverter", "labelField", "valueField", "lazy", "parse", "opts", "DictOptions", "metas", "_typeof2", "mergeRecursive"], "sources": ["D:/jgst/dataeyeui/src/utils/dict/DictMeta.js"], "sourcesContent": ["import { mergeRecursive } from \"@/utils/ruoyi\";\nimport DictOptions from './DictOptions'\n\n/**\n * @classdesc 字典元数据\n * @property {String} type 类型\n * @property {Function} request 请求\n * @property {String} label 标签字段\n * @property {String} value 值字段\n */\nexport default class DictMeta {\n  constructor(options) {\n    this.type = options.type\n    this.request = options.request\n    this.responseConverter = options.responseConverter\n    this.labelField = options.labelField\n    this.valueField = options.valueField\n    this.lazy = options.lazy === true\n  }\n}\n\n\n/**\n * 解析字典元数据\n * @param {Object} options\n * @returns {DictMeta}\n */\nDictMeta.parse= function(options) {\n  let opts = null\n  if (typeof options === 'string') {\n    opts = DictOptions.metas[options] || {}\n    opts.type = options\n  } else if (typeof options === 'object') {\n    opts = options\n  }\n  opts = mergeRecursive(DictOptions.metas['*'], opts)\n  return new DictMeta(opts)\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOqBG,QAAQ,GAAAC,OAAA,CAAAC,OAAA,oBAAAC,aAAA,CAAAD,OAAA,EAC3B,SAAAF,SAAYI,OAAO,EAAE;EAAA,IAAAC,gBAAA,CAAAH,OAAA,QAAAF,QAAA;EACnB,IAAI,CAACM,IAAI,GAAGF,OAAO,CAACE,IAAI;EACxB,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACG,OAAO;EAC9B,IAAI,CAACC,iBAAiB,GAAGJ,OAAO,CAACI,iBAAiB;EAClD,IAAI,CAACC,UAAU,GAAGL,OAAO,CAACK,UAAU;EACpC,IAAI,CAACC,UAAU,GAAGN,OAAO,CAACM,UAAU;EACpC,IAAI,CAACC,IAAI,GAAGP,OAAO,CAACO,IAAI,KAAK,IAAI;AACnC,CAAC;AAIH;AACA;AACA;AACA;AACA;AACAX,QAAQ,CAACY,KAAK,GAAE,UAASR,OAAO,EAAE;EAChC,IAAIS,IAAI,GAAG,IAAI;EACf,IAAI,OAAOT,OAAO,KAAK,QAAQ,EAAE;IAC/BS,IAAI,GAAGC,oBAAW,CAACC,KAAK,CAACX,OAAO,CAAC,IAAI,CAAC,CAAC;IACvCS,IAAI,CAACP,IAAI,GAAGF,OAAO;EACrB,CAAC,MAAM,IAAI,IAAAY,QAAA,CAAAd,OAAA,EAAOE,OAAO,MAAK,QAAQ,EAAE;IACtCS,IAAI,GAAGT,OAAO;EAChB;EACAS,IAAI,GAAG,IAAAI,qBAAc,EAACH,oBAAW,CAACC,KAAK,CAAC,GAAG,CAAC,EAAEF,IAAI,CAAC;EACnD,OAAO,IAAIb,QAAQ,CAACa,IAAI,CAAC;AAC3B,CAAC", "ignoreList": []}]}